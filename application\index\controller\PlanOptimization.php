<?php
namespace app\index\controller;
use app\index\controller\Acl;
use app\index\model\ProductionOrder;
use app\index\model\ProductionSchedule;
use app\index\model\InjectionMachine;
use app\index\model\Shift;

class PlanOptimization extends Acl{
    
    //主页面
    public function main(){
        return $this->fetch();
    }
    
    //获取优化建议
    public function get_optimization_suggestions(){
        $input = input('post.');
        $start_date = isset($input['start_date']) ? $input['start_date'] : date('Y-m-d');
        $end_date = isset($input['end_date']) ? $input['end_date'] : date('Y-m-d', strtotime('+7 days'));
        
        // 获取待排产订单（放宽查询条件）
        try {
            $pending_orders = db('production_order')
                ->alias('po')
                ->join('goods g', 'po.goods_id = g.id', 'left')
                ->where('po.merchant', Session('is_merchant_id'))
                ->where('po.status', 'in', [0, 1]) // 包括待排产和已排产状态
                ->field('po.*, g.name as goods_name')
                ->order('po.priority asc, po.plan_start_date asc')
                ->select()
                ->toArray();
        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '查询待排产订单失败: ' . $e->getMessage()]);
        }

        // 获取现有排产计划（放宽查询条件）
        try {
            $existing_schedules = db('production_schedule')
                ->alias('ps')
                ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
                ->join('work_shift ws', 'ps.shift_id = ws.id', 'left')
                ->join('production_order po', 'ps.production_order_id = po.id', 'left')
                ->where('ps.merchant', Session('is_merchant_id'))
                ->field('ps.*, im.name as machine_name, ws.name as shift_name, po.order_no, po.priority')
                ->select()
                ->toArray();
        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '查询现有排产计划失败: ' . $e->getMessage()]);
        }
        
        // 生成优化建议
        $suggestions = $this->generateOptimizationSuggestions($pending_orders, $existing_schedules, $start_date, $end_date);
        
        return json(['state' => 'success', 'data' => $suggestions]);
    }
    
    //应用优化方案
    public function apply_optimization(){
        $input = input('post.');
        if(!isset($input['optimization_plan']) || !is_array($input['optimization_plan'])){
            return json(['state' => 'error', 'info' => '优化方案数据不完整']);
        }
        
        $success_count = 0;
        $error_messages = [];
        
        foreach($input['optimization_plan'] as $plan){
            try{
                // 创建新的排产计划
                $schedule_data = [
                    'merchant' => Session('is_merchant_id'),
                    'order_id' => $plan['order_id'],
                    'machine_id' => $plan['machine_id'],
                    'shift_id' => $plan['shift_id'],
                    'schedule_date' => $plan['schedule_date'],
                    'plan_start_time' => $plan['plan_start_time'],
                    'plan_end_time' => $plan['plan_end_time'],
                    'plan_qty' => $plan['plan_qty'],
                    'status' => 0,
                    'creator' => Session('is_user_id'),
                    'createtime' => time()
                ];
                
                ProductionSchedule::create($schedule_data);
                
                // 更新订单状态
                db('production_order')->where('id', $plan['order_id'])->update(['status' => 1]);
                
                $success_count++;
            } catch(\Exception $e){
                $error_messages[] = '订单ID ' . $plan['order_id'] . ' 排产失败：' . $e->getMessage();
            }
        }
        
        if($success_count > 0){
            $message = '成功应用' . $success_count . '个优化方案';
            if(!empty($error_messages)){
                $message .= '，但有' . count($error_messages) . '个方案应用失败';
            }
            push_log('应用计划优化方案，成功' . $success_count . '个');
            return json(['state' => 'success', 'info' => $message]);
        } else {
            return json(['state' => 'error', 'info' => '没有方案被成功应用：' . implode('；', $error_messages)]);
        }
    }
    
    //获取资源利用率分析
    public function get_resource_utilization(){
        $input = input('post.');
        $start_date = isset($input['start_date']) ? $input['start_date'] : date('Y-m-d');
        $end_date = isset($input['end_date']) ? $input['end_date'] : date('Y-m-d', strtotime('+7 days'));
        
        // 获取所有设备
        try {
            $machines = db('injection_machine')
                ->where(['merchant' => Session('is_merchant_id'), 'status' => 1])
                ->select();
        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '查询设备信息失败: ' . $e->getMessage()]);
        }

        // 获取所有班次
        try {
            $shifts = db('work_shift')
                ->where(['merchant' => Session('is_merchant_id')])
                ->select();
        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '查询班次信息失败: ' . $e->getMessage()]);
        }
        
        // 计算资源利用率
        $utilization_data = [];
        
        foreach($machines as $machine){
            $machine_utilization = [
                'machine_id' => $machine['id'],
                'machine_name' => $machine['name'],
                'shifts' => []
            ];

            foreach($shifts as $shift){
                // 计算该设备该班次的利用率
                try {
                    $scheduled_hours = db('production_schedule')
                        ->where([
                            'merchant' => Session('is_merchant_id'),
                            'machine_id' => $machine['id'],
                            'shift_id' => $shift['id']
                        ])
                        ->where('schedule_date', 'between', [$start_date, $end_date])
                        ->sum('TIMESTAMPDIFF(HOUR, plan_start_time, plan_end_time)');
                } catch(\Exception $e) {
                    $scheduled_hours = 0;
                }
                
                $total_available_hours = $this->calculateAvailableHours($shift, $start_date, $end_date);
                $utilization_rate = $total_available_hours > 0 ? round($scheduled_hours / $total_available_hours * 100, 2) : 0;
                
                $machine_utilization['shifts'][] = [
                    'shift_id' => $shift['id'],
                    'shift_name' => $shift['name'],
                    'scheduled_hours' => $scheduled_hours,
                    'available_hours' => $total_available_hours,
                    'utilization_rate' => $utilization_rate
                ];
            }
            
            $utilization_data[] = $machine_utilization;
        }
        
        return json(['state' => 'success', 'data' => $utilization_data]);
    }
    
    //获取负载均衡建议
    public function get_load_balancing_suggestions(){
        $input = input('post.');
        $date = isset($input['date']) ? $input['date'] : date('Y-m-d');
        
        // 获取当日排产情况
        try {
            $schedules = db('production_schedule')
                ->alias('ps')
                ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
                ->join('work_shift ws', 'ps.shift_id = ws.id', 'left')
                ->where('ps.merchant', Session('is_merchant_id'))
                ->where('ps.schedule_date', $date)
                ->field('ps.*, im.name as machine_name, ws.name as shift_name')
                ->select();
        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '查询排产情况失败: ' . $e->getMessage()]);
        }

        // 按设备分组计算负载
        $machine_loads = [];
        foreach($schedules as $schedule){
            $machine_id = $schedule['machine_id'];
            if(!isset($machine_loads[$machine_id])){
                $machine_loads[$machine_id] = [
                    'machine_name' => $schedule['machine_name'] ?: '未知设备',
                    'total_hours' => 0,
                    'schedules' => []
                ];
            }

            $hours = $this->calculateHoursBetween($schedule['plan_start_time'], $schedule['plan_end_time']);
            $machine_loads[$machine_id]['total_hours'] += $hours;
            $machine_loads[$machine_id]['schedules'][] = $schedule;
        }
        
        // 生成负载均衡建议
        $suggestions = $this->generateLoadBalancingSuggestions($machine_loads);
        
        return json(['state' => 'success', 'data' => $suggestions]);
    }
    
    //生成优化建议
    private function generateOptimizationSuggestions($pending_orders, $existing_schedules, $start_date, $end_date){
        $suggestions = [
            'summary' => [
                'pending_orders_count' => count($pending_orders),
                'existing_schedules_count' => count($existing_schedules),
                'optimization_potential' => 0
            ],
            'recommendations' => []
        ];

        // 基础建议：如果有待排产订单
        if(count($pending_orders) > 0){
            $unscheduled_orders = [];
            foreach($pending_orders as $order){
                if($order['status'] == 0){ // 待排产状态
                    $unscheduled_orders[] = $order;
                }
            }

            if(count($unscheduled_orders) > 0){
                $suggestions['recommendations'][] = [
                    'type' => 'unscheduled_orders',
                    'title' => '待排产订单处理',
                    'description' => '发现' . count($unscheduled_orders) . '个待排产订单，建议尽快安排生产',
                    'details' => array_map(function($order){
                        return '订单 ' . $order['order_no'] . ' (' . $order['goods_name'] . ') - 计划数量: ' . $order['plan_qty'];
                    }, $unscheduled_orders),
                    'impact' => 'high'
                ];
            }
        }

        // 分析优先级冲突
        $priority_conflicts = $this->analyzePriorityConflicts($pending_orders, $existing_schedules);
        if(!empty($priority_conflicts)){
            $suggestions['recommendations'][] = [
                'type' => 'priority_conflict',
                'title' => '优先级冲突处理',
                'description' => '发现' . count($priority_conflicts) . '个优先级冲突，建议调整排产顺序',
                'details' => $priority_conflicts,
                'impact' => 'high'
            ];
        }
        
        // 分析资源空闲时段
        $idle_periods = $this->analyzeIdlePeriods($existing_schedules, $start_date, $end_date);
        if(!empty($idle_periods)){
            $suggestions['recommendations'][] = [
                'type' => 'idle_optimization',
                'title' => '空闲时段利用',
                'description' => '发现' . count($idle_periods) . '个可利用的空闲时段',
                'details' => $idle_periods,
                'impact' => 'medium'
            ];
        }

        // 分析设备负载不均
        $load_imbalance = $this->analyzeLoadImbalance($existing_schedules);
        if(!empty($load_imbalance)){
            $suggestions['recommendations'][] = [
                'type' => 'load_balancing',
                'title' => '负载均衡优化',
                'description' => '设备负载不均，建议重新分配任务',
                'details' => $load_imbalance,
                'impact' => 'medium'
            ];
        }

        // 如果没有任何建议，提供通用建议
        if(empty($suggestions['recommendations'])){
            $suggestions['recommendations'][] = [
                'type' => 'general',
                'title' => '系统运行良好',
                'description' => '当前排产计划运行良好，暂无需要优化的项目',
                'details' => [
                    '所有订单已合理安排',
                    '设备利用率均衡',
                    '无明显的优先级冲突'
                ],
                'impact' => 'low'
            ];
        }
        
        $suggestions['summary']['optimization_potential'] = count($suggestions['recommendations']);
        
        return $suggestions;
    }
    
    //分析优先级冲突
    private function analyzePriorityConflicts($pending_orders, $existing_schedules){
        $conflicts = [];
        
        foreach($pending_orders as $order){
            // 将优先级文本转换为数字进行比较
            $order_priority = $this->getPriorityValue($order['priority']);

            if($order_priority <= 2){ // 高优先级订单
                // 检查是否有低优先级订单占用了更早的时间段
                foreach($existing_schedules as $schedule){
                    // 获取排产对应的订单信息
                    $schedule_order = db('production_order')
                        ->where('id', $schedule['production_order_id'])
                        ->find();

                    if($schedule_order){
                        $schedule_priority = $this->getPriorityValue($schedule_order['priority']);

                        if($schedule_priority > $order_priority){
                            if($schedule['schedule_date'] <= $order['plan_start_date']){
                                $conflicts[] = [
                                    'high_priority_order' => $order['order_no'],
                                    'low_priority_schedule' => $schedule_order['order_no'],
                                    'conflict_date' => $schedule['schedule_date'],
                                    'suggestion' => '建议将高优先级订单' . $order['order_no'] . '提前排产'
                                ];
                            }
                        }
                    }
                }
            }
        }
        
        return $conflicts;
    }
    
    //分析空闲时段
    private function analyzeIdlePeriods($existing_schedules, $start_date, $end_date){
        $idle_periods = [];
        
        // 获取所有设备和班次
        try {
            $machines = db('injection_machine')
                ->where(['merchant' => Session('is_merchant_id'), 'status' => 1])
                ->select()
                ->toArray();
            $shifts = db('work_shift')
                ->where(['merchant' => Session('is_merchant_id')])
                ->select()
                ->toArray();
        } catch(\Exception $e) {
            return [];
        }
        
        $current_date = $start_date;
        while($current_date <= $end_date){
            foreach($machines as $machine){
                foreach($shifts as $shift){
                    // 检查该设备该班次该日期是否有排产
                    $has_schedule = false;
                    foreach($existing_schedules as $schedule){
                        if($schedule['machine_id'] == $machine['id'] &&
                           $schedule['shift_id'] == $shift['id'] &&
                           $schedule['schedule_date'] == $current_date){
                            $has_schedule = true;
                            break;
                        }
                    }

                    if(!$has_schedule){
                        // 计算班次时长
                        $shift_duration = 8; // 默认8小时
                        if(isset($shift['start_time']) && isset($shift['end_time'])){
                            $start_time = strtotime($shift['start_time']);
                            $end_time = strtotime($shift['end_time']);
                            $shift_duration = ($end_time - $start_time) / 3600;
                        }

                        $idle_periods[] = [
                            'machine_id' => $machine['id'],
                            'machine_name' => $machine['name'],
                            'shift_id' => $shift['id'],
                            'shift_name' => $shift['name'],
                            'date' => $current_date,
                            'available_hours' => $shift_duration
                        ];
                    }
                }
            }
            
            $current_date = date('Y-m-d', strtotime($current_date . ' +1 day'));
        }
        
        return $idle_periods;
    }
    
    //分析负载不均
    private function analyzeLoadImbalance($existing_schedules){
        $machine_loads = [];
        
        foreach($existing_schedules as $schedule){
            $machine_id = $schedule['machine_id'];
            if(!isset($machine_loads[$machine_id])){
                $machine_loads[$machine_id] = [
                    'machine_name' => $schedule['machine_name'] ?: '未知设备',
                    'total_hours' => 0
                ];
            }

            $hours = $this->calculateHoursBetween($schedule['plan_start_time'], $schedule['plan_end_time']);
            $machine_loads[$machine_id]['total_hours'] += $hours;
        }
        
        if(count($machine_loads) < 2) return [];
        
        $loads = array_column($machine_loads, 'total_hours');
        $avg_load = array_sum($loads) / count($loads);
        $max_load = max($loads);
        $min_load = min($loads);
        
        // 如果负载差异超过30%，认为存在不均衡
        if(($max_load - $min_load) / $avg_load > 0.3){
            return [
                'avg_load' => round($avg_load, 2),
                'max_load' => $max_load,
                'min_load' => $min_load,
                'imbalance_ratio' => round(($max_load - $min_load) / $avg_load * 100, 2),
                'suggestion' => '建议将高负载设备的部分任务转移到低负载设备'
            ];
        }
        
        return [];
    }
    
    //生成负载均衡建议
    private function generateLoadBalancingSuggestions($machine_loads){
        $suggestions = [];
        
        if(count($machine_loads) < 2) return $suggestions;
        
        $loads = [];
        foreach($machine_loads as $machine_id => $data){
            $loads[$machine_id] = $data['total_hours'];
        }
        
        $avg_load = array_sum($loads) / count($loads);
        
        foreach($machine_loads as $machine_id => $data){
            if($data['total_hours'] > $avg_load * 1.2){ // 超载20%
                $suggestions[] = [
                    'type' => 'overload',
                    'machine_id' => $machine_id,
                    'machine_name' => $data['machine_name'],
                    'current_load' => $data['total_hours'],
                    'avg_load' => round($avg_load, 2),
                    'overload_ratio' => round(($data['total_hours'] - $avg_load) / $avg_load * 100, 2),
                    'suggestion' => '建议转移部分任务到其他设备'
                ];
            } elseif($data['total_hours'] < $avg_load * 0.8){ // 低载20%
                $suggestions[] = [
                    'type' => 'underload',
                    'machine_id' => $machine_id,
                    'machine_name' => $data['machine_name'],
                    'current_load' => $data['total_hours'],
                    'avg_load' => round($avg_load, 2),
                    'underload_ratio' => round(($avg_load - $data['total_hours']) / $avg_load * 100, 2),
                    'suggestion' => '可以承接更多生产任务'
                ];
            }
        }
        
        return $suggestions;
    }
    
    //计算可用工时
    private function calculateAvailableHours($shift, $start_date, $end_date){
        $start_timestamp = strtotime($start_date);
        $end_timestamp = strtotime($end_date);
        $days = ceil(($end_timestamp - $start_timestamp) / 86400) + 1;

        // 计算班次时长（小时）
        $shift_duration = 8; // 默认8小时
        if(isset($shift['start_time']) && isset($shift['end_time'])){
            $start_time = strtotime($shift['start_time']);
            $end_time = strtotime($shift['end_time']);
            $shift_duration = ($end_time - $start_time) / 3600;
        } elseif(isset($shift['duration'])){
            $shift_duration = $shift['duration'];
        }

        return $days * $shift_duration;
    }
    
    //计算时间差（小时）
    private function calculateHoursBetween($start_time, $end_time){
        $start = strtotime($start_time);
        $end = strtotime($end_time);
        return ($end - $start) / 3600;
    }

    //导出优化报告
    public function export_report(){
        $input = input('get.');
        // 这里可以实现导出功能
        return json(['state' => 'success', 'info' => '导出功能待实现']);
    }

    //获取优先级数值
    private function getPriorityValue($priority){
        $priority_map = [
            '紧急' => 1,
            '高' => 2,
            '普通' => 3,
            '低' => 4
        ];

        return isset($priority_map[$priority]) ? $priority_map[$priority] : 3;
    }
}
