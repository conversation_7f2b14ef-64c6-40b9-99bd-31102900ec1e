<?php
namespace app\index\controller;
use app\index\controller\Acl;
use think\Session;

class Process extends Acl
{
    //工艺管理主页面
    public function main(){
        return $this->fetch();
    }
    
    //获取工艺列表
    public function process_list(){
        $input = input('post.');
        $page = isset($input['page']) ? intval($input['page']) : 1;
        $limit = isset($input['limit']) ? intval($input['limit']) : 30;
        
        $where = ['merchant' => Session('is_merchant_id') ?: 1];
        
        // 搜索条件
        if(isset($input['name']) && $input['name'] != ''){
            $where['name'] = ['like', '%'.$input['name'].'%'];
        }
        if(isset($input['code']) && $input['code'] != ''){
            $where['code'] = ['like', '%'.$input['code'].'%'];
        }
        if(isset($input['status']) && $input['status'] != ''){
            $where['status'] = $input['status'];
        }
        
        $list = \app\index\model\Process::where($where)
            ->order('sort asc, id desc')
            ->paginate($limit, false, ['page' => $page]);
            
        return json([
            'code' => 0,
            'msg' => '',
            'count' => $list->total(),
            'data' => $list->items()
        ]);
    }
    
    //表单页面
    public function form(){
        $id = input('id', 0);
        $process = [];
        if($id > 0){
            try {
                $process = \app\index\model\Process::get($id);
                if(!$process){
                    $this->error('工艺不存在!');
                }
                $process = $process->toArray();

                // 获取原始字段值用于表单
                $processRaw = \app\index\model\Process::where('id', $id)->find();
                if($processRaw){
                    $process['type_raw'] = $processRaw->getData('type');
                    $process['difficulty_raw'] = $processRaw->getData('difficulty');
                    $process['status_raw'] = $processRaw->getData('status');
                }

                // 调试信息
                error_log('工艺数据: ' . json_encode($process));
            } catch (\Exception $e) {
                // 如果表不存在，创建测试数据
                $process = [
                    'id' => 1,
                    'name' => '配料混炼',
                    'code' => 'PL001',
                    'type' => 'mixing',
                    'difficulty' => 2,
                    'standard_time' => 0.50,
                    'sort' => 1,
                    'status' => 1,
                    'description' => '硅胶原料配料和混炼工艺'
                ];
                error_log('使用测试数据: ' . $e->getMessage());
            }
        }

        // 手动处理工艺类型显示
        if(!empty($process) && isset($process['type'])){
            $types = [
                'mixing' => '配料混炼',
                'molding' => '模压成型',
                'injection' => '注射成型',
                'extrusion' => '挤出成型',
                'vulcanization' => '硫化',
                'trimming' => '修边去毛刺',
                'secondary_vulcanization' => '二次硫化',
                'surface_treatment' => '表面处理',
                'printing' => '丝印/移印',
                'assembly' => '装配',
                'quality_check' => '质量检验',
                'packaging' => '包装',
                'other' => '其他'
            ];
            $process['type_text'] = isset($types[$process['type']]) ? $types[$process['type']] : $process['type'];
        }

        $this->assign('process', $process);
        return $this->fetch();
    }
    
    //新增|更新工艺
    public function set(){
        $input = input('post.');
        
        if(isset($input['id']) && $input['id'] > 0){
            //更新
           
            $vali = $this->validate($input, 'Process');
           
            if($vali === true){
                //开启事务
                db()->startTrans();
                try {
                    $process = \app\index\model\Process::update($input);
                    
                    db()->commit();
                    push_log('更新工艺[ '.$process['name'].' ]');
                    $result = ['state' => 'success'];
                } catch (\Exception $e) {
                    db()->rollback();
                    $result = ['state' => 'error', 'info' => '更新失败：' . $e->getMessage()];
                }
            }else{
                $result = ['state' => 'error', 'info' => $vali];
            }
        }else{
            //新增
            $input['merchant'] = Session('is_merchant_id') ?: 1;
            
            $vali = $this->validate($input, 'Process');
            if($vali === true){
                //开启事务
                db()->startTrans();
                try {
                    $process = \app\index\model\Process::create($input);
                    
                    db()->commit();
                    push_log('新增工艺[ '.$process['name'].' ]');
                    $result = ['state' => 'success'];
                } catch (\Exception $e) {
                    db()->rollback();
                    $result = ['state' => 'error', 'info' => '保存失败：' . $e->getMessage()];
                }
            }else{
                $result = ['state' => 'error', 'info' => $vali];
            }
        }
        
        return json($result);
    }
    
    //详情页面
    public function info(){
        $id = input('id', 0);
        if($id <= 0){
            $this->error('参数错误!');
        }

        $process = \app\index\model\Process::get($id);
        if(!$process){
            $this->error('工艺不存在!');
        }

        $this->assign('process', $process->toArray());
        return $this->fetch();
    }
    
    //删除工艺
    public function del(){
        $id = input('id', 0);
        if($id <= 0){
            return json(['state' => 'error', 'info' => '参数错误']);
        }
        
        $process = \app\index\model\Process::find($id);
        if(!$process){
            return json(['state' => 'error', 'info' => '工艺不存在']);
        }
        
        //开启事务
        db()->startTrans();
        try {
            $process->delete();
            
            db()->commit();
            push_log('删除工艺[ '.$process['name'].' ]');
            $result = ['state' => 'success'];
        } catch (\Exception $e) {
            db()->rollback();
            $result = ['state' => 'error', 'info' => '删除失败：' . $e->getMessage()];
        }
        
        return json($result);
    }
    
    //批量删除工艺
    public function batch_del(){
        $input = input('post.');
        if(isset($input['ids']) && is_array($input['ids']) && !empty($input['ids'])){
            $success_count = 0;
            $error_messages = [];

            foreach($input['ids'] as $id){
                $process = \app\index\model\Process::get($id);
                if($process){
                    try {
                        $process->delete();
                        push_log('删除工艺[ '.$process['name'].' ]');
                        $success_count++;
                    } catch (\Exception $e) {
                        $error_messages[] = '工艺[ '.$process['name'].' ]删除失败：' . $e->getMessage();
                    }
                } else {
                    $error_messages[] = 'ID为'.$id.'的工艺不存在';
                }
            }

            if($success_count > 0 && empty($error_messages)){
                $result = ['state' => 'success', 'info' => '成功删除'.$success_count.'个工艺'];
            } elseif($success_count > 0 && !empty($error_messages)){
                $result = ['state' => 'warning', 'info' => '成功删除'.$success_count.'个工艺，但有'.count($error_messages).'个失败：'.implode('；', $error_messages)];
            } else {
                $result = ['state' => 'error', 'info' => '删除失败：'.implode('；', $error_messages)];
            }
        } else {
            $result = ['state' => 'error', 'info' => '请选择要删除的工艺'];
        }

        return json($result);
    }
    
    //更新状态
    public function update_status(){
        $id = input('id', 0);
        $status = input('status', 1);
        
        if($id <= 0){
            return json(['state' => 'error', 'info' => '参数错误']);
        }
        
        $process = \app\index\model\Process::get($id);
        if(!$process){
            return json(['state' => 'error', 'info' => '工艺不存在']);
        }
        
        try {
            $process->status = $status;
            $process->save();
            
            $status_text = $status == 1 ? '启用' : '停用';
            push_log($status_text.'工艺[ '.$process['name'].' ]');
            $result = ['state' => 'success', 'info' => $status_text.'成功'];
        } catch (\Exception $e) {
            $result = ['state' => 'error', 'info' => '操作失败：' . $e->getMessage()];
        }
        
        return json($result);
    }
}
