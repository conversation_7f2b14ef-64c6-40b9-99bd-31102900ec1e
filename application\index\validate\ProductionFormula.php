<?php
namespace app\index\validate;
use think\Validate;

class ProductionFormula extends Validate
{
    protected $rule = [
        'name'              => 'require|max:64',
        'code'              => 'require|max:32|unique:production_formula',
        'goods_id'          => 'require|integer|gt:0',
        'version'           => 'max:16',
        'status'            => 'require|in:0,1',
        'data'              => 'max:256'
    ];

    protected $message = [
        'name.require'              => '配方名称不能为空',
        'name.max'                  => '配方名称不能超过64个字符',
        'code.require'              => '配方编号不能为空',
        'code.max'                  => '配方编号不能超过32个字符',
        'code.unique'               => '配方编号已存在',
        'goods_id.require'          => '请选择对应商品',
        'goods_id.integer'          => '商品ID必须是整数',
        'goods_id.gt'               => '请选择对应商品',
        'version.max'               => '配方版本不能超过16个字符',
        'status.require'            => '配方状态不能为空',
        'status.in'                 => '配方状态值不正确',
        'data.max'                  => '备注信息不能超过256个字符'
    ];

    protected $scene = [
        'add'   => ['name', 'code', 'goods_id', 'version', 'status', 'data'],
        'edit'  => ['name', 'goods_id', 'version', 'status', 'data']
    ];
}
