<?php
namespace app\index\model;
use think\Model;

class Deliveryclass extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'is_delivery_class';
    
    // 设置主键
    protected $pk = 'id';
    
    // 设置字段信息
    protected $field = [
        'id', 'merchant', 'sale_id', 'customer', 'time', 'number', 
        'total_qty', 'user', 'data', 'type', 'auditinguser', 
        'auditingtime', 'more'
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = false;
    
    // JSON字段
    protected $json = ['more'];
    
    // 类型转换
    protected $type = [
        'time' => 'timestamp',
        'auditingtime' => 'timestamp',
        'total_qty' => 'float',
        'type' => 'integer',
        'merchant' => 'integer',
        'sale_id' => 'integer',
        'customer' => 'integer',
        'user' => 'integer',
        'auditinguser' => 'integer'
    ];
    
    // 关联销售订单
    public function saleinfo()
    {
        return $this->belongsTo('Saleclass', 'sale_id', 'id');
    }
    
    // 关联客户信息
    public function customerinfo()
    {
        return $this->belongsTo('Customer', 'customer', 'id');
    }
    
    // 关联制单人
    public function userinfo()
    {
        return $this->belongsTo('User', 'user', 'id');
    }
    
    // 关联审核人
    public function auditinguserinfo()
    {
        return $this->belongsTo('User', 'auditinguser', 'id');
    }
    
    // 关联发货单详情
    public function deliveryinfo()
    {
        return $this->hasMany('Deliveryinfo', 'pid', 'id');
    }
    
    // 获取审核状态
    public function getTypeAttr($value)
    {
        $status = [0 => '未审核', 1 => '已审核'];
        return ['nod' => $value, 'name' => $status[$value] ?? '未知'];
    }
    
    // 获取时间格式
    public function getTimeAttr($value)
    {
        return $value ? date('Y-m-d', $value) : '';
    }
    
    // 获取审核时间格式
    public function getAuditingtimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }
}
