<?php
namespace app\index\validate;
use think\Validate;

class QualityInspection extends Validate{
    protected $rule = [
        'goods_id' => 'require|integer',
        'report_id' => 'integer',
        'inspection_date' => 'require|date',
        'sample_qty' => 'require|integer|gt:0',
        'qualified_qty' => 'require|integer|egt:0',
        'defect_qty' => 'integer|egt:0',
        'inspection_standard' => 'max:200',
        'result' => 'require|in:pass,fail,conditional',
        'inspector_id' => 'integer'
    ];
    
    protected $message = [
        'goods_id.require' => '请选择检验商品',
        'goods_id.integer' => '商品ID必须为整数',
        'report_id.integer' => '报工记录ID必须为整数',
        'inspection_date.require' => '请选择检验日期',
        'inspection_date.date' => '检验日期格式不正确',
        'sample_qty.require' => '请输入抽样数量',
        'sample_qty.integer' => '抽样数量必须为整数',
        'sample_qty.gt' => '抽样数量必须大于0',
        'qualified_qty.require' => '请输入合格数量',
        'qualified_qty.integer' => '合格数量必须为整数',
        'qualified_qty.egt' => '合格数量不能为负数',
        'defect_qty.integer' => '不良数量必须为整数',
        'defect_qty.egt' => '不良数量不能为负数',
        'inspection_standard.max' => '检验标准不能超过200个字符',
        'result.require' => '请选择检验结果',
        'result.in' => '检验结果值不正确',
        'inspector_id.integer' => '检验员ID必须为整数'
    ];
    
    protected $scene = [
        'add' => ['goods_id', 'inspection_date', 'sample_qty', 'qualified_qty', 'defect_qty', 'inspection_standard', 'result'],
        'edit' => ['goods_id', 'inspection_date', 'sample_qty', 'qualified_qty', 'defect_qty', 'inspection_standard', 'result']
    ];
}
