<?php
namespace app\index\controller;
use app\index\controller\Acl;

class KpiManagement extends Acl {
    //KPI管理模块
    
    //主视图
    public function main(){
        return $this->fetch();
    }

    //新增KPI页面
    public function add(){
        return $this->fetch();
    }

    //编辑KPI页面
    public function edit(){
        $id = input('id');
        $this->assign('id', $id);
        return $this->fetch();
    }

    //KPI配置
    public function config(){
        return $this->fetch();
    }

    //KPI分析
    public function analysis(){
        return $this->fetch();
    }
    
    //获取KPI列表
    public function get_kpi_list(){
        $input = input('post.');

        try {
            // 检查表是否存在
            try {
                db()->query("SELECT 1 FROM is_performance_kpi LIMIT 1");
            } catch(\Exception $e) {
                // 表不存在，返回空数据
                return json(['code' => 0, 'msg' => '', 'count' => 0, 'data' => []]);
            }

            $where = ['pk.merchant' => Session('is_merchant_id')];

            //搜索条件
            if(isset_full($input, 'kpi_date')){
                $where['pk.kpi_date'] = $input['kpi_date'];
            }
            if(isset_full($input, 'kpi_period')){
                $where['pk.kpi_period'] = $input['kpi_period'];
            }
            if(isset_full($input, 'category')){
                $where['pk.category'] = $input['category'];
            }
            if(isset_full($input, 'department')){
                $where['pk.department'] = $input['department'];
            }
            if(isset_full($input, 'status')){
                $where['pk.status'] = $input['status'];
            }

            $list = db('performance_kpi')
                ->alias('pk')
                ->join('user u', 'pk.responsible_person = u.id', 'left')
                ->where($where)
                ->field('pk.*,u.name as responsible_name')
                ->order('pk.kpi_date desc, pk.category asc, pk.kpi_code asc')
                ->paginate(input('limit', 15))
                ->toArray();

            return json(['code' => 0, 'msg' => '', 'count' => $list['total'], 'data' => $list['data']]);

        } catch(\Exception $e) {
            return json(['code' => 1, 'msg' => '获取KPI列表失败: ' . $e->getMessage(), 'count' => 0, 'data' => []]);
        }
    }

    //获取KPI概览数据
    public function get_kpi_overview(){
        try {
            // 检查表是否存在
            try {
                db()->query("SELECT 1 FROM is_performance_kpi LIMIT 1");

                $where = ['merchant' => Session('is_merchant_id')];

                // 获取KPI统计数据
                $stats = db('performance_kpi')
                    ->where($where)
                    ->field('
                        COUNT(*) as total_kpi,
                        SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as achieved_kpi,
                        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as warning_kpi,
                        SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as abnormal_kpi
                    ')
                    ->find();

                return json(['state' => 'success', 'data' => $stats]);

            } catch(\Exception $e) {
                // 表不存在，返回默认值
                return json(['state' => 'success', 'data' => [
                    'total_kpi' => 0,
                    'achieved_kpi' => 0,
                    'warning_kpi' => 0,
                    'abnormal_kpi' => 0
                ]]);
            }

        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '获取KPI概览失败: ' . $e->getMessage()]);
        }
    }
    
    //新增|更新KPI
    public function set_kpi(){
        $input = input('post.');
        
        if(isset($input['id'])){
            if(empty($input['id'])){
                //新增
                $input['merchant'] = Session('is_merchant_id');
                $input['createtime'] = time();
                
                //计算达成率和得分
                if($input['target_value'] > 0){
                    $input['achievement_rate'] = ($input['actual_value'] / $input['target_value']) * 100;
                }
                
                $input['score'] = min(100, $input['achievement_rate']);
                $input['weighted_score'] = ($input['score'] * $input['weight']) / 100;
                
                //判断状态
                if($input['achievement_rate'] >= 100){
                    $input['status'] = 2; //优秀
                } elseif($input['achievement_rate'] >= 80){
                    $input['status'] = 1; //正常
                } else {
                    $input['status'] = 0; //异常
                }
                
                $id = db('performance_kpi')->insertGetId($input);
                push_log('新增KPI指标[ '.$input['kpi_name'].' ]');
                $result = ['state' => 'success', 'data' => ['id' => $id]];
            }else{
                //更新
                //重新计算指标
                if($input['target_value'] > 0){
                    $input['achievement_rate'] = ($input['actual_value'] / $input['target_value']) * 100;
                }
                
                $input['score'] = min(100, $input['achievement_rate']);
                $input['weighted_score'] = ($input['score'] * $input['weight']) / 100;
                
                //判断状态
                if($input['achievement_rate'] >= 100){
                    $input['status'] = 2; //优秀
                } elseif($input['achievement_rate'] >= 80){
                    $input['status'] = 1; //正常
                } else {
                    $input['status'] = 0; //异常
                }
                
                db('performance_kpi')->where(['id' => $input['id']])->update($input);
                push_log('更新KPI指标[ '.$input['kpi_name'].' ]');
                $result = ['state' => 'success'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //获取KPI详情
    public function get_kpi_info(){
        $input = input('post.');
        if(isset_full($input, 'id')){
            $kpi = db('performance_kpi')
                ->alias('pk')
                ->join('user u', 'pk.responsible_person = u.id', 'left')
                ->where(['pk.id' => $input['id']])
                ->field('pk.*,u.name as responsible_name')
                ->find();
                
            if($kpi){
                return json(['state' => 'success', 'data' => $kpi]);
            }else{
                return json(['state' => 'error', 'info' => 'KPI指标不存在']);
            }
        }else{
            return json(['state' => 'error', 'info' => '传入参数不完整']);
        }
    }
    
    //删除KPI
    public function del_kpi(){
        $input = input('post.');
        if(isset_full($input, 'id')){
            $kpi = db('performance_kpi')->where(['id' => $input['id']])->find();
            if($kpi){
                db('performance_kpi')->where(['id' => $input['id']])->delete();
                push_log('删除KPI指标[ '.$kpi['kpi_name'].' ]');
                $result = ['state' => 'success'];
            }else{
                $result = ['state' => 'error', 'info' => 'KPI指标不存在'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整'];
        }
        
        return json($result);
    }
    
    //批量生成KPI
    public function batch_generate_kpi(){
        $input = input('post.');
        $kpi_date = isset($input['kpi_date']) ? $input['kpi_date'] : date('Y-m-d');
        $kpi_period = isset($input['kpi_period']) ? $input['kpi_period'] : 'daily';
        
        try {
            $this->generateDailyKPI($kpi_date);
            return json(['state' => 'success', 'info' => 'KPI数据生成成功']);
        } catch (\Exception $e) {
            return json(['state' => 'error', 'info' => 'KPI数据生成失败：' . $e->getMessage()]);
        }
    }
    
    //KPI分析报告
    public function get_kpi_analysis(){
        $input = input('post.');
        $start_date = isset($input['start_date']) ? $input['start_date'] : date('Y-m-01');
        $end_date = isset($input['end_date']) ? $input['end_date'] : date('Y-m-d');
        $kpi_period = isset($input['kpi_period']) ? $input['kpi_period'] : 'daily';
        
        // 总体KPI分析
        $overall_analysis = $this->getOverallKPIAnalysis($start_date, $end_date, $kpi_period);
        
        // 分类KPI分析
        $category_analysis = $this->getCategoryKPIAnalysis($start_date, $end_date, $kpi_period);
        
        // KPI趋势分析
        $trend_analysis = $this->getKPITrendAnalysis($start_date, $end_date, $kpi_period);
        
        // 异常KPI分析
        $exception_analysis = $this->getExceptionKPIAnalysis($start_date, $end_date, $kpi_period);
        
        return json([
            'state' => 'success',
            'data' => [
                'overall' => $overall_analysis,
                'category' => $category_analysis,
                'trend' => $trend_analysis,
                'exception' => $exception_analysis
            ]
        ]);
    }
    
    //生成日度KPI
    private function generateDailyKPI($kpi_date){
        $merchant_id = Session('is_merchant_id');
        
        // 获取当日生产统计数据
        $production_stats = db('production_summary')
            ->where([
                'merchant' => $merchant_id,
                'summary_date' => $kpi_date
            ])
            ->field('SUM(plan_qty) as total_plan_qty, SUM(actual_qty) as total_actual_qty,
                     SUM(good_qty) as total_good_qty, SUM(defect_qty) as total_defect_qty,
                     AVG(efficiency) as avg_efficiency, AVG(oee) as avg_oee,
                     AVG(qualified_rate) as avg_qualified_rate, SUM(working_hours) as total_working_hours')
            ->find();
            
        if(!$production_stats || $production_stats['total_actual_qty'] == 0){
            throw new \Exception('当日无生产数据，无法生成KPI');
        }
        
        // 计划完成率
        $plan_completion_rate = $production_stats['total_plan_qty'] > 0 ? 
            ($production_stats['total_actual_qty'] / $production_stats['total_plan_qty']) * 100 : 0;
            
        // 产品合格率
        $quality_rate = $production_stats['total_actual_qty'] > 0 ? 
            ($production_stats['total_good_qty'] / $production_stats['total_actual_qty']) * 100 : 0;
            
        // 设备综合效率
        $oee = $production_stats['avg_oee'];
        
        // 获取成本数据
        $cost_stats = db('cost_analysis')
            ->where([
                'merchant' => $merchant_id,
                'analysis_date' => $kpi_date
            ])
            ->field('AVG(unit_cost) as avg_unit_cost')
            ->find();
            
        $unit_cost = $cost_stats ? $cost_stats['avg_unit_cost'] : 0;
        
        // 获取异常数据
        $exception_count = db('production_exception')
            ->where([
                'merchant' => $merchant_id,
                'occurrence_time' => ['like', $kpi_date.'%']
            ])
            ->count();
            
        // 能耗数据（简化计算）
        $energy_consumption = $production_stats['total_working_hours'] * 50; // 假设每小时50kWh
        $energy_per_unit = $production_stats['total_actual_qty'] > 0 ? 
            $energy_consumption / $production_stats['total_actual_qty'] : 0;
        
        // KPI数据数组
        $kpi_data = [
            [
                'kpi_code' => 'OEE_DAILY',
                'kpi_name' => '设备综合效率(OEE)',
                'category' => '效率指标',
                'target_value' => 85.00,
                'actual_value' => $oee,
                'weight' => 25.00,
                'unit' => '%'
            ],
            [
                'kpi_code' => 'QUALITY_RATE_DAILY',
                'kpi_name' => '产品合格率',
                'category' => '质量指标',
                'target_value' => 98.00,
                'actual_value' => $quality_rate,
                'weight' => 20.00,
                'unit' => '%'
            ],
            [
                'kpi_code' => 'PLAN_COMPLETION_DAILY',
                'kpi_name' => '计划完成率',
                'category' => '产量指标',
                'target_value' => 100.00,
                'actual_value' => $plan_completion_rate,
                'weight' => 20.00,
                'unit' => '%'
            ],
            [
                'kpi_code' => 'UNIT_COST_DAILY',
                'kpi_name' => '单位成本控制',
                'category' => '成本指标',
                'target_value' => 12.50,
                'actual_value' => $unit_cost,
                'weight' => 15.00,
                'unit' => '元/件'
            ],
            [
                'kpi_code' => 'SAFETY_INCIDENT_DAILY',
                'kpi_name' => '安全事故次数',
                'category' => '安全指标',
                'target_value' => 0.00,
                'actual_value' => $exception_count,
                'weight' => 10.00,
                'unit' => '次'
            ],
            [
                'kpi_code' => 'ENERGY_CONSUMPTION_DAILY',
                'kpi_name' => '能耗控制',
                'category' => '环保指标',
                'target_value' => 2.50,
                'actual_value' => $energy_per_unit,
                'weight' => 10.00,
                'unit' => 'kWh/件'
            ]
        ];
        
        // 保存KPI数据
        foreach($kpi_data as $kpi){
            $kpi['merchant'] = $merchant_id;
            $kpi['kpi_date'] = $kpi_date;
            $kpi['kpi_period'] = 'daily';
            $kpi['department'] = '生产部';
            
            // 计算达成率和得分
            if($kpi['kpi_code'] == 'UNIT_COST_DAILY' || $kpi['kpi_code'] == 'SAFETY_INCIDENT_DAILY'){
                // 成本和安全事故：越低越好
                $kpi['achievement_rate'] = $kpi['target_value'] > 0 ? 
                    min(200, ($kpi['target_value'] / max($kpi['actual_value'], 0.01)) * 100) : 100;
            } else {
                // 其他指标：越高越好
                $kpi['achievement_rate'] = $kpi['target_value'] > 0 ? 
                    ($kpi['actual_value'] / $kpi['target_value']) * 100 : 0;
            }
            
            $kpi['score'] = min(100, $kpi['achievement_rate']);
            $kpi['weighted_score'] = ($kpi['score'] * $kpi['weight']) / 100;
            
            // 判断状态
            if($kpi['achievement_rate'] >= 100){
                $kpi['status'] = 2; //优秀
                $kpi['trend'] = '稳定';
            } elseif($kpi['achievement_rate'] >= 80){
                $kpi['status'] = 1; //正常
                $kpi['trend'] = '稳定';
            } else {
                $kpi['status'] = 0; //异常
                $kpi['trend'] = '下降';
            }
            
            $kpi['createtime'] = time();
            
            // 检查是否已存在
            $existing = db('performance_kpi')
                ->where([
                    'kpi_date' => $kpi_date,
                    'kpi_period' => 'daily',
                    'kpi_code' => $kpi['kpi_code']
                ])
                ->find();
                
            if($existing){
                db('performance_kpi')->where(['id' => $existing['id']])->update($kpi);
            }else{
                db('performance_kpi')->insert($kpi);
            }
        }
    }
    
    //获取总体KPI分析
    private function getOverallKPIAnalysis($start_date, $end_date, $kpi_period){
        $kpi_data = db('performance_kpi')
            ->where([
                'merchant' => Session('is_merchant_id'),
                'kpi_date' => ['between', [$start_date, $end_date]],
                'kpi_period' => $kpi_period
            ])
            ->field('AVG(achievement_rate) as avg_achievement_rate, AVG(weighted_score) as avg_weighted_score,
                     COUNT(*) as total_kpi, SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as excellent_count,
                     SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as normal_count,
                     SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as exception_count')
            ->find();
            
        return [
            'avg_achievement_rate' => round($kpi_data['avg_achievement_rate'], 2),
            'avg_score' => round($kpi_data['avg_weighted_score'], 2),
            'total_kpi' => $kpi_data['total_kpi'],
            'excellent_rate' => $kpi_data['total_kpi'] > 0 ? round(($kpi_data['excellent_count'] / $kpi_data['total_kpi']) * 100, 1) : 0,
            'normal_rate' => $kpi_data['total_kpi'] > 0 ? round(($kpi_data['normal_count'] / $kpi_data['total_kpi']) * 100, 1) : 0,
            'exception_rate' => $kpi_data['total_kpi'] > 0 ? round(($kpi_data['exception_count'] / $kpi_data['total_kpi']) * 100, 1) : 0
        ];
    }
    
    //获取分类KPI分析
    private function getCategoryKPIAnalysis($start_date, $end_date, $kpi_period){
        return db('performance_kpi')
            ->where([
                'merchant' => Session('is_merchant_id'),
                'kpi_date' => ['between', [$start_date, $end_date]],
                'kpi_period' => $kpi_period
            ])
            ->field('category, AVG(achievement_rate) as avg_achievement_rate, AVG(weighted_score) as avg_score,
                     COUNT(*) as kpi_count')
            ->group('category')
            ->select();
    }
    
    //获取KPI趋势分析
    private function getKPITrendAnalysis($start_date, $end_date, $kpi_period){
        return db('performance_kpi')
            ->where([
                'merchant' => Session('is_merchant_id'),
                'kpi_date' => ['between', [$start_date, $end_date]],
                'kpi_period' => $kpi_period
            ])
            ->field('kpi_date, AVG(achievement_rate) as avg_achievement_rate')
            ->group('kpi_date')
            ->order('kpi_date asc')
            ->select();
    }
    
    //获取异常KPI分析
    private function getExceptionKPIAnalysis($start_date, $end_date, $kpi_period){
        return db('performance_kpi')
            ->where([
                'merchant' => Session('is_merchant_id'),
                'kpi_date' => ['between', [$start_date, $end_date]],
                'kpi_period' => $kpi_period,
                'status' => 0
            ])
            ->field('kpi_name, kpi_date, target_value, actual_value, achievement_rate')
            ->order('achievement_rate asc')
            ->select();
    }
}
