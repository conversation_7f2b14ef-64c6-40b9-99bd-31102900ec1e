<?php
namespace app\index\controller;
use app\index\controller\Acl;
use app\index\model\InjectionMachine;
use app\index\model\ProductionSchedule;
use app\index\model\ProductionSummary;

class CapacityAnalysis extends Acl{
    
    //主页面
    public function main(){
        return $this->fetch();
    }
    
    //获取产能分析数据
    public function get_analysis_data(){
        $input = input('post.');
        $start_date = isset($input['start_date']) ? $input['start_date'] : date('Y-m-01');
        $end_date = isset($input['end_date']) ? $input['end_date'] : date('Y-m-d');
        $machine_id = isset($input['machine_id']) ? $input['machine_id'] : '';
        
        // 获取排产数据进行产能分析
        try {
            $query = db('production_schedule')
                ->alias('ps')
                ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
                ->join('work_shift ws', 'ps.shift_id = ws.id', 'left')
                ->join('production_order po', 'ps.production_order_id = po.id', 'left')
                ->join('goods g', 'po.goods_id = g.id', 'left')
                ->where('ps.merchant', Session('is_merchant_id'))
                ->where('ps.schedule_date', 'between', [$start_date, $end_date]);

            if($machine_id){
                $query->where('ps.machine_id', $machine_id);
            }

            $schedules = $query
                ->field('ps.*, im.name as machine_name, ws.name as shift_name, g.name as goods_name, po.order_no')
                ->order('ps.schedule_date desc, ps.machine_id asc, ps.shift_id asc')
                ->select()
                ->toArray();
        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '查询排产数据失败: ' . $e->getMessage()]);
        }

        // 如果没有排产数据，尝试查询生产汇总数据
        if(empty($schedules)){
            try {
                $query = db('production_summary')
                    ->alias('ps')
                    ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
                    ->join('work_shift ws', 'ps.shift_id = ws.id', 'left')
                    ->join('goods g', 'ps.goods_id = g.id', 'left')
                    ->where('ps.merchant', Session('is_merchant_id'))
                    ->where('ps.summary_date', 'between', [$start_date, $end_date]);

                if($machine_id){
                    $query->where('ps.machine_id', $machine_id);
                }

                $schedules = $query
                    ->field('ps.*, im.name as machine_name, ws.name as shift_name, g.name as goods_name')
                    ->order('ps.summary_date desc, ps.machine_id asc, ps.shift_id asc')
                    ->select()
                    ->toArray();
            } catch(\Exception $e) {
                return json(['state' => 'error', 'info' => '查询生产汇总数据失败: ' . $e->getMessage()]);
            }
        }
            
        // 计算产能指标
        $analysis_data = $this->calculateCapacityMetrics($schedules, $start_date, $end_date);

        return json(['state' => 'success', 'data' => $analysis_data]);
    }
    
    //获取设备产能对比
    public function get_machine_comparison(){
        $input = input('post.');
        $start_date = isset($input['start_date']) ? $input['start_date'] : date('Y-m-01');
        $end_date = isset($input['end_date']) ? $input['end_date'] : date('Y-m-d');
        
        // 按设备分组统计（优先使用排产数据）
        try {
            $machine_data = db('production_schedule')
                ->alias('ps')
                ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
                ->where('ps.merchant', Session('is_merchant_id'))
                ->where('ps.schedule_date', 'between', [$start_date, $end_date])
                ->field('ps.machine_id, im.name as machine_name,
                    SUM(ps.plan_qty) as total_plan_qty,
                    SUM(ps.plan_qty) as total_actual_qty,
                    SUM(ps.plan_qty) as total_good_qty,
                    0 as total_defect_qty,
                    COUNT(*) * 8 as total_working_hours,
                    0 as total_downtime_hours,
                    100 as avg_completion_rate,
                    100 as avg_qualified_rate,
                    100 as avg_efficiency')
                ->group('ps.machine_id')
                ->select()
                ->toArray();

            // 如果没有排产数据，使用生产汇总数据
            if(empty($machine_data)){
                $machine_data = db('production_summary')
                    ->alias('ps')
                    ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
                    ->where('ps.merchant', Session('is_merchant_id'))
                    ->where('ps.summary_date', 'between', [$start_date, $end_date])
                    ->field('ps.machine_id, im.name as machine_name,
                        SUM(ps.plan_qty) as total_plan_qty,
                        SUM(ps.actual_qty) as total_actual_qty,
                        SUM(ps.good_qty) as total_good_qty,
                        SUM(ps.defect_qty) as total_defect_qty,
                        SUM(ps.working_hours) as total_working_hours,
                        SUM(ps.downtime_hours) as total_downtime_hours,
                        AVG(ps.completion_rate) as avg_completion_rate,
                        AVG(ps.qualified_rate) as avg_qualified_rate,
                        AVG(ps.efficiency) as avg_efficiency')
                    ->group('ps.machine_id')
                    ->select()
                    ->toArray();
            }
        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '查询设备对比数据失败: ' . $e->getMessage()]);
        }
            
        $comparison_data = [];
        foreach($machine_data as $data){
            $comparison_data[] = [
                'machine_id' => $data['machine_id'],
                'machine_name' => $data['machine_name'] ?: '未知设备',
                'total_plan_qty' => $data['total_plan_qty'],
                'total_actual_qty' => $data['total_actual_qty'],
                'total_good_qty' => $data['total_good_qty'],
                'total_defect_qty' => $data['total_defect_qty'],
                'total_working_hours' => round($data['total_working_hours'], 2),
                'total_downtime_hours' => round($data['total_downtime_hours'], 2),
                'avg_completion_rate' => round($data['avg_completion_rate'], 2),
                'avg_qualified_rate' => round($data['avg_qualified_rate'], 2),
                'avg_efficiency' => round($data['avg_efficiency'], 2),
                'utilization_rate' => $data['total_working_hours'] > 0 ?
                    round($data['total_working_hours'] / ($data['total_working_hours'] + $data['total_downtime_hours']) * 100, 2) : 0
            ];
        }
        
        return json(['state' => 'success', 'data' => $comparison_data]);
    }
    
    //获取产能趋势数据
    public function get_capacity_trend(){
        $input = input('post.');
        $start_date = isset($input['start_date']) ? $input['start_date'] : date('Y-m-01');
        $end_date = isset($input['end_date']) ? $input['end_date'] : date('Y-m-d');
        $machine_id = isset($input['machine_id']) ? $input['machine_id'] : '';
        
        // 按日期分组统计（优先使用排产数据）
        try {
            $query = db('production_schedule')
                ->where('merchant', Session('is_merchant_id'))
                ->where('schedule_date', 'between', [$start_date, $end_date]);

            if($machine_id){
                $query->where('machine_id', $machine_id);
            }

            $trend_data = $query
                ->field('schedule_date as summary_date,
                    SUM(plan_qty) as daily_plan_qty,
                    SUM(plan_qty) as daily_actual_qty,
                    SUM(plan_qty) as daily_good_qty,
                    0 as daily_defect_qty,
                    COUNT(*) * 8 as daily_working_hours,
                    100 as daily_completion_rate,
                    100 as daily_qualified_rate,
                    100 as daily_efficiency')
                ->group('schedule_date')
                ->order('schedule_date asc')
                ->select()
                ->toArray();

            // 如果没有排产数据，使用生产汇总数据
            if(empty($trend_data)){
                $query = db('production_summary')
                    ->where('merchant', Session('is_merchant_id'))
                    ->where('summary_date', 'between', [$start_date, $end_date]);

                if($machine_id){
                    $query->where('machine_id', $machine_id);
                }

                $trend_data = $query
                    ->field('summary_date,
                        SUM(plan_qty) as daily_plan_qty,
                        SUM(actual_qty) as daily_actual_qty,
                        SUM(good_qty) as daily_good_qty,
                        SUM(defect_qty) as daily_defect_qty,
                        SUM(working_hours) as daily_working_hours,
                        AVG(completion_rate) as daily_completion_rate,
                        AVG(qualified_rate) as daily_qualified_rate,
                        AVG(efficiency) as daily_efficiency')
                    ->group('summary_date')
                    ->order('summary_date asc')
                    ->select()
                    ->toArray();
            }
        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '查询趋势数据失败: ' . $e->getMessage()]);
        }

        $trend_result = [];
        foreach($trend_data as $data){
            $trend_result[] = [
                'date' => isset($data['summary_date']) ? $data['summary_date'] : $data['schedule_date'],
                'plan_qty' => $data['daily_plan_qty'],
                'actual_qty' => $data['daily_actual_qty'],
                'good_qty' => $data['daily_good_qty'],
                'defect_qty' => $data['daily_defect_qty'],
                'working_hours' => round($data['daily_working_hours'], 2),
                'completion_rate' => round($data['daily_completion_rate'], 2),
                'qualified_rate' => round($data['daily_qualified_rate'], 2),
                'efficiency' => round($data['daily_efficiency'], 2)
            ];
        }
        
        return json(['state' => 'success', 'data' => $trend_result]);
    }
    
    //获取瓶颈分析
    public function get_bottleneck_analysis(){
        $input = input('post.');
        $start_date = isset($input['start_date']) ? $input['start_date'] : date('Y-m-01');
        $end_date = isset($input['end_date']) ? $input['end_date'] : date('Y-m-d');
        
        // 分析各设备的瓶颈指标（优先使用排产数据）
        try {
            $bottleneck_data = db('production_schedule')
                ->alias('ps')
                ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
                ->where('ps.merchant', Session('is_merchant_id'))
                ->where('ps.schedule_date', 'between', [$start_date, $end_date])
                ->field('ps.machine_id, im.name as machine_name,
                    100 as avg_efficiency,
                    100 as avg_qualified_rate,
                    0 as total_downtime,
                    COUNT(*) as work_days')
                ->group('ps.machine_id')
                ->select()
                ->toArray();

            // 如果没有排产数据，使用生产汇总数据
            if(empty($bottleneck_data)){
                $bottleneck_data = db('production_summary')
                    ->alias('ps')
                    ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
                    ->where('ps.merchant', Session('is_merchant_id'))
                    ->where('ps.summary_date', 'between', [$start_date, $end_date])
                    ->field('ps.machine_id, im.name as machine_name,
                        AVG(ps.efficiency) as avg_efficiency,
                        AVG(ps.qualified_rate) as avg_qualified_rate,
                        SUM(ps.downtime_hours) as total_downtime,
                        COUNT(*) as work_days')
                    ->group('ps.machine_id')
                    ->select()
                    ->toArray();
            }
        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '查询瓶颈分析数据失败: ' . $e->getMessage()]);
        }
            
        $bottlenecks = [];
        foreach($bottleneck_data as $data){
            $issues = [];
            
            // 效率低于80%
            if($data['avg_efficiency'] < 80){
                $issues[] = '生产效率偏低(' . round($data['avg_efficiency'], 2) . '%)';
            }

            // 合格率低于95%
            if($data['avg_qualified_rate'] < 95){
                $issues[] = '产品合格率偏低(' . round($data['avg_qualified_rate'], 2) . '%)';
            }

            // 平均每天停机时间超过2小时
            $avg_downtime = $data['work_days'] > 0 ? $data['total_downtime'] / $data['work_days'] : 0;
            if($avg_downtime > 2){
                $issues[] = '停机时间过长(平均' . round($avg_downtime, 2) . '小时/天)';
            }

            if(!empty($issues)){
                $bottlenecks[] = [
                    'machine_id' => $data['machine_id'],
                    'machine_name' => $data['machine_name'] ?: '未知设备',
                    'issues' => $issues,
                    'avg_efficiency' => round($data['avg_efficiency'], 2),
                    'avg_qualified_rate' => round($data['avg_qualified_rate'], 2),
                    'avg_downtime' => round($avg_downtime, 2),
                    'severity' => count($issues) // 问题严重程度
                ];
            }
        }
        
        // 按严重程度排序
        usort($bottlenecks, function($a, $b){
            return $b['severity'] - $a['severity'];
        });
        
        return json(['state' => 'success', 'data' => $bottlenecks]);
    }
    
    //计算产能指标
    private function calculateCapacityMetrics($data, $start_date, $end_date){
        $total_plan_qty = 0;
        $total_actual_qty = 0;
        $total_good_qty = 0;
        $total_defect_qty = 0;
        $total_working_hours = 0;
        $total_downtime_hours = 0;
        $completion_rates = [];
        $qualified_rates = [];
        $efficiencies = [];

        // 获取设备和班次信息用于计算理论产能
        $machines = db('injection_machine')
            ->where(['merchant' => Session('is_merchant_id'), 'status' => 1])
            ->select();
        $shifts = db('work_shift')
            ->where(['merchant' => Session('is_merchant_id')])
            ->select();

        foreach($data as $item){
            // 兼容排产数据和生产汇总数据
            $plan_qty = isset($item['plan_qty']) ? $item['plan_qty'] : 0;
            $actual_qty = isset($item['actual_qty']) ? $item['actual_qty'] : $plan_qty; // 排产数据用计划数量作为实际数量
            $good_qty = isset($item['good_qty']) ? $item['good_qty'] : $actual_qty;
            $defect_qty = isset($item['defect_qty']) ? $item['defect_qty'] : 0;

            $total_plan_qty += $plan_qty;
            $total_actual_qty += $actual_qty;
            $total_good_qty += $good_qty;
            $total_defect_qty += $defect_qty;

            // 计算工作时间（如果是排产数据，根据计划时间计算）
            if(isset($item['plan_start_time']) && isset($item['plan_end_time'])){
                $start_time = strtotime($item['plan_start_time']);
                $end_time = strtotime($item['plan_end_time']);
                $working_hours = ($end_time - $start_time) / 3600;
                $total_working_hours += $working_hours;
            } else {
                $total_working_hours += isset($item['working_hours']) ? $item['working_hours'] : 0;
                $total_downtime_hours += isset($item['downtime_hours']) ? $item['downtime_hours'] : 0;
            }

            // 计算完成率和合格率
            if($plan_qty > 0){
                $completion_rate = ($actual_qty / $plan_qty) * 100;
                $completion_rates[] = $completion_rate;
            }

            if($actual_qty > 0){
                $qualified_rate = ($good_qty / $actual_qty) * 100;
                $qualified_rates[] = $qualified_rate;
            }

            // 效率计算（这里简化为完成率）
            if(isset($completion_rate)){
                $efficiencies[] = $completion_rate;
            }
        }
        
        $total_hours = $total_working_hours + $total_downtime_hours;
        
        return [
            'period' => $start_date . ' 至 ' . $end_date,
            'total_plan_qty' => $total_plan_qty,
            'total_actual_qty' => $total_actual_qty,
            'total_good_qty' => $total_good_qty,
            'total_defect_qty' => $total_defect_qty,
            'total_working_hours' => round($total_working_hours, 2),
            'total_downtime_hours' => round($total_downtime_hours, 2),
            'overall_completion_rate' => $total_plan_qty > 0 ? round($total_actual_qty / $total_plan_qty * 100, 2) : 0,
            'overall_qualified_rate' => $total_actual_qty > 0 ? round($total_good_qty / $total_actual_qty * 100, 2) : 0,
            'utilization_rate' => $total_hours > 0 ? round($total_working_hours / $total_hours * 100, 2) : 0,
            'avg_completion_rate' => !empty($completion_rates) ? round(array_sum($completion_rates) / count($completion_rates), 2) : 0,
            'avg_qualified_rate' => !empty($qualified_rates) ? round(array_sum($qualified_rates) / count($qualified_rates), 2) : 0,
            'avg_efficiency' => !empty($efficiencies) ? round(array_sum($efficiencies) / count($efficiencies), 2) : 0,
            'daily_avg_output' => $this->calculateDailyAverage($total_actual_qty, $start_date, $end_date)
        ];
    }
    
    //计算日均产量
    private function calculateDailyAverage($total_qty, $start_date, $end_date){
        $start_timestamp = strtotime($start_date);
        $end_timestamp = strtotime($end_date);
        $days = ceil(($end_timestamp - $start_timestamp) / 86400) + 1;
        
        return $days > 0 ? round($total_qty / $days, 2) : 0;
    }
}
