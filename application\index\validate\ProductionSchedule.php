<?php
namespace app\index\validate;
use think\Validate;

class ProductionSchedule extends Validate{
    protected $rule = [
        'production_order_id' => 'require|integer',
        'machine_id' => 'require|integer',
        'shift_id' => 'require|integer',
        'schedule_date' => 'require|date',
        'plan_start_time' => 'require',
        'plan_end_time' => 'require',
        'plan_qty' => 'require|integer|gt:0',
        'actual_qty' => 'integer|egt:0',
        'operator_id' => 'integer',
        'status' => 'in:0,1,2,3,4'
    ];
    
    protected $message = [
        'production_order_id.require' => '请选择生产订单',
        'production_order_id.integer' => '订单ID必须为整数',
        'machine_id.require' => '请选择生产设备',
        'machine_id.integer' => '设备ID必须为整数',
        'shift_id.require' => '请选择生产班次',
        'shift_id.integer' => '班次ID必须为整数',
        'schedule_date.require' => '请选择排产日期',
        'schedule_date.date' => '排产日期格式不正确',
        'plan_start_time.require' => '请输入计划开始时间',
        'plan_end_time.require' => '请输入计划结束时间',
        'plan_qty.require' => '请输入计划数量',
        'plan_qty.integer' => '计划数量必须为整数',
        'plan_qty.gt' => '计划数量必须大于0',
        'actual_qty.integer' => '实际数量必须为整数',
        'actual_qty.egt' => '实际数量不能为负数',
        'operator_id.integer' => '操作员ID必须为整数',
        'status.in' => '状态值不正确'
    ];
    
    protected $scene = [
        'add' => ['production_order_id', 'machine_id', 'shift_id', 'schedule_date', 'plan_start_time', 'plan_end_time', 'plan_qty', 'operator_id'],
        'edit' => ['production_order_id', 'machine_id', 'shift_id', 'schedule_date', 'plan_start_time', 'plan_end_time', 'plan_qty', 'operator_id', 'status']
    ];
}
