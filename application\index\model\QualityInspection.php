<?php
namespace app\index\model;
use think\Model;

class QualityInspection extends Model{
    //质量检验表
    protected $table = 'is_quality_inspection';
    
    protected $type = [
        'more' => 'json'
    ];
    
    //关联商品信息
    public function goodsinfo(){
        return $this->hasOne('app\index\model\Goods', 'id', 'goods_id');
    }
    
    //关联检验员信息
    public function inspectorinfo(){
        return $this->hasOne('app\index\model\User', 'id', 'inspector_id');
    }
    
    //关联报工记录
    public function reportinfo(){
        return $this->hasOne('app\index\model\ProductionReport', 'id', 'report_id');
    }
    
    //检验结果读取器
    protected function getResultAttr($val, $data){
        $results = [
            'pass' => '合格',
            'fail' => '不合格',
            'conditional' => '有条件合格'
        ];
        return isset($results[$val]) ? $results[$val] : '未知';
    }
    
    //检验结果原始值读取器
    protected function getResultValueAttr($val, $data){
        return $data['result'];
    }
    
    //创建时间读取器
    protected function getCreatetimeAttr($val, $data){
        return $val ? date('Y-m-d H:i:s', $val) : '';
    }
    
    //检验日期读取器
    protected function getInspectionDateAttr($val, $data){
        return $val ? $val : '';
    }
    
    //检验时间读取器
    protected function getInspectionTimeAttr($val, $data){
        return $val ? $val : '';
    }
    
    //计算合格率
    public function getQualifiedRate(){
        if($this->sample_qty <= 0){
            return 0;
        }
        
        return round(($this->qualified_qty / $this->sample_qty) * 100, 2);
    }
    
    //计算不良率
    public function getDefectRate(){
        if($this->sample_qty <= 0){
            return 0;
        }
        
        return round(($this->defect_qty / $this->sample_qty) * 100, 2);
    }
    
    //获取检验详细信息
    public function getInspectionDetails(){
        return [
            'basic_info' => [
                'inspection_date' => $this->inspection_date,
                'inspection_time' => $this->inspection_time,
                'goods_name' => $this->goodsinfo ? $this->goodsinfo->name : '',
                'inspector_name' => $this->inspectorinfo ? $this->inspectorinfo->name : '',
                'inspection_standard' => $this->inspection_standard
            ],
            'sample_data' => [
                'sample_qty' => $this->sample_qty,
                'qualified_qty' => $this->qualified_qty,
                'defect_qty' => $this->defect_qty,
                'qualified_rate' => $this->getQualifiedRate(),
                'defect_rate' => $this->getDefectRate()
            ],
            'result_info' => [
                'result' => $this->result,
                'result_desc' => $this->result_desc,
                'improvement_suggestions' => $this->improvement_suggestions
            ]
        ];
    }
    
    //根据日期获取检验记录
    public static function getByDate($date, $goods_id = null, $inspector_id = null){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'inspection_date' => $date
        ];
        
        if($goods_id){
            $where['goods_id'] = $goods_id;
        }
        
        if($inspector_id){
            $where['inspector_id'] = $inspector_id;
        }
        
        return self::where($where)
            ->with(['goodsinfo', 'inspectorinfo'])
            ->order('inspection_time desc')
            ->select();
    }
    
    //根据商品获取检验记录
    public static function getByGoods($goods_id, $start_date = null, $end_date = null){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'goods_id' => $goods_id
        ];
        
        if($start_date){
            $where[] = ['inspection_date', '>=', $start_date];
        }
        
        if($end_date){
            $where[] = ['inspection_date', '<=', $end_date];
        }
        
        return self::where($where)
            ->with(['inspectorinfo'])
            ->order('inspection_date desc, inspection_time desc')
            ->select();
    }
    
    //根据检验员获取检验记录
    public static function getByInspector($inspector_id, $start_date = null, $end_date = null){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'inspector_id' => $inspector_id
        ];
        
        if($start_date){
            $where[] = ['inspection_date', '>=', $start_date];
        }
        
        if($end_date){
            $where[] = ['inspection_date', '<=', $end_date];
        }
        
        return self::where($where)
            ->with(['goodsinfo'])
            ->order('inspection_date desc, inspection_time desc')
            ->select();
    }
    
    //获取质量统计
    public static function getQualityStatistics($start_date, $end_date, $group_by = 'date'){
        $where = [
            'merchant' => Session('is_merchant_id'),
            ['inspection_date', 'between', [$start_date, $end_date]]
        ];
        
        $field = 'inspection_date,
                  COUNT(*) as inspection_count,
                  SUM(sample_qty) as total_sample_qty,
                  SUM(qualified_qty) as total_qualified_qty,
                  SUM(defect_qty) as total_defect_qty,
                  AVG(qualified_qty/sample_qty*100) as avg_qualified_rate';
        
        $group = 'inspection_date';
        $order = 'inspection_date asc';
        
        switch($group_by){
            case 'goods':
                $field = 'goods_id,' . str_replace('inspection_date,', '', $field);
                $group = 'goods_id';
                $order = 'total_sample_qty desc';
                break;
            case 'inspector':
                $field = 'inspector_id,' . str_replace('inspection_date,', '', $field);
                $group = 'inspector_id';
                $order = 'inspection_count desc';
                break;
        }
        
        return self::where($where)
            ->field($field)
            ->group($group)
            ->order($order)
            ->select();
    }
    
    //获取不合格品分析
    public static function getDefectAnalysis($start_date, $end_date){
        $where = [
            'merchant' => Session('is_merchant_id'),
            ['inspection_date', 'between', [$start_date, $end_date]],
            ['result', 'in', ['fail', 'conditional']]
        ];
        
        return self::where($where)
            ->field('goods_id,
                     COUNT(*) as defect_inspection_count,
                     SUM(sample_qty) as total_sample_qty,
                     SUM(defect_qty) as total_defect_qty,
                     AVG(defect_qty/sample_qty*100) as avg_defect_rate')
            ->with(['goodsinfo'])
            ->group('goods_id')
            ->order('total_defect_qty desc')
            ->select();
    }
    
    //获取检验员绩效
    public static function getInspectorPerformance($start_date, $end_date){
        $where = [
            'merchant' => Session('is_merchant_id'),
            ['inspection_date', 'between', [$start_date, $end_date]]
        ];
        
        return self::where($where)
            ->field('inspector_id,
                     COUNT(*) as inspection_count,
                     SUM(sample_qty) as total_sample_qty,
                     COUNT(CASE WHEN result = "pass" THEN 1 END) as pass_count,
                     COUNT(CASE WHEN result = "fail" THEN 1 END) as fail_count')
            ->with(['inspectorinfo'])
            ->group('inspector_id')
            ->order('inspection_count desc')
            ->select();
    }
    
    //验证检验数据
    public function validateInspectionData(){
        $errors = [];
        
        //检查数量逻辑
        if($this->qualified_qty + $this->defect_qty != $this->sample_qty){
            $errors[] = '合格数量 + 不良数量 应等于抽样数量';
        }
        
        if($this->sample_qty <= 0){
            $errors[] = '抽样数量必须大于0';
        }
        
        if($this->qualified_qty < 0 || $this->defect_qty < 0){
            $errors[] = '合格数量和不良数量不能为负数';
        }
        
        //检查结果逻辑
        if($this->defect_qty > 0 && $this->result == 'pass'){
            $errors[] = '存在不良品时检验结果不应为合格';
        }
        
        if($this->defect_qty == 0 && $this->result == 'fail'){
            $errors[] = '无不良品时检验结果不应为不合格';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
