<?php
namespace app\index\validate;
use think\Validate;

class WorkShift extends Validate
{
    protected $rule = [
        'name'              => 'require|max:32',
        'code'              => 'require|max:16|unique:work_shift',
        'start_time'        => 'require',
        'end_time'          => 'require',
        'duration'          => 'require|float|gt:0|elt:24',
        'sort'              => 'integer|egt:0',
        'status'            => 'require|in:0,1',
        'data'              => 'max:128'
    ];

    protected $message = [
        'name.require'              => '班次名称不能为空',
        'name.max'                  => '班次名称不能超过32个字符',
        'code.require'              => '班次编号不能为空',
        'code.max'                  => '班次编号不能超过16个字符',
        'code.unique'               => '班次编号已存在',
        'start_time.require'        => '开始时间不能为空',
        'end_time.require'          => '结束时间不能为空',
        'duration.require'          => '工作时长不能为空',
        'duration.float'            => '工作时长必须是数字',
        'duration.gt'               => '工作时长必须大于0',
        'duration.elt'              => '工作时长不能超过24小时',
        'sort.integer'              => '排序必须是整数',
        'sort.egt'                  => '排序不能小于0',
        'status.require'            => '班次状态不能为空',
        'status.in'                 => '班次状态值不正确',
        'data.max'                  => '备注信息不能超过128个字符'
    ];

    protected $scene = [
        'add'   => ['name', 'code', 'start_time', 'end_time', 'duration', 'sort', 'status', 'data'],
        'edit'  => ['name', 'start_time', 'end_time', 'duration', 'sort', 'status', 'data']
    ];
}
