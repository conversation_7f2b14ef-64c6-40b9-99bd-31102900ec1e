<?php
namespace app\index\controller;
use app\index\controller\Acl;
use app\index\model\ProductionOrder as ProductionOrderModel;
use app\index\model\ProductionSchedule;

class ProductionOrder extends Acl {
    //生产订单管理模块
    
    //主视图
    public function main(){
        return $this->fetch();
    }
    
    //获取生产订单列表
    public function get_list(){
        $input = input('post.');
        $where = auth('production_order', []);
        
        //搜索条件
        if(isset_full($input, 'order_no')){
            $where[] = ['order_no', 'like', '%'.$input['order_no'].'%'];
        }
        if(isset_full($input, 'goods_id')){
            $where[] = ['goods_id', '=', $input['goods_id']];
        }
        if(isset_full($input, 'status')){
            $where[] = ['status', '=', $input['status']];
        }
        if(isset_full($input, 'priority')){
            $where[] = ['priority', '=', $input['priority']];
        }
        if(isset_full($input, 'plan_start_date')){
            $where[] = ['plan_start_date', '>=', $input['plan_start_date']];
        }
        if(isset_full($input, 'plan_end_date')){
            $where[] = ['plan_end_date', '<=', $input['plan_end_date']];
        }
        
        $list = ProductionOrderModel::with(['goodsinfo', 'formulainfo', 'moldinfo', 'creatorinfo'])
            ->where($where)
            ->order('priority asc, plan_start_date asc, id desc')
            ->paginate(input('limit', 15))
            ->toArray();

        // 添加状态文本和排产进度信息
        foreach($list['data'] as &$item){
            // 状态文本转换和数值转换
            $status_map = [
                0 => '待排产',
                1 => '已排产',
                2 => '生产中',
                3 => '已完成',
                4 => '已取消'
            ];

            // 状态文本到数值的映射
            $status_text_to_value = [
                '待排产' => 0,
                '已排产' => 1,
                '生产中' => 2,
                '已完成' => 3,
                '已取消' => 4
            ];

            // 如果status是文本，转换为数值
            if(is_string($item['status']) && isset($status_text_to_value[$item['status']])){
                $item['status_value'] = $status_text_to_value[$item['status']];
                $item['status_text'] = $item['status'];
            } else {
                // 如果status是数值，转换为文本
                $item['status_value'] = (int)$item['status'];
                $item['status_text'] = $status_map[$item['status']] ?? '未知';
            }

            // 优先级文本转换和数值转换
            $priority_text_to_value = [
                '紧急' => 1,
                '高' => 2,
                '普通' => 3,
                '低' => 4
            ];
            $priority_value_to_text = [
                1 => '紧急',
                2 => '高',
                3 => '普通',
                4 => '低'
            ];

            // 如果 priority 是文本，转换为数值
            if(is_string($item['priority']) && isset($priority_text_to_value[$item['priority']])){
                $item['priority_value'] = $priority_text_to_value[$item['priority']];
                $item['priority_text'] = $item['priority'];
            } else {
                // 如果 priority 是数值，转换为文本
                $item['priority_value'] = (int)$item['priority'];
                $item['priority_text'] = $priority_value_to_text[$item['priority']] ?? '普通';
            }

            // 获取排产进度信息
            $schedule_info = $this->getOrderScheduleProgress($item['id']);
            $item['schedule_progress'] = $schedule_info;
        }

        return json(['code' => 0, 'msg' => '', 'count' => $list['total'], 'data' => $list['data']]);
    }
    
    //新增|更新生产订单
    public function set(){
        $input = input('post.');
        
        if(isset($input['id'])){
            if(empty($input['id'])){
                //新增
                $input['merchant'] = Session('is_merchant_id');
                $input['creator'] = Session('is_user_id');
                $input['createtime'] = time();
                
                //自动生成订单号
                if(empty($input['order_no'])){
                    $input['order_no'] = $this->generateOrderNo();
                }
                
                // 基础验证
                if(empty($input['goods_id'])){
                    return json(['state' => 'error', 'info' => '商品不能为空']);
                }
                if(empty($input['plan_qty']) || $input['plan_qty'] <= 0){
                    return json(['state' => 'error', 'info' => '计划数量必须大于0']);
                }
                if(empty($input['plan_start_date'])){
                    return json(['state' => 'error', 'info' => '计划开始日期不能为空']);
                }
                if(empty($input['plan_end_date'])){
                    return json(['state' => 'error', 'info' => '计划结束日期不能为空']);
                }

                // 验证通过，继续处理
                if(true){
                    $create_info = ProductionOrderModel::create($input);
                    push_log('新增生产订单[ '.$create_info['order_no'].' ]');
                    $result = ['state' => 'success', 'data' => ['id' => $create_info->id]];
                }else{
                    $result = ['state' => 'error', 'info' => $vali];
                }
            }else{
                //更新
                $order = ProductionOrderModel::get($input['id']);
                if($order && $order->status == 0){
                    // 基础验证
                    if(empty($input['goods_id'])){
                        return json(['state' => 'error', 'info' => '商品不能为空']);
                    }
                    if(empty($input['plan_qty']) || $input['plan_qty'] <= 0){
                        return json(['state' => 'error', 'info' => '计划数量必须大于0']);
                    }
                    if(empty($input['plan_start_date'])){
                        return json(['state' => 'error', 'info' => '计划开始日期不能为空']);
                    }
                    if(empty($input['plan_end_date'])){
                        return json(['state' => 'error', 'info' => '计划结束日期不能为空']);
                    }

                    // 验证通过，继续处理
                    if(true){
                        $update_info = ProductionOrderModel::update($input);
                        push_log('更新生产订单[ '.$update_info['order_no'].' ]');
                        $result = ['state' => 'success'];
                    }else{
                        $result = ['state' => 'error', 'info' => $vali];
                    }
                }else{
                    $result = ['state' => 'error', 'info' => '只能修改待排产状态的订单!'];
                }
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //删除生产订单
    public function del(){
        $input = input('post.');
        if(isset_full($input, 'id')){
            $order = ProductionOrderModel::find($input['id']);
            if($order){
                if($order->status == 0){
                    $order->delete();
                    push_log('删除生产订单[ '.$order['order_no'].' ]');
                    $result = ['state' => 'success'];
                }else{
                    $result = ['state' => 'error', 'info' => '只能删除待排产状态的订单!'];
                }
            }else{
                $result = ['state' => 'error', 'info' => '订单不存在!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //获取订单详情
    public function get_info(){
        $input = input('post.');
        if(isset_full($input, 'id')){
            $order = ProductionOrderModel::with(['goodsinfo', 'formulainfo', 'moldinfo', 'processinfo', 'creatorinfo', 'auditorinfo'])
                ->find($input['id']);
            if($order){
                $result = ['state' => 'success', 'data' => $order];
            }else{
                $result = ['state' => 'error', 'info' => '订单不存在!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //审核订单
    public function audit(){
        $input = input('post.');
        if(isset_full($input, 'id') && isset_full($input, 'status')){
            $order = ProductionOrderModel::get($input['id']);
            if($order){
                if($order->status == 0){
                    $order->status = $input['status'];
                    if($input['status'] == 1){
                        $order->auditor = Session('is_user_id');
                        $order->audit_time = time();
                    }
                    $order->save();

                    $status_text = $input['status'] == 1 ? '审核通过' : '审核拒绝';
                    push_log('生产订单[ '.$order['order_no'].' ]'.$status_text);
                    $result = ['state' => 'success'];
                }else{
                    $result = ['state' => 'error', 'info' => '只能审核待排产状态的订单!'];
                }
            }else{
                $result = ['state' => 'error', 'info' => '订单不存在!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //智能排产
    public function auto_schedule(){
        $input = input('post.');

        try {
            if(!isset_full($input, 'order_id')){
                return json(['state' => 'error', 'info' => '传入参数不完整!']);
            }

            $order = ProductionOrderModel::find($input['order_id']);
            if(!$order){
                return json(['state' => 'error', 'info' => '生产订单不存在!']);
            }

            if($order->status != 0){
                return json(['state' => 'error', 'info' => '订单状态不正确，只能对待排产订单进行智能排产!']);
            }

            //获取排产建议
            $suggestions = $this->getSimpleScheduleSuggestions($order);

            if(empty($suggestions)){
                return json(['state' => 'error', 'info' => '无法生成排产建议，请检查设备和班次配置']);
            }

            return json(['state' => 'success', 'data' => $suggestions, 'info' => '智能排产建议生成成功']);

        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '智能排产失败: ' . $e->getMessage()]);
        }
    }

    //手动排产
    public function manual_schedule(){
        $input = input('post.');
        if(isset_full($input, 'order_id') && isset_full($input, 'schedules')){
            $order = ProductionOrderModel::get($input['order_id']);
            if($order && $order->status == 0){
                //验证排产数据
                $validation = $this->validateSchedules($input['schedules']);
                if(!$validation['valid']){
                    return json(['state' => 'error', 'info' => $validation['message']]);
                }

                //开启事务
                db()->startTrans();
                try {
                    foreach ($input['schedules'] as $schedule) {
                        $schedule['production_order_id'] = $input['order_id'];
                        $schedule['merchant'] = Session('is_merchant_id');
                        $schedule['scheduler'] = Session('is_user_id');
                        $schedule['createtime'] = time();

                        ProductionSchedule::create($schedule);
                    }

                    //更新订单状态为已排产
                    $order->status = 1;
                    $order->save();

                    //更新产能分析
                    $this->updateCapacityAnalysis($input['schedules']);

                    db()->commit();
                    push_log('生产订单[ '.$order['order_no'].' ]完成排产');
                    $result = ['state' => 'success'];
                } catch (\Exception $e) {
                    db()->rollback();
                    $result = ['state' => 'error', 'info' => '排产失败：' . $e->getMessage()];
                }
            }else{
                $result = ['state' => 'error', 'info' => '订单状态不正确!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }

        return json($result);
    }
    
    //获取排产计划
    public function get_schedules(){
        $input = input('post.');
        if(isset_full($input, 'order_id')){
            $schedules = ProductionSchedule::with(['machineinfo', 'shiftinfo', 'operatorinfo'])
                ->where(['production_order_id' => $input['order_id']])
                ->order('schedule_date asc, plan_start_time asc')
                ->select()
                ->toArray();
                
            return json(['state' => 'success', 'data' => $schedules]);
        }else{
            return json(['state' => 'error', 'info' => '参数错误']);
        }
    }
    
    //取消订单
    public function cancel(){
        $input = input('post.');
        if(isset_full($input, 'id')){
            $order = ProductionOrderModel::find($input['id']);
            if($order){
                if($order->status <= 1){
                    //检查是否有进行中的排产
                    $active_schedule = db('production_schedule')
                        ->where([
                            'production_order_id' => $input['id'],
                            'status' => ['in', [1, 3]] //生产中或暂停
                        ])
                        ->find();
                        
                    if($active_schedule){
                        return json(['state' => 'error', 'info' => '存在进行中的生产任务，无法取消订单!']);
                    }
                    
                    $order->status = 4; //已取消
                    $order->save();
                    
                    //取消相关排产
                    db('production_schedule')
                        ->where(['production_order_id' => $input['id']])
                        ->update(['status' => 4]);
                    
                    push_log('取消生产订单[ '.$order['order_no'].' ]');
                    $result = ['state' => 'success'];
                }else{
                    $result = ['state' => 'error', 'info' => '当前状态无法取消订单!'];
                }
            }else{
                $result = ['state' => 'error', 'info' => '订单不存在!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //生成订单号
    private function generateOrderNo(){
        $prefix = 'PO' . date('Ymd');
        $count = ProductionOrderModel::where('order_no', 'like', $prefix.'%')->count();
        return $prefix . str_pad($count + 1, 4, '0', STR_PAD_LEFT);
    }

    //获取简化的排产建议
    private function getSimpleScheduleSuggestions($order){
        $suggestions = [];

        try {
            // 获取可用的设备
            $machines = db('injection_machine')
                ->where([
                    'merchant' => Session('is_merchant_id'),
                    'status' => 1
                ])
                ->field('id,name,tonnage,status')
                ->select();

            if(empty($machines)){
                return [];
            }

            // 获取可用的班次
            $shifts = db('work_shift')
                ->where([
                    'merchant' => Session('is_merchant_id')
                ])
                ->field('id,name,start_time,end_time')
                ->select();

            if(empty($shifts)){
                return [];
            }

            // 获取可用的模具（如果有的话）
            $molds = db('mold')
                ->where([
                    'merchant' => Session('is_merchant_id'),
                    'goods_id' => $order->goods_id,
                    'status' => 1
                ])
                ->field('id,name,cavity_count,cycle_time')
                ->select();

            // 生成排产建议
            $suggestion_count = 0;
            foreach($machines as $machine){
                if($suggestion_count >= 3) break; // 最多生成3个建议

                foreach($shifts as $shift){
                    if($suggestion_count >= 3) break;

                    // 计算建议的排产日期（从计划开始日期开始）
                    $schedule_date = $order->plan_start_date;

                    // 检查该设备在该班次是否已有排产
                    $existing = db('production_schedule')
                        ->where([
                            'machine_id' => $machine['id'],
                            'shift_id' => $shift['id'],
                            'schedule_date' => $schedule_date,
                            'status' => ['in', [0, 1, 3]] // 待生产、生产中、暂停
                        ])
                        ->find();

                    if($existing){
                        // 如果当天已有排产，尝试下一天
                        $schedule_date = date('Y-m-d', strtotime($schedule_date . ' +1 day'));
                    }

                    // 选择模具（如果有的话）
                    $selected_mold = null;
                    if(!empty($molds)){
                        $selected_mold = $molds[0]; // 选择第一个可用模具
                    }

                    // 计算预估工时（简化计算）
                    $estimated_hours = 8; // 默认8小时
                    if($selected_mold && $selected_mold['cycle_time'] > 0){
                        $pieces_per_hour = 3600 / $selected_mold['cycle_time'] * $selected_mold['cavity_count'];
                        if($pieces_per_hour > 0){
                            $estimated_hours = ceil($order->plan_qty / $pieces_per_hour);
                            $estimated_hours = min($estimated_hours, 8); // 最多8小时
                        }
                    }

                    // 计算开始和结束时间
                    $start_time = $shift['start_time'];
                    $end_time = date('H:i:s', strtotime($start_time . ' +' . $estimated_hours . ' hours'));

                    // 如果结束时间超过班次结束时间，调整为班次结束时间
                    if($end_time > $shift['end_time']){
                        $end_time = $shift['end_time'];
                    }

                    $suggestion = [
                        'machine_id' => $machine['id'],
                        'machine_name' => $machine['name'],
                        'shift_id' => $shift['id'],
                        'shift_name' => $shift['name'],
                        'schedule_date' => $schedule_date,
                        'plan_start_time' => $start_time,
                        'plan_end_time' => $end_time,
                        'plan_qty' => $order->plan_qty,
                        'estimated_hours' => $estimated_hours,
                        'priority_score' => $this->calculatePriorityScore($machine, $shift, $schedule_date, $order),
                        'reason' => $this->getRecommendationReason($machine, $shift, $selected_mold)
                    ];

                    if($selected_mold){
                        $suggestion['mold_id'] = $selected_mold['id'];
                        $suggestion['mold_name'] = $selected_mold['name'];
                    }

                    $suggestions[] = $suggestion;
                    $suggestion_count++;
                }
            }

            // 按优先级排序
            usort($suggestions, function($a, $b){
                return $b['priority_score'] <=> $a['priority_score'];
            });

            return array_slice($suggestions, 0, 3); // 返回前3个建议

        } catch(\Exception $e) {
            return [];
        }
    }

    //计算优先级评分
    private function calculatePriorityScore($machine, $shift, $schedule_date, $order){
        $score = 100; // 基础分数

        // 根据设备吨位评分（假设更大的设备优先级更高）
        $score += $machine['tonnage'] * 0.1;

        // 根据排产日期评分（越早排产分数越高）
        $days_diff = (strtotime($schedule_date) - strtotime($order->plan_start_date)) / (24 * 3600);
        $score -= $days_diff * 10;

        // 根据班次评分（假设白班优先级更高）
        if(strpos($shift['name'], '白班') !== false || strpos($shift['name'], '早班') !== false){
            $score += 20;
        } elseif(strpos($shift['name'], '中班') !== false){
            $score += 10;
        }

        return max($score, 0);
    }

    //获取推荐原因
    private function getRecommendationReason($machine, $shift, $mold){
        $reasons = [];

        $reasons[] = "设备: " . $machine['name'] . " (吨位: " . $machine['tonnage'] . "T)";
        $reasons[] = "班次: " . $shift['name'] . " (" . $shift['start_time'] . "-" . $shift['end_time'] . ")";

        if($mold){
            $reasons[] = "模具: " . $mold['name'] . " (穴数: " . $mold['cavity_count'] . ")";
        }

        return implode('; ', $reasons);
    }

    //获取排产建议（原方法保留但不使用）
    private function getScheduleSuggestions($order){
        //获取适用的设备和模具
        $available_resources = $this->getAvailableResources($order);

        if(empty($available_resources)){
            return [];
        }

        //计算排产方案
        $suggestions = [];
        foreach($available_resources as $resource){
            $suggestion = $this->calculateSchedule($order, $resource);
            if($suggestion){
                $suggestions[] = $suggestion;
            }
        }

        //按优先级排序
        usort($suggestions, function($a, $b){
            return $a['score'] <=> $b['score'];
        });

        return array_slice($suggestions, 0, 3); //返回前3个最优方案
    }

    //获取可用资源
    private function getAvailableResources($order){
        $resources = [];

        //获取适用的模具
        $molds = db('mold')
            ->where([
                'merchant' => Session('is_merchant_id'),
                'goods_id' => $order->goods_id,
                'status' => 1
            ])
            ->select();

        foreach($molds as $mold){
            //获取适用的设备
            $machines = db('injection_machine')
                ->where([
                    'merchant' => Session('is_merchant_id'),
                    'status' => 1,
                    'tonnage' => ['>=', $this->getRequiredTonnage($mold)]
                ])
                ->select();

            foreach($machines as $machine){
                $resources[] = [
                    'machine' => $machine,
                    'mold' => $mold,
                    'efficiency' => $this->calculateEfficiency($machine, $mold)
                ];
            }
        }

        return $resources;
    }

    //计算排产方案
    private function calculateSchedule($order, $resource){
        $machine = $resource['machine'];
        $mold = $resource['mold'];

        //获取排产模板
        $template = db('schedule_template')
            ->where([
                'goods_id' => $order->goods_id,
                'machine_id' => $machine['id'],
                'mold_id' => $mold['id'],
                'status' => 1
            ])
            ->find();

        if(!$template){
            //使用默认参数
            $template = [
                'standard_cycle_time' => $mold['cycle_time'],
                'standard_output_per_hour' => 3600 / $mold['cycle_time'] * $mold['cavity_count'],
                'setup_time' => 30,
                'cleanup_time' => 15,
                'efficiency_factor' => 0.85
            ];
        }

        //计算所需时间
        $required_hours = $order->plan_qty / ($template['standard_output_per_hour'] * $template['efficiency_factor']);
        $setup_hours = $template['setup_time'] / 60;
        $cleanup_hours = $template['cleanup_time'] / 60;
        $total_hours = $required_hours + $setup_hours + $cleanup_hours;

        //查找可用时间段
        $available_slots = $this->findAvailableTimeSlots($machine['id'], $order->plan_start_date, $total_hours);

        if(empty($available_slots)){
            return null;
        }

        $best_slot = $available_slots[0];

        //计算评分
        $score = $this->calculateScheduleScore($order, $resource, $best_slot);

        return [
            'machine_id' => $machine['id'],
            'machine_name' => $machine['name'],
            'mold_id' => $mold['id'],
            'mold_name' => $mold['name'],
            'schedule_date' => $best_slot['date'],
            'shift_id' => $best_slot['shift_id'],
            'shift_name' => $best_slot['shift_name'],
            'plan_start_time' => $best_slot['start_time'],
            'plan_end_time' => $best_slot['end_time'],
            'plan_qty' => $order->plan_qty,
            'estimated_hours' => $total_hours,
            'efficiency' => $template['efficiency_factor'],
            'score' => $score,
            'reason' => $this->getScheduleReason($score)
        ];
    }
    
    //订单统计
    public function statistics(){
        $input = input('post.');
        $start_date = isset($input['start_date']) ? $input['start_date'] : date('Y-m-01');
        $end_date = isset($input['end_date']) ? $input['end_date'] : date('Y-m-d');
        
        $where = auth('production_order', []);
        $where[] = ['plan_start_date', 'between', [$start_date, $end_date]];
        
        $stats = db('production_order')
            ->where($where)
            ->field('status,COUNT(*) as count,SUM(plan_qty) as total_qty')
            ->group('status')
            ->select()
            ->toArray();
            
        $result = [
            'total' => 0,
            'pending' => 0,
            'scheduled' => 0,
            'producing' => 0,
            'completed' => 0,
            'cancelled' => 0
        ];
        
        foreach($stats as $stat){
            $result['total'] += $stat['count'];
            switch($stat['status']){
                case 0: $result['pending'] = $stat['count']; break;
                case 1: $result['scheduled'] = $stat['count']; break;
                case 2: $result['producing'] = $stat['count']; break;
                case 3: $result['completed'] = $stat['count']; break;
                case 4: $result['cancelled'] = $stat['count']; break;
            }
        }
        
        return json(['state' => 'success', 'data' => $result]);
    }

    //查找可用时间段
    private function findAvailableTimeSlots($machine_id, $start_date, $required_hours){
        $slots = [];
        $current_date = $start_date;
        $max_days = 30; //最多查找30天

        for($i = 0; $i < $max_days; $i++){
            $date = date('Y-m-d', strtotime($current_date . " +$i days"));

            //获取该日期的班次
            $shifts = db('work_shift')
                ->where(['merchant' => Session('is_merchant_id'), 'status' => 1])
                ->order('sort asc')
                ->select();

            foreach($shifts as $shift){
                //检查该时间段是否可用
                $capacity = $this->getAvailableCapacity($machine_id, $date, $shift['id']);

                if($capacity['remaining_hours'] >= $required_hours){
                    $start_time = $date . ' ' . $shift['start_time'];
                    $end_time = date('Y-m-d H:i:s', strtotime($start_time . " +{$required_hours} hours"));

                    $slots[] = [
                        'date' => $date,
                        'shift_id' => $shift['id'],
                        'shift_name' => $shift['name'],
                        'start_time' => $start_time,
                        'end_time' => $end_time,
                        'available_hours' => $capacity['remaining_hours']
                    ];
                }
            }

            if(count($slots) >= 5) break; //找到5个可用时间段就够了
        }

        return $slots;
    }

    //获取可用产能
    private function getAvailableCapacity($machine_id, $date, $shift_id){
        $capacity = db('capacity_analysis')
            ->where([
                'machine_id' => $machine_id,
                'analysis_date' => $date,
                'shift_id' => $shift_id
            ])
            ->find();

        if(!$capacity){
            //创建默认产能分析
            $shift = db('work_shift')->where(['id' => $shift_id])->find();
            $capacity = [
                'available_hours' => $shift['duration'],
                'planned_hours' => 0,
                'remaining_hours' => $shift['duration'],
                'utilization_rate' => 0
            ];
        }

        return $capacity;
    }

    //计算所需锁模力
    private function getRequiredTonnage($mold){
        //根据模具信息计算所需锁模力
        //这里简化处理，实际应根据产品投影面积等计算
        return max(100, $mold['cavity_count'] * 20);
    }

    //计算设备模具效率
    private function calculateEfficiency($machine, $mold){
        //根据设备和模具匹配度计算效率
        $tonnage_ratio = $machine['tonnage'] / $this->getRequiredTonnage($mold);

        if($tonnage_ratio >= 1.5){
            return 0.9; //设备能力充足
        } elseif($tonnage_ratio >= 1.2){
            return 0.85; //设备能力适中
        } elseif($tonnage_ratio >= 1.0){
            return 0.8; //设备能力刚好
        } else {
            return 0.6; //设备能力不足
        }
    }

    //计算排产评分
    private function calculateScheduleScore($order, $resource, $slot){
        $score = 0;

        //时间因素（越早越好）
        $days_delay = (strtotime($slot['date']) - strtotime($order->plan_start_date)) / 86400;
        $score += max(0, 100 - $days_delay * 5);

        //设备效率因素
        $score += $resource['efficiency'] * 50;

        //设备利用率因素
        $utilization = $slot['available_hours'] > 0 ?
            (8 - $slot['available_hours']) / 8 * 100 : 0;
        $score += $utilization * 0.3;

        //优先级因素
        $priority_bonus = [1 => 30, 2 => 20, 3 => 10, 4 => 0];
        $score += $priority_bonus[$order->priority] ?? 0;

        return round($score, 2);
    }

    //获取排产原因
    private function getScheduleReason($score){
        if($score >= 150){
            return '最优方案：时间充裕，设备匹配度高';
        } elseif($score >= 120){
            return '较优方案：时间合适，设备效率良好';
        } elseif($score >= 100){
            return '可行方案：基本满足要求';
        } else {
            return '备选方案：存在一定限制';
        }
    }

    //验证排产数据
    private function validateSchedules($schedules){
        foreach($schedules as $schedule){
            //检查必要字段
            if(empty($schedule['machine_id']) || empty($schedule['schedule_date']) ||
               empty($schedule['shift_id']) || empty($schedule['plan_qty'])){
                return ['valid' => false, 'message' => '排产数据不完整'];
            }

            //检查时间冲突
            $conflict = $this->checkTimeConflict($schedule);
            if($conflict){
                return ['valid' => false, 'message' => '存在时间冲突：' . $conflict];
            }
        }

        return ['valid' => true, 'message' => ''];
    }

    //检查时间冲突
    private function checkTimeConflict($schedule){
        $existing = db('production_schedule')
            ->where([
                'machine_id' => $schedule['machine_id'],
                'schedule_date' => $schedule['schedule_date'],
                'shift_id' => $schedule['shift_id'],
                'status' => ['in', [0, 1, 3]] //待生产、生产中、暂停
            ])
            ->find();

        if($existing){
            return '设备在该时间段已有排产';
        }

        return null;
    }

    //更新产能分析
    private function updateCapacityAnalysis($schedules){
        foreach($schedules as $schedule){
            $capacity = db('capacity_analysis')
                ->where([
                    'machine_id' => $schedule['machine_id'],
                    'analysis_date' => $schedule['schedule_date'],
                    'shift_id' => $schedule['shift_id']
                ])
                ->find();

            if($capacity){
                $planned_hours = $capacity['planned_hours'] +
                    (strtotime($schedule['plan_end_time']) - strtotime($schedule['plan_start_time'])) / 3600;

                db('capacity_analysis')
                    ->where(['id' => $capacity['id']])
                    ->update([
                        'planned_hours' => $planned_hours,
                        'remaining_hours' => $capacity['available_hours'] - $planned_hours,
                        'utilization_rate' => ($planned_hours / $capacity['available_hours']) * 100
                    ]);
            }
        }
    }

    //批量删除订单
    public function batch_del(){
        $input = input('post.');
        if(isset_full($input, 'ids') && is_array($input['ids'])){
            $success_count = 0;
            $error_messages = [];

            foreach($input['ids'] as $id){
                $order = ProductionOrderModel::find($id);
                if($order){
                    if($order->status == 0){
                        $order->delete();
                        $success_count++;
                        push_log('删除生产订单[ '.$order['order_no'].' ]');
                    }else{
                        $error_messages[] = '订单['.$order['order_no'].']状态不允许删除';
                    }
                }else{
                    $error_messages[] = 'ID['.$id.']对应的订单不存在';
                }
            }

            if($success_count > 0){
                $message = '成功删除'.$success_count.'个订单';
                if(!empty($error_messages)){
                    $message .= '，但有'.count($error_messages).'个订单删除失败';
                }
                $result = ['state' => 'success', 'info' => $message];
            }else{
                $result = ['state' => 'error', 'info' => '没有订单被删除：'.implode('；', $error_messages)];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }

        return json($result);
    }

    //新增订单页面
    public function add(){
        return $this->fetch();
    }

    //编辑订单页面
    public function edit(){
        $id = input('id');
        $order = ProductionOrderModel::with(['goodsinfo', 'formulainfo', 'moldinfo'])
            ->find($id);
        if(!$order){
            $this->error('订单不存在');
        }
        $this->assign('order', $order);
        return $this->fetch();
    }

    //查看订单页面
    public function view(){
        $id = input('id');
        $order = ProductionOrderModel::with(['goodsinfo', 'formulainfo', 'moldinfo', 'processinfo', 'creatorinfo', 'auditorinfo'])
            ->find($id);
        if(!$order){
            $this->error('订单不存在');
        }
        $this->assign('order', $order);
        return $this->fetch();
    }

    //获取订单排产进度
    private function getOrderScheduleProgress($order_id){
        try {
            // 获取该订单的所有排产记录
            $schedules = db('production_schedule')
                ->where([
                    'production_order_id' => $order_id,
                    'merchant' => Session('is_merchant_id')
                ])
                ->select();

            if(empty($schedules)){
                return [
                    'scheduled_qty' => 0,
                    'completed_qty' => 0,
                    'progress_rate' => 0,
                    'schedule_count' => 0,
                    'completed_count' => 0
                ];
            }

            $scheduled_qty = 0;
            $completed_qty = 0;
            $schedule_count = count($schedules);
            $completed_count = 0;

            foreach($schedules as $schedule){
                if($schedule['status'] != 4){ // 排除已取消的排产
                    $scheduled_qty += $schedule['plan_qty'];

                    if($schedule['status'] == 2){ // 已完成
                        $completed_qty += $schedule['actual_qty'] ?: $schedule['plan_qty'];
                        $completed_count++;
                    }
                }
            }

            $progress_rate = $scheduled_qty > 0 ? round(($completed_qty / $scheduled_qty) * 100, 2) : 0;

            return [
                'scheduled_qty' => $scheduled_qty,
                'completed_qty' => $completed_qty,
                'progress_rate' => $progress_rate,
                'schedule_count' => $schedule_count,
                'completed_count' => $completed_count
            ];

        } catch(\Exception $e) {
            return [
                'scheduled_qty' => 0,
                'completed_qty' => 0,
                'progress_rate' => 0,
                'schedule_count' => 0,
                'completed_count' => 0
            ];
        }
    }

    //手动同步所有订单状态
    public function sync_all_order_status(){
        try {
            // 获取所有生产订单
            $orders = db('production_order')
                ->where(['merchant' => Session('is_merchant_id')])
                ->select();

            $updated_count = 0;

            foreach($orders as $order){
                // 获取该订单的所有排产记录
                $schedules = db('production_schedule')
                    ->where([
                        'production_order_id' => $order['id'],
                        'merchant' => Session('is_merchant_id')
                    ])
                    ->select();

                $new_status = $this->calculateOrderStatus($order, $schedules);

                // 如果状态有变化，则更新
                if($order['status'] != $new_status){
                    db('production_order')
                        ->where(['id' => $order['id'], 'merchant' => Session('is_merchant_id')])
                        ->update(['status' => $new_status]);

                    $updated_count++;
                }
            }

            return json([
                'state' => 'success',
                'info' => "状态同步完成，共更新 {$updated_count} 个订单状态"
            ]);

        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '同步失败: ' . $e->getMessage()]);
        }
    }

    //计算订单应该的状态（与 ProductionSchedule 中的方法保持一致）
    private function calculateOrderStatus($order, $schedules){
        // 如果没有排产记录，状态为待排产
        if(empty($schedules)){
            return 0; // 待排产
        }

        // 统计各种状态的排产数量
        $status_counts = [
            0 => 0, // 待生产
            1 => 0, // 生产中
            2 => 0, // 已完成
            3 => 0, // 暂停
            4 => 0  // 已取消
        ];

        $total_scheduled_qty = 0;
        $total_completed_qty = 0;

        foreach($schedules as $schedule){
            $status = $schedule['status'];
            $status_counts[$status]++;

            if($status != 4){ // 排除已取消的排产
                $total_scheduled_qty += $schedule['plan_qty'];

                if($status == 2){ // 已完成的排产
                    $total_completed_qty += $schedule['plan_qty'];
                }
            }
        }

        // 判断订单状态
        // 如果所有排产都已完成，且完成数量达到订单计划数量
        if($status_counts[2] > 0 && $total_completed_qty >= $order['plan_qty']){
            return 3; // 已完成
        }

        // 如果有生产中的排产
        if($status_counts[1] > 0){
            return 2; // 生产中
        }

        // 如果有待生产或暂停的排产
        if($status_counts[0] > 0 || $status_counts[3] > 0){
            return 1; // 已排产
        }

        // 如果只有已取消的排产，回到待排产状态
        if($status_counts[4] > 0 && $total_scheduled_qty == 0){
            return 0; // 待排产
        }

        // 默认为已排产状态
        return 1;
    }

    //获取可用的生产工单列表（用于生产入库单选择）
    public function get_available_orders(){
        try {
            // 获取已完成或生产中的生产工单
            $orders = ProductionOrderModel::with('goodsinfo')
                ->where([
                    'merchant' => Session('is_merchant_id'),
                    'status' => ['in', [2, 3]] // 2:生产中, 3:已完成
                ])
                ->field('id, order_no, goods_id, plan_qty, status')
                ->order('order_no desc')
                ->limit(50)
                ->select();

            $result = [];
            foreach($orders as $order){
                $result[] = [
                    'id' => $order['id'],
                    'order_no' => $order['order_no'],
                    'goods_id' => $order['goods_id'],
                    'goods_name' => $order['goodsinfo']['name'] ?? '未知商品',
                    'plan_qty' => $order['plan_qty'],
                    'status' => $order['status']
                ];
            }

            return json(['code' => 0, 'msg' => '获取成功', 'data' => $result]);

        } catch(\Exception $e) {
            return json(['code' => 1, 'msg' => '获取生产工单失败: ' . $e->getMessage(), 'data' => []]);
        }
    }

    //获取生产订单入库信息
    public function get_inbound_info(){
        $input = input('post.');

        if(!isset_full($input, 'order_id')){
            return json(['code' => 1, 'msg' => '参数不完整']);
        }

        try {
            $order_id = $input['order_id'];

            // 获取生产订单信息
            $order = ProductionOrderModel::where(['id' => $order_id, 'merchant' => Session('is_merchant_id')])->find();
            if(!$order){
                return json(['code' => 1, 'msg' => '生产订单不存在']);
            }

            // 获取该订单的合格数量（从生产报工表统计）
            $totalGoodQty = 0;
            try {
                $totalGoodQty = db('production_report')
                    ->alias('pr')
                    ->join('production_schedule ps', 'pr.schedule_id = ps.id')
                    ->where([
                        'ps.production_order_id' => $order_id,
                        'pr.merchant' => Session('is_merchant_id')
                    ])
                    ->sum('pr.good_qty');
            } catch(\Exception $e) {
                // 如果生产报工表不存在，使用计划数量
                $totalGoodQty = $order['plan_qty'];
            }

            // 获取已入库数量（从其他入库单统计）
            $alreadyInboundQty = 0;
            try {
                $alreadyInboundQty = db('otpurchaseclass')
                    ->alias('opc')
                    ->join('otpurchaseinfo opi', 'opc.id = opi.pid')
                    ->where([
                        'opc.production_order_id' => $order_id,
                        'opc.pagetype' => 2, // 生产入库单
                        'opc.type' => 1, // 已审核
                        'opc.merchant' => Session('is_merchant_id')
                    ])
                    ->sum('opi.nums');
            } catch(\Exception $e) {
                $alreadyInboundQty = 0;
            }

            $availableQty = max(0, $totalGoodQty - $alreadyInboundQty);

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => [
                    'total_good_qty' => floatval($totalGoodQty),
                    'already_inbound_qty' => floatval($alreadyInboundQty),
                    'available_qty' => floatval($availableQty)
                ]
            ]);

        } catch(\Exception $e) {
            return json(['code' => 1, 'msg' => '获取入库信息失败: ' . $e->getMessage()]);
        }
    }

    //创建生产入库单
    public function create_inbound(){
        $input = input('post.');

        if(!isset_full($input, 'order_id') || !isset_full($input, 'inbound_qty') || !isset_full($input, 'warehouse_id')){
            return json(['code' => 1, 'msg' => '参数不完整']);
        }

        try {
            $order_id = $input['order_id'];
            $inbound_qty = floatval($input['inbound_qty']);
            $warehouse_id = intval($input['warehouse_id']);

            if($inbound_qty <= 0){
                return json(['code' => 1, 'msg' => '入库数量必须大于0']);
            }

            if($warehouse_id <= 0){
                return json(['code' => 1, 'msg' => '请选择有效的仓库']);
            }

            // 验证仓库是否存在（仓库表没有merchant字段，是全局的）
            $warehouse = db('warehouse')->where(['id' => $warehouse_id])->find();
            if(!$warehouse){
                return json(['code' => 1, 'msg' => '选择的仓库不存在']);
            }

            // 获取生产订单信息
            $order = ProductionOrderModel::with('goodsinfo')->where([
                'id' => $order_id,
                'merchant' => Session('is_merchant_id')
            ])->find();

            if(!$order){
                return json(['code' => 1, 'msg' => '生产订单不存在']);
            }

            // 验证可入库数量
            $inboundInfo = $this->getInboundInfo($order_id);
            if($inbound_qty > $inboundInfo['available_qty']){
                return json(['code' => 1, 'msg' => '入库数量超过可入库数量']);
            }

            // 开始事务
            db()->startTrans();

            try {
                // 创建入库单主表
                $inboundData = [
                    'merchant' => Session('is_merchant_id'),
                    'time' => time(),
                    'number' => get_number('SCRK'), // 生产入库单号
                    'pagetype' => 2, // 生产入库单
                    'production_order_id' => $order_id,
                    'user' => Session('is_user_id'),
                    'data' => '生产订单[' . $order['order_no'] . ']入库',
                    'type' => 0, // 未审核
                    'auditinguser' => 0,
                    'auditingtime' => 0,
                    'more' => json_encode([])
                ];

                $inboundClass = db('otpurchaseclass')->insertGetId($inboundData);

                // 创建入库单明细
                $inboundDetailData = [
                    'pid' => $inboundClass,
                    'goods' => $order['goods_id'],
                    'attr' => '',
                    'nums' => $inbound_qty,
                    'warehouse' => $warehouse_id, // 使用选择的仓库
                    'room' => 0,
                    'serial' => '',
                    'data' => '生产入库',
                    'more' => json_encode([])
                ];

                db('otpurchaseinfo')->insert($inboundDetailData);

                // 注意：不在这里处理仓储信息，让审核流程自动处理
                // 这样可以避免重复创建 is_roominfo 数据

                // 提交事务
                db()->commit();

                // 检查是否需要自动审核
                $autoAuditing = get_sys(['auto_auditing']);
                if(empty($autoAuditing)){
                    // 自动审核入库单
                    $this->autoAuditInbound($inboundClass);
                }

                return json([
                    'code' => 0,
                    'msg' => '生产入库单创建成功',
                    'data' => [
                        'inbound_id' => $inboundClass,
                        'inbound_number' => $inboundData['number']
                    ]
                ]);

            } catch(\Exception $e) {
                db()->rollback();
                throw $e;
            }

        } catch(\Exception $e) {
            return json(['code' => 1, 'msg' => '创建入库单失败: ' . $e->getMessage()]);
        }
    }

    //获取入库信息的私有方法
    private function getInboundInfo($order_id){
        // 获取合格数量
        $totalGoodQty = 0;
        try {
            $totalGoodQty = db('production_report')
                ->alias('pr')
                ->join('production_schedule ps', 'pr.schedule_id = ps.id')
                ->where([
                    'ps.production_order_id' => $order_id,
                    'pr.merchant' => Session('is_merchant_id')
                ])
                ->sum('pr.good_qty');
        } catch(\Exception $e) {
            // 如果生产报工表不存在，使用计划数量
            $order = ProductionOrderModel::where(['id' => $order_id])->find();
            $totalGoodQty = $order ? $order['plan_qty'] : 0;
        }

        // 获取已入库数量
        $alreadyInboundQty = 0;
        try {
            $alreadyInboundQty = db('otpurchaseclass')
                ->alias('opc')
                ->join('otpurchaseinfo opi', 'opc.id = opi.pid')
                ->where([
                    'opc.production_order_id' => $order_id,
                    'opc.pagetype' => 2, // 生产入库单
                    'opc.type' => 1, // 已审核
                    'opc.merchant' => Session('is_merchant_id')
                ])
                ->sum('opi.nums');
        } catch(\Exception $e) {
            $alreadyInboundQty = 0;
        }

        return [
            'total_good_qty' => floatval($totalGoodQty),
            'already_inbound_qty' => floatval($alreadyInboundQty),
            'available_qty' => max(0, floatval($totalGoodQty) - floatval($alreadyInboundQty))
        ];
    }

    //自动审核生产入库单
    private function autoAuditInbound($inboundId){
        try {
            // 调用其他入库单控制器的审核方法
            $otpurchaseController = new \app\index\controller\Otpurchase();
            $result = $otpurchaseController->auditing([$inboundId], true);

            if($result === true){
                push_log('自动审核生产入库单[ID:' . $inboundId . ']成功');
            }

        } catch(\Exception $e) {
            push_log('自动审核生产入库单[ID:' . $inboundId . ']失败: ' . $e->getMessage());
        }
    }
}
