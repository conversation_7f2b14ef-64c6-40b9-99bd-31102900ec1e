<?php
namespace app\index\model;
use think\Model;

class ProductionSchedule extends Model{
    //生产排产表
    protected $table = 'is_production_schedule';
    
    protected $type = [
        'more' => 'json'
    ];
    
    //关联生产订单
    public function orderinfo(){
        return $this->hasOne('app\index\model\ProductionOrder', 'id', 'production_order_id');
    }
    
    //关联设备信息
    public function machineinfo(){
        return $this->hasOne('app\index\model\InjectionMachine', 'id', 'machine_id');
    }

    //关联班次信息
    public function shiftinfo(){
        return $this->hasOne('app\index\model\WorkShift', 'id', 'shift_id');
    }

    //关联操作员信息
    public function operatorinfo(){
        return $this->hasOne('app\index\model\User', 'id', 'operator_id');
    }

    //关联排产人信息
    public function schedulerinfo(){
        return $this->hasOne('app\index\model\User', 'id', 'scheduler');
    }
    
    //状态读取器
    protected function getStatusAttr($val, $data){
        $status = [
            '0' => '待生产',
            '1' => '生产中',
            '2' => '已完成',
            '3' => '暂停',
            '4' => '取消'
        ];
        return isset($status[$val]) ? $status[$val] : '未知';
    }
    
    //状态原始值读取器
    protected function getStatusValueAttr($val, $data){
        return $data['status'];
    }
    
    //计划数量显示
    protected function getPlanQtyTextAttr($val, $data){
        return number_format($data['plan_qty'], 2);
    }
    
    //实际数量显示
    protected function getActualQtyTextAttr($val, $data){
        return number_format($data['actual_qty'], 2);
    }
    
    //合格数量显示
    protected function getGoodQtyTextAttr($val, $data){
        return number_format($data['good_qty'], 2);
    }
    
    //不良数量显示
    protected function getDefectQtyTextAttr($val, $data){
        return number_format($data['defect_qty'], 2);
    }
    
    //创建时间读取器
    protected function getCreatetimeAttr($val, $data){
        return $val ? date('Y-m-d H:i:s', $val) : '';
    }
    
    //计划开始时间读取器
    protected function getPlanStartTimeAttr($val, $data){
        return $val ? $val : '';
    }
    
    //计划结束时间读取器
    protected function getPlanEndTimeAttr($val, $data){
        return $val ? $val : '';
    }
    
    //实际开始时间读取器
    protected function getActualStartTimeAttr($val, $data){
        return $val ? $val : '';
    }
    
    //实际结束时间读取器
    protected function getActualEndTimeAttr($val, $data){
        return $val ? $val : '';
    }
    
    //获取完成进度
    public function getProgress(){
        if($this->plan_qty <= 0){
            return 0;
        }
        
        return round(($this->actual_qty / $this->plan_qty) * 100, 2);
    }
    
    //获取合格率
    public function getQualifiedRate(){
        if($this->actual_qty <= 0){
            return 0;
        }
        
        return round(($this->good_qty / $this->actual_qty) * 100, 2);
    }
    
    //获取不良率
    public function getDefectRate(){
        if($this->actual_qty <= 0){
            return 0;
        }
        
        return round(($this->defect_qty / $this->actual_qty) * 100, 2);
    }
    
    //获取计划工时
    public function getPlanHours(){
        if(!$this->plan_start_time || !$this->plan_end_time){
            return 0;
        }
        
        return round((strtotime($this->plan_end_time) - strtotime($this->plan_start_time)) / 3600, 2);
    }
    
    //获取实际工时
    public function getActualHours(){
        if(!$this->actual_start_time || !$this->actual_end_time){
            return 0;
        }
        
        return round((strtotime($this->actual_end_time) - strtotime($this->actual_start_time)) / 3600, 2);
    }
    
    //获取效率
    public function getEfficiency(){
        $plan_hours = $this->getPlanHours();
        $actual_hours = $this->getActualHours();
        
        if($actual_hours <= 0){
            return 0;
        }
        
        return round(($plan_hours / $actual_hours) * 100, 2);
    }
    
    //获取产能利用率
    public function getCapacityUtilization(){
        $plan_hours = $this->getPlanHours();
        $shift_hours = 8; //标准班次8小时
        
        if($shift_hours <= 0){
            return 0;
        }
        
        return round(($plan_hours / $shift_hours) * 100, 2);
    }
    
    //检查是否可以开始生产
    public function canStart(){
        if($this->status != 0){
            return ['can_start' => false, 'reason' => '排产状态不正确'];
        }
        
        //检查设备状态
        $machine = $this->machineinfo;
        if(!$machine || $machine->status != 1){
            return ['can_start' => false, 'reason' => '设备状态不可用'];
        }
        
        //检查时间
        $current_time = date('Y-m-d H:i:s');
        if($current_time < $this->plan_start_time){
            return ['can_start' => false, 'reason' => '未到计划开始时间'];
        }
        
        //检查操作员
        if(!$this->operator_id){
            return ['can_start' => false, 'reason' => '未安排操作员'];
        }
        
        return ['can_start' => true, 'reason' => ''];
    }
    
    //检查是否可以完工
    public function canFinish(){
        if($this->status != 1){
            return ['can_finish' => false, 'reason' => '排产状态不正确'];
        }
        
        if(!$this->actual_start_time){
            return ['can_finish' => false, 'reason' => '未开始生产'];
        }
        
        return ['can_finish' => true, 'reason' => ''];
    }
    
    //获取延期情况
    public function getDelayInfo(){
        $current_time = date('Y-m-d H:i:s');
        
        if($this->status == 2){
            //已完成，检查是否延期完成
            if($this->actual_end_time > $this->plan_end_time){
                $delay_hours = (strtotime($this->actual_end_time) - strtotime($this->plan_end_time)) / 3600;
                return [
                    'is_delayed' => true,
                    'delay_hours' => round($delay_hours, 2),
                    'delay_reason' => '完工延期'
                ];
            }
        } elseif($this->status == 1){
            //生产中，检查是否延期
            if($current_time > $this->plan_end_time){
                $delay_hours = (strtotime($current_time) - strtotime($this->plan_end_time)) / 3600;
                return [
                    'is_delayed' => true,
                    'delay_hours' => round($delay_hours, 2),
                    'delay_reason' => '生产延期'
                ];
            }
        } elseif($this->status == 0){
            //待生产，检查是否延期开始
            if($current_time > $this->plan_start_time){
                $delay_hours = (strtotime($current_time) - strtotime($this->plan_start_time)) / 3600;
                return [
                    'is_delayed' => true,
                    'delay_hours' => round($delay_hours, 2),
                    'delay_reason' => '开工延期'
                ];
            }
        }
        
        return ['is_delayed' => false, 'delay_hours' => 0, 'delay_reason' => ''];
    }
    
    //获取预计完成时间
    public function getEstimatedFinishTime(){
        if($this->status == 2){
            return $this->actual_end_time;
        }
        
        if($this->status == 1 && $this->actual_start_time){
            //根据当前进度估算
            $progress = $this->getProgress();
            if($progress > 0){
                $elapsed_time = time() - strtotime($this->actual_start_time);
                $total_estimated_time = $elapsed_time / ($progress / 100);
                $estimated_finish = date('Y-m-d H:i:s', strtotime($this->actual_start_time) + $total_estimated_time);
                return $estimated_finish;
            }
        }
        
        return $this->plan_end_time;
    }
    
    //获取排产冲突
    public function getConflicts(){
        $conflicts = db('schedule_conflict')
            ->where([
                'schedule_id_1' => $this->id,
                'status' => 1
            ])
            ->whereOr([
                'schedule_id_2' => $this->id,
                'status' => 1
            ])
            ->select();
            
        return $conflicts;
    }
    
    //更新排产状态
    public function updateStatus($new_status, $data = []){
        $this->status = $new_status;
        
        switch($new_status){
            case 1: //开始生产
                $this->actual_start_time = date('Y-m-d H:i:s');
                break;
            case 2: //完成生产
                $this->actual_end_time = date('Y-m-d H:i:s');
                if(isset($data['actual_qty'])){
                    $this->actual_qty = $data['actual_qty'];
                }
                if(isset($data['good_qty'])){
                    $this->good_qty = $data['good_qty'];
                }
                if(isset($data['defect_qty'])){
                    $this->defect_qty = $data['defect_qty'];
                }
                break;
        }
        
        $this->save();
        
        //更新关联订单状态
        $order = $this->orderinfo;
        if($order){
            $order->updateStatus();
        }
    }
}
