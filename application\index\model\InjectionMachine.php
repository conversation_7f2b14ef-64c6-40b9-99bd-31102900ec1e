<?php
namespace app\index\model;
use think\Model;

class InjectionMachine extends Model{
    //注塑机设备表
    protected $table = 'is_injection_machine';
    
    protected $type = [
        'more' => 'json'
    ];
    
    //状态读取器
    protected function getStatusAttr($val, $data){
        $status = ['0' => '停用', '1' => '启用', '2' => '维修'];
        return isset($status[$val]) ? $status[$val] : '未知';
    }
    
    //状态原始值读取器
    protected function getStatusValueAttr($val, $data){
        return $data['status'];
    }
    
    //创建时间读取器
    protected function getCreatetimeAttr($val, $data){
        return $val ? date('Y-m-d H:i:s', $val) : '';
    }
    
    //创建时间原始值读取器
    protected function getCreatetimeValueAttr($val, $data){
        return $data['createtime'];
    }
    
    //购买日期读取器
    protected function getPurchaseDateAttr($val, $data){
        return $val ? $val : '';
    }
    
    //保修期读取器
    protected function getWarrantyDateAttr($val, $data){
        return $val ? $val : '';
    }
    
    //锁模力显示
    protected function getTonnageTextAttr($val, $data){
        return $data['tonnage'] > 0 ? $data['tonnage'] . 'T' : '';
    }
    
    //最大射胶量显示
    protected function getMaxShotWeightTextAttr($val, $data){
        return $data['max_shot_weight'] > 0 ? $data['max_shot_weight'] . 'g' : '';
    }
    
    //关联维护记录
    public function maintenanceRecords(){
        return $this->hasMany('app\index\model\MachineMaintenance', 'machine_id', 'id')->order('maintenance_date desc');
    }

    //关联生产排产
    public function schedules(){
        return $this->hasMany('app\index\model\ProductionSchedule', 'machine_id', 'id');
    }
    
    //获取设备当前状态
    public function getCurrentStatus(){
        //检查是否有进行中的排产
        $current_schedule = db('production_schedule')
            ->where([
                'machine_id' => $this->id,
                'status' => ['in', [1, 3]], //生产中或暂停
                'schedule_date' => date('Y-m-d')
            ])
            ->find();
            
        if($current_schedule){
            return $current_schedule['status'] == 1 ? '生产中' : '暂停';
        }
        
        //检查是否有维修记录
        $maintenance = db('machine_maintenance')
            ->where([
                'machine_id' => $this->id,
                'status' => 1, //进行中
                'maintenance_date' => date('Y-m-d')
            ])
            ->find();
            
        if($maintenance){
            return '维修中';
        }
        
        return $this->status_text;
    }
    
    //获取设备今日效率
    public function getTodayEfficiency(){
        $today = date('Y-m-d');
        $summary = db('production_summary')
            ->where([
                'machine_id' => $this->id,
                'summary_date' => $today
            ])
            ->find();
            
        return $summary ? $summary['efficiency'] : 0;
    }
    
    //获取设备本月产量
    public function getMonthlyOutput(){
        $start_date = date('Y-m-01');
        $end_date = date('Y-m-d');
        
        $output = db('production_summary')
            ->where([
                'machine_id' => $this->id,
                'summary_date' => ['between', [$start_date, $end_date]]
            ])
            ->sum('actual_qty');
            
        return $output ? $output : 0;
    }
    
    //获取设备维护提醒
    public function getMaintenanceAlert(){
        //获取最后维护日期
        $last_maintenance = db('machine_maintenance')
            ->where(['machine_id' => $this->id])
            ->order('maintenance_date desc')
            ->value('maintenance_date');
            
        if($last_maintenance){
            $days_since = (strtotime(date('Y-m-d')) - strtotime($last_maintenance)) / 86400;
            
            //超过30天提醒保养
            if($days_since > 30){
                return [
                    'level' => 'warning',
                    'message' => '设备已' . $days_since . '天未保养，建议进行维护'
                ];
            }
            
            //超过60天警告
            if($days_since > 60){
                return [
                    'level' => 'danger',
                    'message' => '设备已' . $days_since . '天未保养，请立即安排维护'
                ];
            }
        }else{
            return [
                'level' => 'info',
                'message' => '暂无维护记录，建议建立维护计划'
            ];
        }
        
        return null;
    }
}
