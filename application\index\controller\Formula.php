<?php
namespace app\index\controller;
use app\index\controller\Acl;
use app\index\model\ProductionFormula;
use app\index\model\FormulaDetail;

class Formula extends Acl {
    //生产配方管理模块
    
    //主视图
    public function main(){
        return $this->fetch();
    }

    //表单页面
    public function form(){
        $id = input('id', 0);
        $formula = [];
        if($id > 0){
            $formula = ProductionFormula::get($id, ['goodsinfo']);
            if(!$formula){
                $this->error('配方不存在!');
            }
            $formula = $formula->toArray();

            // 获取配方明细
            $details = FormulaDetail::where(['formula_id' => $id])
                ->order('sort asc, id asc')
                ->select()
                ->toArray();

            // 转换物料类型为中文显示
            foreach($details as &$detail){
                $detail['material_type'] = $this->convertMaterialTypeToChina($detail['material_type']);
            }

            $formula['details'] = $details;
        }
        $this->assign('formula', $formula);
        return $this->fetch();
    }

    //详情页面
    public function info(){
        $id = input('id', 0);
        if($id <= 0){
            $this->error('参数错误!');
        }

        $formula = ProductionFormula::get($id, ['goodsinfo', 'creatorinfo', 'auditorinfo']);
        if(!$formula){
            $this->error('配方不存在!');
        }

        // 获取配方明细
        $details = FormulaDetail::where(['formula_id' => $id])
            ->order('sort asc, id asc')
            ->select()
            ->toArray();

        // 转换物料类型为中文显示
        foreach($details as &$detail){
            $detail['material_type'] = $this->convertMaterialTypeToChina($detail['material_type']);
        }

        $formula = $formula->toArray();
        $formula['details'] = $details;

        $this->assign('formula', $formula);
        return $this->fetch();
    }
    
    //获取配方列表
    public function get_list(){
        $input = input('post.');
        $where = auth('production_formula', []);
        
        //搜索条件
        if(isset_full($input, 'name')){
            $where[] = ['name', 'like', '%'.$input['name'].'%'];
        }
        if(isset_full($input, 'code')){
            $where[] = ['code', 'like', '%'.$input['code'].'%'];
        }
        if(isset_full($input, 'status')){
            $where[] = ['status', '=', $input['status']];
        }
        if(isset_full($input, 'goods_id')){
            $where[] = ['goods_id', '=', $input['goods_id']];
        }
        
        $list = ProductionFormula::with(['goodsinfo', 'creatorinfo', 'auditorinfo'])
            ->where($where)
            ->order('id desc')
            ->paginate(input('limit', 15))
            ->toArray();
            
        return json(['code' => 0, 'msg' => '', 'count' => $list['total'], 'data' => $list['data']]);
    }
    
    //新增|更新配方
    public function set(){
        $input = input('post.');
        
        // 添加调试信息
        \think\Log::write('接收到的POST数据: ' . json_encode($input, JSON_UNESCAPED_UNICODE), 'info');

        // 处理details数据格式
        $details = [];

        // 检查是否有details[x][field]格式的数据
        $has_form_details = false;
        foreach($input as $key => $value){
            if(strpos($key, 'details[') === 0){
                $has_form_details = true;
                preg_match('/details\[(\d+)\]\[(\w+)\]/', $key, $matches);
                if($matches){
                    $index = intval($matches[1]);
                    $field = $matches[2];
                    $details[$index][$field] = $value;
                }
            }
        }

        // 如果没有找到表单格式的details，检查是否有直接的details数组
        if(!$has_form_details && isset($input['details']) && is_array($input['details'])){
            $details = $input['details'];
        }

        // 确保details数组的索引是连续的
        if(!empty($details)){
            $details = array_values($details);
        }

        // 将处理后的details重新赋值给input
        $input['details'] = $details;

        \think\Log::write('处理后的details数据: ' . json_encode($details, JSON_UNESCAPED_UNICODE), 'info');
        \think\Log::write('details数组长度: ' . count($details), 'info');
        \think\Log::write('has_form_details: ' . ($has_form_details ? 'true' : 'false'), 'info');

        if(isset($input['id'])){
            //验证配方明细
            if(!empty($details)){
                foreach ($details as $detail_key => $detail_vo) {
                    if(empty($detail_vo['material_name']) || empty($detail_vo['ratio'])){
                        return json(['state' => 'error', 'info' => '配方明细第'.($detail_key+1).'行数据不完整']);
                    }
                }
            }else{
                return json(['state' => 'error', 'info' => '配方明细不可为空!']);
            }
           
            if(empty($input['id'])){
                //新增
                $input['merchant'] = Session('is_merchant_id');
                $input['creator'] = Session('is_user_id');
                $input['createtime'] = time();

                // 验证时排除details字段
                $validate_data = $input;
                unset($validate_data['details']);
                $vali = $this->validate($validate_data, 'ProductionFormula');
                if($vali === true){
                    //开启事务
                    db()->startTrans();
                    try {
                        // 创建配方时排除details字段
                        $formula_data = $input;
                        unset($formula_data['details']);
                        $formula = ProductionFormula::create($formula_data);
                        
                        //保存配方明细
                        foreach ($input['details'] as $detail) {
                            // 调试：输出原始明细数据
                            \think\Log::write('原始明细数据: ' . json_encode($detail, JSON_UNESCAPED_UNICODE), 'info');

                            $detail_data = [
                                'formula_id' => $formula->id,
                                'material_type' => isset($detail['material_type']) ? $this->convertMaterialType($detail['material_type']) : 'raw',
                                'material_name' => $detail['material_name'],
                                'material_code' => isset($detail['material_code']) ? $detail['material_code'] : '',
                                'ratio' => $detail['ratio'],
                                'weight_per_kg' => isset($detail['weight_per_kg']) ? $detail['weight_per_kg'] : 0,
                                'sort' => $detail['sort'],
                                'data' => isset($detail['data']) ? $detail['data'] : ''
                            ];

                            // 调试：输出处理后的数据
                            \think\Log::write('处理后明细数据: ' . json_encode($detail_data, JSON_UNESCAPED_UNICODE), 'info');

                            FormulaDetail::create($detail_data);
                        }
                        
                        db()->commit();
                        push_log('新增生产配方[ '.$formula['name'].' ]');
                        $result = ['state' => 'success'];
                    } catch (\Exception $e) {
                        db()->rollback();
                        $result = ['state' => 'error', 'info' => '保存失败：' . $e->getMessage()];
                    }
                }else{
                    $result = ['state' => 'error', 'info' => $vali];
                }
            }else{
                //更新
                // 验证时排除details字段
                $validate_data = $input;
                unset($validate_data['details']);
                $vali = $this->validate($validate_data, 'ProductionFormula');
                if($vali === true){
                 
                    //开启事务
                    db()->startTrans();
                    try {
                        // 更新配方时排除details字段
                        $formula_data = $input;
                        unset($formula_data['details']);
                        $formula = ProductionFormula::update($formula_data);
                      
                        //删除原有明细
                        FormulaDetail::where(['formula_id' => $input['id']])->delete();
                        
                        //保存新明细
                        foreach ($input['details'] as $detail) {
                            // 调试：输出原始明细数据
                            \think\Log::write('更新-原始明细数据: ' . json_encode($detail, JSON_UNESCAPED_UNICODE), 'info');

                            $detail_data = [
                                'formula_id' => $input['id'],
                                'material_type' => isset($detail['material_type']) ? $this->convertMaterialType($detail['material_type']) : 'raw',
                                'material_name' => $detail['material_name'],
                                'material_code' => isset($detail['material_code']) ? $detail['material_code'] : '',
                                'ratio' => $detail['ratio'],
                                'weight_per_kg' => isset($detail['weight_per_kg']) ? $detail['weight_per_kg'] : 0,
                                'sort' => $detail['sort'],
                                'data' => isset($detail['data']) ? $detail['data'] : ''
                            ];

                            // 调试：输出处理后的数据
                            \think\Log::write('更新-处理后明细数据: ' . json_encode($detail_data, JSON_UNESCAPED_UNICODE), 'info');

                            FormulaDetail::create($detail_data);
                        }
                        
                        db()->commit();
                        push_log('更新生产配方[ '.$formula['name'].' ]');
                        $result = ['state' => 'success'];
                    } catch (\Exception $e) {
                        db()->rollback();
                        $result = ['state' => 'error', 'info' => '更新失败：' . $e->getMessage()];
                    }
                }else{
                    $result = ['state' => 'error', 'info' => $vali];
                }
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //删除配方
    public function del(){
        $input = input('post.');
        if(isset_full($input, 'id')){
            $formula = ProductionFormula::find($input['id']);
            if($formula){
                //检查是否有关联的生产订单
                $order_count = db('production_order')->where(['formula_id' => $input['id']])->count();
                if($order_count > 0){
                    return json(['state' => 'error', 'info' => '该配方已有生产订单使用，无法删除!']);
                }
                
                //开启事务
                db()->startTrans();
                try {
                    //删除配方明细
                    FormulaDetail::where(['formula_id' => $input['id']])->delete();
                    //删除配方
                    $formula->delete();
                    
                    db()->commit();
                    push_log('删除生产配方[ '.$formula['name'].' ]');
                    $result = ['state' => 'success'];
                } catch (\Exception $e) {
                    db()->rollback();
                    $result = ['state' => 'error', 'info' => '删除失败：' . $e->getMessage()];
                }
            }else{
                $result = ['state' => 'error', 'info' => '配方不存在!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //获取配方详情
    public function get_info(){
        $input = input('post.');
        if(isset_full($input, 'id')){
            $formula = ProductionFormula::get($input['id'], ['goodsinfo', 'creatorinfo', 'auditorinfo']);
            if($formula){
                //获取配方明细
                $details = FormulaDetail::where(['formula_id' => $input['id']])
                    ->order('sort asc, id asc')
                    ->select()
                    ->toArray();
                    
                $formula = $formula->toArray();
                $formula['details'] = $details;
                
                $result = ['state' => 'success', 'data' => $formula];
            }else{
                $result = ['state' => 'error', 'info' => '配方不存在!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //审核配方
    public function audit(){
        $input = input('post.');
        if(isset($input['id']) && isset($input['status']) && $input['id'] > 0 && in_array($input['status'], ['0', '1', 0, 1])){
            $formula = ProductionFormula::get($input['id']);
            if($formula){
                $formula->status = $input['status'];
                if($input['status'] == 1){
                    $formula->auditor = Session('is_user_id');
                    $formula->audit_time = time();
                }
                $formula->save();

                $status_text = $input['status'] == 1 ? '启用' : '停用';
                push_log('配方[ '.$formula['name'].' ]'.$status_text);
                $result = ['state' => 'success'];
            }else{
                $result = ['state' => 'error', 'info' => '配方不存在!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }

        return json($result);
    }
    
    //获取可用配方列表(用于下拉选择)
    public function get_available(){
        $input = input('post.');

        try {
            // 构建基础查询条件
            $where = [];
            $where['merchant'] = Session('is_merchant_id');
            $where['status'] = 1; //只获取启用的配方

            if(isset_full($input, 'goods_id')){
                $where['goods_id'] = $input['goods_id'];
            }

            $list = db('production_formula')
                ->where($where)
                ->field('id,name,code,goods_id,version')
                ->order('name asc')
                ->select();

            // 如果需要商品名称，再次查询
            if($list){
                foreach($list as &$item){
                    $goods = db('goods')->where('id', $item['goods_id'])->field('name')->find();
                    $item['goods_name'] = $goods ? $goods['name'] : '';
                }
            }

            return json(['state' => 'success', 'data' => $list]);
        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '查询失败: ' . $e->getMessage()]);
        }
    }
    
    //复制配方
    public function copy(){
        $input = input('post.');
        if(isset_full($input, 'id')){
            $original = ProductionFormula::get($input['id']);
            if($original){
                //获取原配方明细
                $details = FormulaDetail::where(['formula_id' => $input['id']])->select()->toArray();
                
                //开启事务
                db()->startTrans();
                try {
                    //创建新配方
                    $new_formula = $original->toArray();
                    unset($new_formula['id']);
                    $new_formula['name'] = $new_formula['name'] . '_副本';
                    $new_formula['code'] = $new_formula['code'] . '_COPY';
                    $new_formula['status'] = 0; //待审核
                    $new_formula['creator'] = Session('is_user_id');
                    $new_formula['auditor'] = null;
                    $new_formula['audit_time'] = null;
                    $new_formula['createtime'] = time();
                    
                    $formula = ProductionFormula::create($new_formula);
                    
                    //复制明细
                    foreach ($details as $detail) {
                        unset($detail['id']);
                        $detail['formula_id'] = $formula->id;
                        FormulaDetail::create($detail);
                    }
                    
                    db()->commit();
                    push_log('复制生产配方[ '.$original['name'].' ]为[ '.$formula['name'].' ]');
                    $result = ['state' => 'success', 'data' => ['id' => $formula->id]];
                } catch (\Exception $e) {
                    db()->rollback();
                    $result = ['state' => 'error', 'info' => '复制失败：' . $e->getMessage()];
                }
            }else{
                $result = ['state' => 'error', 'info' => '原配方不存在!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }

    //配方使用统计
    public function usage_stats(){
        $input = input('post.');
        $formula_id = isset($input['formula_id']) ? $input['formula_id'] : 0;
        $start_date = isset($input['start_date']) ? $input['start_date'] : date('Y-m-01');
        $end_date = isset($input['end_date']) ? $input['end_date'] : date('Y-m-d');

        if($formula_id <= 0){
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        $where = auth('production_schedule', []);

        $stats = db('production_schedule')
            ->alias('ps')
            ->join('production_order po', 'ps.production_order_id = po.id', 'left')
            ->join('goods g', 'po.goods_id = g.id', 'left')
            ->where($where)
            ->where('ps.schedule_date', 'between', [$start_date, $end_date])
            ->where('po.formula_id', '=', $formula_id)
            ->field('po.order_no,
                     g.name as goods_name,
                     ps.plan_qty,
                     ps.actual_qty,
                     ps.schedule_date,
                     (ps.actual_qty/ps.plan_qty*100) as completion_rate')
            ->order('ps.schedule_date desc')
            ->paginate(input('limit', 15))
            ->toArray();

        return json(['code' => 0, 'msg' => '', 'count' => $stats['total'], 'data' => $stats['data']]);
    }

    //获取商品列表(用于原料选择)
    public function get_goods_list(){
        $input = input('post.');
        $keyword = isset($input['keyword']) ? trim($input['keyword']) : '';

        $where = auth('goods', []);

        // 搜索条件
        if(!empty($keyword)){
            $where[] = ['name|py|number|code|spec', 'like', '%'.$keyword.'%'];
        }

        $list = db('goods')
            ->alias('g')
            ->join('unit u', 'g.unit = u.id', 'left')
            ->join('brand b', 'g.brand = b.id', 'left')
            ->join('goodsclass gc', 'g.class = gc.id', 'left')
            ->where($where)
            ->field('g.id,g.name,g.number,g.spec,g.unit as unit_id,u.name as unit_name,
                     b.name as brand_name,g.buy,g.sell,g.data,g.class as class_id,gc.name as class_name')
            ->order('g.name asc')
            ->limit(50) // 限制返回数量，避免数据过多
            ->select();

        return json(['code' => 0, 'msg' => '', 'data' => $list]);
    }

    //中文物料类型转换为英文代码
    private function convertMaterialType($chinese_type){
        $type_map = [
            '原料' => 'raw',
            '色膏' => 'colorant',
            '颜料' => 'colorant',
            '色料' => 'colorant',
            '染料' => 'colorant',
            '添加剂' => 'additive',
            '助剂' => 'additive',
            '催化剂' => 'additive',
            '固化剂' => 'additive',
            '脱模剂' => 'additive',
            '稳定剂' => 'additive'
        ];

        return isset($type_map[$chinese_type]) ? $type_map[$chinese_type] : 'raw';
    }

    //英文代码转换为中文物料类型
    private function convertMaterialTypeToChina($english_type){
        $type_map = [
            'raw' => '原料',
            'colorant' => '色膏',
            'additive' => '添加剂'
        ];

        return isset($type_map[$english_type]) ? $type_map[$english_type] : '原料';
    }
}
