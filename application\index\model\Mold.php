<?php
namespace app\index\model;
use think\Model;

class Mold extends Model{
    //模具管理表
    protected $table = 'is_mold';
    
    protected $type = [
        'more' => 'json'
    ];
    
    //关联商品信息
    public function goodsinfo(){
        return $this->hasOne('app\index\model\Goods', 'id', 'goods_id');
    }
    
    //状态读取器
    protected function getStatusAttr($val, $data){
        $status = ['0' => '停用', '1' => '正常', '2' => '维修', '3' => '报废'];
        return isset($status[$val]) ? $status[$val] : '未知';
    }
    
    //状态原始值读取器
    protected function getStatusValueAttr($val, $data){
        return $data['status'];
    }
    
    //创建时间读取器
    protected function getCreatetimeAttr($val, $data){
        return $val ? date('Y-m-d H:i:s', $val) : '';
    }
    
    //创建时间原始值读取器
    protected function getCreatetimeValueAttr($val, $data){
        return $data['createtime'];
    }
    
    //最后维护日期读取器
    protected function getLastMaintenanceAttr($val, $data){
        return $val ? $val : '';
    }
    
    //穴数显示
    protected function getCavityCountTextAttr($val, $data){
        return $data['cavity_count'] . '穴';
    }
    
    //周期时间显示
    protected function getCycleTimeTextAttr($val, $data){
        return $data['cycle_time'] > 0 ? $data['cycle_time'] . 's' : '';
    }
    
    //单次射胶重量显示
    protected function getWeightPerShotTextAttr($val, $data){
        return $data['weight_per_shot'] > 0 ? $data['weight_per_shot'] . 'g' : '';
    }
    
    //累计射胶次数显示
    protected function getTotalShotsTextAttr($val, $data){
        return number_format($data['total_shots']);
    }
    
    //关联生产订单
    public function orders(){
        return $this->hasMany('app\index\model\ProductionOrder', 'mold_id', 'id');
    }
    
    //获取模具当前状态
    public function getCurrentStatus(){
        //检查是否有进行中的生产订单
        $current_order = db('production_order')
            ->alias('po')
            ->join('production_schedule ps', 'po.id = ps.production_order_id', 'left')
            ->where([
                'po.mold_id' => $this->id,
                'ps.status' => ['in', [1, 3]], //生产中或暂停
                'ps.schedule_date' => date('Y-m-d')
            ])
            ->find();
            
        if($current_order){
            return $current_order['status'] == 1 ? '生产中' : '暂停';
        }
        
        return $this->status_text;
    }
    
    //获取模具本月使用次数
    public function getMonthlyUsage(){
        $start_date = date('Y-m-01');
        $end_date = date('Y-m-d');
        
        $usage = db('production_schedule')
            ->alias('ps')
            ->join('production_order po', 'ps.production_order_id = po.id', 'left')
            ->where([
                'po.mold_id' => $this->id,
                'ps.schedule_date' => ['between', [$start_date, $end_date]]
            ])
            ->count();
            
        return $usage;
    }
    
    //获取模具本月产量
    public function getMonthlyOutput(){
        $start_date = date('Y-m-01');
        $end_date = date('Y-m-d');
        
        $output = db('production_schedule')
            ->alias('ps')
            ->join('production_order po', 'ps.production_order_id = po.id', 'left')
            ->where([
                'po.mold_id' => $this->id,
                'ps.schedule_date' => ['between', [$start_date, $end_date]]
            ])
            ->sum('ps.actual_qty');
            
        return $output ? $output : 0;
    }
    
    //获取模具维护提醒
    public function getMaintenanceAlert(){
        if($this->last_maintenance){
            $days_since = (strtotime(date('Y-m-d')) - strtotime($this->last_maintenance)) / 86400;
            
            //超过15天提醒保养
            if($days_since > 15){
                return [
                    'level' => 'warning',
                    'message' => '模具已' . $days_since . '天未保养，建议进行维护'
                ];
            }
            
            //超过30天警告
            if($days_since > 30){
                return [
                    'level' => 'danger',
                    'message' => '模具已' . $days_since . '天未保养，请立即安排维护'
                ];
            }
        }else{
            return [
                'level' => 'info',
                'message' => '暂无维护记录，建议建立维护计划'
            ];
        }
        
        return null;
    }
    
    //获取模具寿命预警
    public function getLifeAlert(){
        //假设模具寿命为100万次射胶
        $life_limit = 1000000;
        $usage_rate = $this->total_shots / $life_limit * 100;
        
        if($usage_rate > 90){
            return [
                'level' => 'danger',
                'message' => '模具使用率已达' . round($usage_rate, 1) . '%，接近报废'
            ];
        }elseif($usage_rate > 80){
            return [
                'level' => 'warning',
                'message' => '模具使用率已达' . round($usage_rate, 1) . '%，请关注'
            ];
        }elseif($usage_rate > 60){
            return [
                'level' => 'info',
                'message' => '模具使用率' . round($usage_rate, 1) . '%，状态良好'
            ];
        }
        
        return null;
    }
    
    //更新射胶次数
    public function updateShotCount($shots){
        $this->total_shots += $shots;
        $this->save();
    }
    
    //计算理论产能(每小时)
    public function getTheoreticalCapacity(){
        if($this->cycle_time > 0){
            return round(3600 / $this->cycle_time * $this->cavity_count, 0);
        }
        return 0;
    }
    
    //获取模具效率统计
    public function getEfficiencyStats($start_date, $end_date){
        $stats = db('production_schedule')
            ->alias('ps')
            ->join('production_order po', 'ps.production_order_id = po.id', 'left')
            ->where([
                'po.mold_id' => $this->id,
                'ps.schedule_date' => ['between', [$start_date, $end_date]],
                'ps.status' => 2 //已完成
            ])
            ->field('AVG(ps.actual_qty/ps.plan_qty*100) as avg_efficiency,
                     SUM(ps.actual_qty) as total_output,
                     COUNT(ps.id) as total_schedules')
            ->find();
            
        return $stats;
    }
}
