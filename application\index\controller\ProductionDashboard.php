<?php
namespace app\index\controller;
use app\index\controller\Acl;

class ProductionDashboard extends Acl {
    //生产看板模块
    
    //主视图
    public function main(){
        return $this->fetch();
    }
    
    //获取生产概览数据
    public function get_overview(){
        $today = date('Y-m-d');
        $merchant_id = Session('is_merchant_id');
        
        //今日生产统计
        $today_stats = $this->getTodayStats($merchant_id, $today);
        
        //设备状态统计
        $machine_stats = $this->getMachineStats($merchant_id);
        
        //订单状态统计
        $order_stats = $this->getOrderStats($merchant_id);
        
        //质量统计
        $quality_stats = $this->getQualityStats($merchant_id, $today);
        
        return json([
            'state' => 'success',
            'data' => [
                'today_stats' => $today_stats,
                'machine_stats' => $machine_stats,
                'order_stats' => $order_stats,
                'quality_stats' => $quality_stats
            ]
        ]);
    }
    
    //获取实时生产状态
    public function get_realtime_status(){
        $today = date('Y-m-d');
        $merchant_id = Session('is_merchant_id');
        
        //当前生产中的任务
        $producing_tasks = db('production_schedule')
            ->alias('ps')
            ->join('production_order po', 'ps.production_order_id = po.id', 'left')
            ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
            ->join('goods g', 'po.goods_id = g.id', 'left')
            ->join('user u', 'ps.operator_id = u.id', 'left')
            ->join('work_shift ws', 'ps.shift_id = ws.id', 'left')
            ->where([
                'ps.merchant' => $merchant_id,
                'ps.schedule_date' => $today,
                'ps.status' => ['in', [1, 3]] //生产中或暂停
            ])
            ->field('ps.*,po.order_no,im.name as machine_name,g.name as goods_name,
                     u.name as operator_name,ws.name as shift_name')
            ->select()
            ->toArray();
            
        //计算进度
        foreach($producing_tasks as &$task){
            if($task['plan_qty'] > 0){
                $task['progress'] = round(($task['actual_qty'] / $task['plan_qty']) * 100, 1);
            }else{
                $task['progress'] = 0;
            }
            
            //计算预计剩余时间
            if($task['actual_start_time'] && $task['actual_qty'] > 0){
                $elapsed_time = time() - strtotime($task['actual_start_time']);
                $rate = $task['actual_qty'] / ($elapsed_time / 3600); //每小时产量
                if($rate > 0){
                    $remaining_qty = $task['plan_qty'] - $task['actual_qty'];
                    $remaining_hours = $remaining_qty / $rate;
                    $task['estimated_remaining'] = round($remaining_hours, 1);
                }else{
                    $task['estimated_remaining'] = 0;
                }
            }else{
                $task['estimated_remaining'] = 0;
            }
        }
        
        return json(['state' => 'success', 'data' => $producing_tasks]);
    }
    
    //获取设备状态监控
    public function get_machine_monitor(){
        $merchant_id = Session('is_merchant_id');
        
        $machines = db('injection_machine')
            ->where(['merchant' => $merchant_id])
            ->select()
            ->toArray();
            
        foreach($machines as &$machine){
            //获取当前状态
            $current_schedule = db('production_schedule')
                ->alias('ps')
                ->join('production_order po', 'ps.production_order_id = po.id', 'left')
                ->join('goods g', 'po.goods_id = g.id', 'left')
                ->where([
                    'ps.machine_id' => $machine['id'],
                    'ps.schedule_date' => date('Y-m-d'),
                    'ps.status' => ['in', [1, 3]]
                ])
                ->field('ps.*,po.order_no,g.name as goods_name')
                ->find();
                
            if($current_schedule){
                $machine['current_task'] = $current_schedule;
                $machine['working_status'] = $current_schedule['status'] == 1 ? '生产中' : '暂停';
            }else{
                $machine['current_task'] = null;
                $machine['working_status'] = '空闲';
            }
            
            //获取今日效率
            $today_summary = db('production_summary')
                ->where([
                    'machine_id' => $machine['id'],
                    'summary_date' => date('Y-m-d')
                ])
                ->field('SUM(actual_qty) as total_qty, AVG(efficiency) as avg_efficiency')
                ->find();
                
            $machine['today_output'] = $today_summary['total_qty'] ?: 0;
            $machine['today_efficiency'] = $today_summary['avg_efficiency'] ?: 0;
        }
        
        return json(['state' => 'success', 'data' => $machines]);
    }
    
    //获取生产趋势数据
    public function get_production_trend(){
        $merchant_id = Session('is_merchant_id');
        $days = input('days', 7); //默认7天
        
        $dates = [];
        $output_data = [];
        $efficiency_data = [];
        
        for($i = $days - 1; $i >= 0; $i--){
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $dates[] = $date;
            
            $summary = db('production_summary')
                ->where([
                    'merchant' => $merchant_id,
                    'summary_date' => $date
                ])
                ->field('SUM(actual_qty) as total_output, AVG(efficiency) as avg_efficiency')
                ->find();
                
            $output_data[] = $summary['total_output'] ?: 0;
            $efficiency_data[] = round($summary['avg_efficiency'] ?: 0, 1);
        }
        
        return json([
            'state' => 'success',
            'data' => [
                'dates' => $dates,
                'output' => $output_data,
                'efficiency' => $efficiency_data
            ]
        ]);
    }
    
    //获取质量趋势数据
    public function get_quality_trend(){
        $merchant_id = Session('is_merchant_id');
        $days = input('days', 7);
        
        $dates = [];
        $qualified_rate = [];
        $defect_rate = [];
        
        for($i = $days - 1; $i >= 0; $i--){
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $dates[] = $date;
            
            $summary = db('production_summary')
                ->where([
                    'merchant' => $merchant_id,
                    'summary_date' => $date
                ])
                ->field('SUM(actual_qty) as total_qty, SUM(good_qty) as good_qty, SUM(defect_qty) as defect_qty')
                ->find();
                
            if($summary['total_qty'] > 0){
                $qualified_rate[] = round(($summary['good_qty'] / $summary['total_qty']) * 100, 1);
                $defect_rate[] = round(($summary['defect_qty'] / $summary['total_qty']) * 100, 1);
            }else{
                $qualified_rate[] = 0;
                $defect_rate[] = 0;
            }
        }
        
        return json([
            'state' => 'success',
            'data' => [
                'dates' => $dates,
                'qualified_rate' => $qualified_rate,
                'defect_rate' => $defect_rate
            ]
        ]);
    }
    
    //获取今日统计
    private function getTodayStats($merchant_id, $today){
        $stats = db('production_summary')
            ->where([
                'merchant' => $merchant_id,
                'summary_date' => $today
            ])
            ->field('SUM(plan_qty) as plan_total, SUM(actual_qty) as actual_total,
                     SUM(good_qty) as good_total, SUM(defect_qty) as defect_total,
                     AVG(completion_rate) as avg_completion, AVG(qualified_rate) as avg_qualified')
            ->find();
            
        return [
            'plan_qty' => $stats['plan_total'] ?: 0,
            'actual_qty' => $stats['actual_total'] ?: 0,
            'good_qty' => $stats['good_total'] ?: 0,
            'defect_qty' => $stats['defect_total'] ?: 0,
            'completion_rate' => round($stats['avg_completion'] ?: 0, 1),
            'qualified_rate' => round($stats['avg_qualified'] ?: 0, 1)
        ];
    }
    
    //获取设备统计
    private function getMachineStats($merchant_id){
        $total = db('injection_machine')->where(['merchant' => $merchant_id])->count();
        $normal = db('injection_machine')->where(['merchant' => $merchant_id, 'status' => 1])->count();
        $maintenance = db('injection_machine')->where(['merchant' => $merchant_id, 'status' => 2])->count();
        $disabled = db('injection_machine')->where(['merchant' => $merchant_id, 'status' => 0])->count();
        
        //当前生产中的设备
        $producing = db('production_schedule')
            ->where([
                'merchant' => $merchant_id,
                'schedule_date' => date('Y-m-d'),
                'status' => 1
            ])
            ->count('DISTINCT machine_id');
            
        return [
            'total' => $total,
            'normal' => $normal,
            'maintenance' => $maintenance,
            'disabled' => $disabled,
            'producing' => $producing,
            'idle' => $normal - $producing
        ];
    }
    
    //获取订单统计
    private function getOrderStats($merchant_id){
        $where = ['merchant' => $merchant_id];
        
        $total = db('production_order')->where($where)->count();
        $pending = db('production_order')->where(array_merge($where, ['status' => 0]))->count();
        $scheduled = db('production_order')->where(array_merge($where, ['status' => 1]))->count();
        $producing = db('production_order')->where(array_merge($where, ['status' => 2]))->count();
        $completed = db('production_order')->where(array_merge($where, ['status' => 3]))->count();
        
        return [
            'total' => $total,
            'pending' => $pending,
            'scheduled' => $scheduled,
            'producing' => $producing,
            'completed' => $completed
        ];
    }
    
    //获取质量统计
    private function getQualityStats($merchant_id, $today){
        $stats = db('production_summary')
            ->where([
                'merchant' => $merchant_id,
                'summary_date' => $today
            ])
            ->field('SUM(actual_qty) as total_qty, SUM(good_qty) as good_qty, SUM(defect_qty) as defect_qty')
            ->find();
            
        $total_qty = $stats['total_qty'] ?: 0;
        $good_qty = $stats['good_qty'] ?: 0;
        $defect_qty = $stats['defect_qty'] ?: 0;
        
        return [
            'total_qty' => $total_qty,
            'good_qty' => $good_qty,
            'defect_qty' => $defect_qty,
            'qualified_rate' => $total_qty > 0 ? round(($good_qty / $total_qty) * 100, 1) : 0,
            'defect_rate' => $total_qty > 0 ? round(($defect_qty / $total_qty) * 100, 1) : 0
        ];
    }
}
