<?php
namespace app\index\model;
use think\Model;

class ProductionOrder extends Model{
    //生产订单表
    protected $table = 'is_production_order';
    
    protected $type = [
        'more' => 'json'
    ];
    
    //关联商品信息
    public function goodsinfo(){
        return $this->hasOne('app\index\model\Goods', 'id', 'goods_id');
    }

    //关联配方信息
    public function formulainfo(){
        return $this->hasOne('app\index\model\ProductionFormula', 'id', 'formula_id');
    }

    //关联模具信息
    public function moldinfo(){
        return $this->hasOne('app\index\model\Mold', 'id', 'mold_id');
    }
    
    //关联工艺参数
    public function processinfo(){
        return $this->hasOne('app\index\model\ProcessParameter', 'id', 'process_id');
    }
    
    //关联创建人信息
    public function creatorinfo(){
        return $this->hasOne('app\index\model\User', 'id', 'creator');
    }

    //关联审核人信息
    public function auditorinfo(){
        return $this->hasOne('app\index\model\User', 'id', 'auditor');
    }

    //关联排产计划
    public function schedules(){
        return $this->hasMany('app\index\model\ProductionSchedule', 'production_order_id', 'id');
    }
    
    //状态读取器
    protected function getStatusAttr($val, $data){
        $status = [
            '0' => '待排产',
            '1' => '已排产', 
            '2' => '生产中',
            '3' => '已完成',
            '4' => '已取消'
        ];
        return isset($status[$val]) ? $status[$val] : '未知';
    }
    
    //状态原始值读取器
    protected function getStatusValueAttr($val, $data){
        return $data['status'];
    }
    
    //优先级读取器
    protected function getPriorityAttr($val, $data){
        $priority = [
            '1' => '紧急',
            '2' => '高',
            '3' => '普通',
            '4' => '低'
        ];
        return isset($priority[$val]) ? $priority[$val] : '普通';
    }
    
    //优先级原始值读取器
    protected function getPriorityValueAttr($val, $data){
        return $data['priority'];
    }
    
    //计划数量显示
    protected function getPlanQtyTextAttr($val, $data){
        return number_format($data['plan_qty'], 2);
    }
    
    //创建时间读取器
    protected function getCreatetimeAttr($val, $data){
        return $val ? date('Y-m-d H:i:s', $val) : '';
    }
    
    //审核时间读取器
    protected function getAuditTimeAttr($val, $data){
        return $val ? date('Y-m-d H:i:s', $val) : '';
    }
    
    //计划开始日期读取器
    protected function getPlanStartDateAttr($val, $data){
        return $val ? $val : '';
    }
    
    //计划结束日期读取器
    protected function getPlanEndDateAttr($val, $data){
        return $val ? $val : '';
    }
    
    //获取订单进度
    public function getProgress(){
        $schedules = $this->schedules()->select();
        if($schedules->isEmpty()){
            return ['progress' => 0, 'completed_qty' => 0, 'remaining_qty' => $this->plan_qty];
        }
        
        $completed_qty = 0;
        foreach($schedules as $schedule){
            if($schedule->status == 2){ //已完成
                $completed_qty += $schedule->actual_qty;
            }
        }
        
        $progress = $this->plan_qty > 0 ? ($completed_qty / $this->plan_qty * 100) : 0;
        
        return [
            'progress' => round($progress, 2),
            'completed_qty' => $completed_qty,
            'remaining_qty' => $this->plan_qty - $completed_qty
        ];
    }
    
    //获取预计完成时间
    public function getEstimatedCompletionTime(){
        $schedules = $this->schedules()
            ->where(['status' => ['in', [0, 1, 3]]]) //待生产、生产中、暂停
            ->order('plan_end_time desc')
            ->select();
            
        if($schedules->isEmpty()){
            return null;
        }
        
        return $schedules[0]->plan_end_time;
    }
    
    //检查是否可以开始生产
    public function canStartProduction(){
        //检查必要条件
        if($this->status != 1){
            return ['can_start' => false, 'reason' => '订单状态不正确'];
        }
        
        if(!$this->formula_id){
            return ['can_start' => false, 'reason' => '未指定生产配方'];
        }
        
        if(!$this->mold_id){
            return ['can_start' => false, 'reason' => '未指定生产模具'];
        }
        
        //检查模具状态
        $mold = $this->moldinfo;
        if(!$mold || $mold->status != 1){
            return ['can_start' => false, 'reason' => '模具状态不可用'];
        }
        
        //检查是否有排产计划
        $schedule_count = $this->schedules()->count();
        if($schedule_count == 0){
            return ['can_start' => false, 'reason' => '未安排生产计划'];
        }
        
        return ['can_start' => true, 'reason' => ''];
    }
    
    //更新订单状态
    public function updateStatus(){
        $schedules = $this->schedules()->select();
        
        if($schedules->isEmpty()){
            return;
        }
        
        $has_producing = false;
        $all_completed = true;
        
        foreach($schedules as $schedule){
            if($schedule->status == 1 || $schedule->status == 3){ //生产中或暂停
                $has_producing = true;
                $all_completed = false;
            } elseif($schedule->status != 2){ //不是已完成
                $all_completed = false;
            }
        }
        
        if($has_producing){
            $this->status = 2; //生产中
        } elseif($all_completed){
            $this->status = 3; //已完成
        }
        
        $this->save();
    }
    
    //获取订单成本分析
    public function getCostAnalysis(){
        if(!$this->formula_id){
            return null;
        }
        
        $formula = $this->formulainfo;
        if(!$formula){
            return null;
        }
        
        //计算原料成本
        $material_cost = $formula->getCostCalculation($this->plan_qty);
        
        //计算人工成本(需要根据实际情况调整)
        $labor_cost = $this->calculateLaborCost();
        
        //计算设备成本
        $machine_cost = $this->calculateMachineCost();
        
        return [
            'material_cost' => $material_cost,
            'labor_cost' => $labor_cost,
            'machine_cost' => $machine_cost,
            'total_cost' => $material_cost['total_cost'] + $labor_cost + $machine_cost
        ];
    }
    
    //计算人工成本
    private function calculateLaborCost(){
        //根据排产计划计算人工成本
        $schedules = $this->schedules()->select();
        $total_hours = 0;
        
        foreach($schedules as $schedule){
            if($schedule->plan_start_time && $schedule->plan_end_time){
                $hours = (strtotime($schedule->plan_end_time) - strtotime($schedule->plan_start_time)) / 3600;
                $total_hours += $hours;
            }
        }
        
        //假设人工成本每小时50元
        return $total_hours * 50;
    }
    
    //计算设备成本
    private function calculateMachineCost(){
        //根据排产计划计算设备使用成本
        $schedules = $this->schedules()->select();
        $total_cost = 0;
        
        foreach($schedules as $schedule){
            if($schedule->plan_start_time && $schedule->plan_end_time){
                $hours = (strtotime($schedule->plan_end_time) - strtotime($schedule->plan_start_time)) / 3600;
                //假设设备成本每小时30元
                $total_cost += $hours * 30;
            }
        }
        
        return $total_cost;
    }
}
