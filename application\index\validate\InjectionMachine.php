<?php
namespace app\index\validate;
use think\Validate;

class InjectionMachine extends Validate
{
    protected $rule = [
        'name'              => 'require|max:64',
        'code'              => 'require|max:32|unique:injection_machine',
        'model'             => 'max:64',
        'tonnage'           => 'integer|egt:0',
        'max_shot_weight'   => 'float|egt:0',
        'status'            => 'require|in:0,1,2',
        'workshop'          => 'max:32',
        'location'          => 'max:64',
        'purchase_date'     => 'date',
        'warranty_date'     => 'date',
        'data'              => 'max:256'
    ];

    protected $message = [
        'name.require'              => '设备名称不能为空',
        'name.max'                  => '设备名称不能超过64个字符',
        'code.require'              => '设备编号不能为空',
        'code.max'                  => '设备编号不能超过32个字符',
        'code.unique'               => '设备编号已存在',
        'model.max'                 => '设备型号不能超过64个字符',
        'tonnage.integer'           => '锁模力必须是整数',
        'tonnage.egt'               => '锁模力不能小于0',
        'max_shot_weight.float'     => '最大射胶量必须是数字',
        'max_shot_weight.egt'       => '最大射胶量不能小于0',
        'status.require'            => '设备状态不能为空',
        'status.in'                 => '设备状态值不正确',
        'workshop.max'              => '所属车间不能超过32个字符',
        'location.max'              => '设备位置不能超过64个字符',
        'purchase_date.date'        => '购买日期格式不正确',
        'warranty_date.date'        => '保修期日期格式不正确',
        'data.max'                  => '备注信息不能超过256个字符'
    ];

    protected $scene = [
        'add'   => ['name', 'code', 'model', 'tonnage', 'max_shot_weight', 'status', 'workshop', 'location', 'purchase_date', 'warranty_date', 'data'],
        'edit'  => ['name', 'model', 'tonnage', 'max_shot_weight', 'status', 'workshop', 'location', 'purchase_date', 'warranty_date', 'data']
    ];
}
