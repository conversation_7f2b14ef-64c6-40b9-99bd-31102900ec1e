<?php
namespace app\index\validate;
use think\Validate;

class Process extends Validate
{
    protected $rule = [
        'name'                  => 'require|max:64',
        'code'                  => 'require|max:32|unique:process',
        'type'                  => 'require|in:mixing,molding,injection,extrusion,vulcanization,trimming,secondary_vulcanization,surface_treatment,printing,assembly,quality_check,packaging,other',
        'difficulty'            => 'integer|between:1,5',
        'standard_time'         => 'float|egt:0',
        'sort'                  => 'integer|egt:0',
        'status'                => 'require|in:0,1',
        'description'           => 'max:500',
        'steps'                 => 'max:2000',
        'quality_requirements'  => 'max:1000',
        'required_equipment'    => 'max:1000',
        'parameters'            => 'max:1000',
        'safety_notes'          => 'max:1000',
        'documents'             => 'max:1000'
    ];

    protected $message = [
        'name.require'                  => '工艺名称不能为空',
        'name.max'                      => '工艺名称不能超过64个字符',
        'code.require'                  => '工艺编号不能为空',
        'code.max'                      => '工艺编号不能超过32个字符',
        'code.unique'                   => '工艺编号已存在',
        'type.require'                  => '工艺类型不能为空',
        'type.in'                       => '工艺类型值不正确',
        'difficulty.integer'            => '难度等级必须是整数',
        'difficulty.between'            => '难度等级必须在1-5之间',
        'standard_time.float'           => '标准工时必须是数字',
        'standard_time.egt'             => '标准工时不能小于0',
        'sort.integer'                  => '排序必须是整数',
        'sort.egt'                      => '排序不能小于0',
        'status.require'                => '工艺状态不能为空',
        'status.in'                     => '工艺状态值不正确',
        'description.max'               => '工艺描述不能超过500个字符',
        'steps.max'                     => '工艺步骤不能超过2000个字符',
        'quality_requirements.max'      => '质量要求不能超过1000个字符',
        'required_equipment.max'        => '所需设备不能超过1000个字符',
        'parameters.max'                => '工艺参数不能超过1000个字符',
        'safety_notes.max'              => '安全注意事项不能超过1000个字符',
        'documents.max'                 => '工艺文档不能超过1000个字符'
    ];

    protected $scene = [
        'add'   => ['name', 'code', 'type', 'difficulty', 'standard_time', 'sort', 'status', 'description', 'steps', 'quality_requirements', 'required_equipment', 'parameters', 'safety_notes', 'documents'],
        'edit'  => ['name', 'type', 'difficulty', 'standard_time', 'sort', 'status', 'description', 'steps', 'quality_requirements', 'required_equipment', 'parameters', 'safety_notes', 'documents']
    ];
}
