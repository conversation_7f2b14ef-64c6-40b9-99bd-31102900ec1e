<?php
namespace app\index\controller;
use think\Hook;
use app\index\controller\Acl;
use app\index\model\Otsaleclass;
use app\index\model\Otsaleinfo;
use app\index\model\Room;
use app\index\model\Roominfo;
use app\index\model\Serial;
use app\index\model\Serialinfo;
class Otsale extends Acl {
    //其他出库模块
    //---------------(^_^)---------------//
    //主视图
    public function main(){
        $input=input('get.');
        //兼容盘亏单
        if(isset_full($input,'info')){
            //1.解析数据
            $arr=json_decode(base64_decode($input['info']),true);
            $data='临时数据|'.substr(md5($input['info'].time()),8,16);
            //2.数据库插入临时数据
            foreach ($arr as $vo) {
                $vo['pid']=0;
                $vo['serial']='';
                $vo['data']=$data;
                Otsaleinfo::create(syn_sql($vo,'otsaleinfo'));
            }
            //3.读取临时数据
            $info=Otsaleinfo::with('roominfo,goodsinfo,warehouseinfo')->where(['pid'=>0,'data'=>$data])->select();
            foreach ($info as $info_key=>$info_vo) {
                $info[$info_key]['data']='';//清空临时数据
            }
            //4.删除临时数据
            Otsaleinfo::where(['pid'=>0,'data'=>$data])->delete();
            $this->assign('info',$info);
        }
        return $this->fetch();
    }
    //新增|更新信息
    public function set(){
        $input=input('post.');
        if(isset($input['id'])){
            //验证其他出库单详情
            if(isset_full($input,'tab')){
                foreach ($input['tab'] as $tab_key=>$tab_vo) {
                    $tab_vali = $this->validate($tab_vo,'Otsaleinfo');//详情验证
                    if($tab_vali!==true){
                        return json(['state'=>'error','info'=>'[ 数据表格 ]第'.($tab_key+1).'行'.$tab_vali]);
                        exit;
                    }
                }
            }else{
                return json(['state'=>'error','info'=>'数据表格不可为空!']);
                exit;
            }
            //验证操作类型
            if(empty($input['id'])){
                //新增
                $input['merchant']=Session('is_merchant_id');//补充商户信息
                $vali = $this->validate($input,'Otsaleclass');
                if($vali===true){
                    $create_info=Otsaleclass::create(syn_sql($input,'otsaleclass'));
                    Hook::listen('create_otsale',$create_info);//其他出库单新增行为
                    push_log('新增其他出库单[ '.$create_info['number'].' ]');//日志
                    $resule=['state'=>'success'];
                }else{
                    $resule=['state'=>'error','info'=>$vali];
                }
            }else{
                //更新
                $vali = $this->validate($input,'Otsaleclass.update');
                if($vali===true){
                    $update_info=Otsaleclass::update(syn_sql($input,'otsaleclass'));
                    Hook::listen('update_otsale',$update_info);//其他出库单更新行为
                    push_log('更新其他出库单[ '.$update_info['number'].' ]');//日志
                    Otsaleinfo::where(['pid'=>$update_info['id']])->delete();
                    $resule=['state'=>'success'];
                }else{
                    $resule=['state'=>'error','info'=>$vali];
                }
            }
            //添加其他出库单详情
            if($resule['state']=='success'){
                $info_pid=empty($input['id'])?$create_info['id']:$update_info['id'];
                foreach ($input['tab'] as $info_vo) {
                    $info_vo['pid']=$info_pid;
                    (isset_full($info_vo,'serial')&& $info_vo['serial']=='&amp;nbsp;')&&($info_vo['serial']='');//兼容串码
                    Otsaleinfo::create(syn_sql($info_vo,'otsaleinfo'));
                }
            }
        }else{
            $resule=['state'=>'error','info'=>'传入参数不完整!'];
        }
        //兼容自动审核[新增操作]
        if($resule['state']=='success'&&empty($input['id'])){
            empty(get_sys(['auto_auditing']))||($this->auditing([$create_info['id']],true));
        }
        return json($resule);
    }
    //报表视图
    public function form(){
        return $this->fetch();
    }
    //报表列表
    public function form_list(){
        $input=input('post.');
        //数据完整性判断
        if(isset_full($input,'page') && isset_full($input,'limit')){
            $sql=get_sql($input,[
                'name'=>'continue',
                'number'=>'full_like',
                'pagetype'=>'full_dec_1',
                'start_time'=>'stime',
                'end_time'=>'etime',
                'type'=>'full_dec_1',
                'warehouse'=>'continue',
                'user'=>'full_division_in',
                'data'=>'full_like',
            ],'otsaleclass');//构造SQL
            //处理名称搜索
            if(isset_full($input,'name')){
                $goods=get_db_field('goods',['name|py'=>['like','%'.$input['name'].'%']],'id');//取出商品表信息
                if(empty($goods)){
                    sql_assign($sql,'id',[]);//多表查询赋值处理
                }else{
                    $info=get_db_field('otsaleinfo',['goods'=>['in',$goods]],'pid');//取出详情表数据
                    sql_assign($sql,'id',$info);//多表查询赋值处理
                }
            }
            //处理仓库搜索
            if(isset_full($input,'warehouse')){
                $info=get_db_field('otsaleinfo',['warehouse'=>['in',explode(",",$input['warehouse'])]],'pid');//取出详情表数据
                sql_assign($sql,'id',$info,'intersect');//多表查询赋值处理
            }
            $sql['merchant']=['in',get_auth_merchant()];//补全授权商户数据
            $sql=auth('otsaleclass',$sql);//数据鉴权
            $count = Otsaleclass::where ($sql)->count();//获取总条数
            $arr = Otsaleclass::with('merchantinfo,userinfo')->where($sql)->page($input['page'],$input['limit'])->order('id desc')->select();//查询分页数据

            // 为销售出库单添加关联信息
            foreach($arr as $key => $item){
                if($item['pagetype']['nod'] == 3){ // 销售出库单
                    // 通过delivery_id字段直接查找发货单和销售订单信息
                    if($item['delivery_id'] > 0){
                        $delivery = db('delivery_class')->where(['id' => $item['delivery_id']])->find();
                        if($delivery){
                            $saleOrder = db('saleclass')->where(['id' => $delivery['sale_id']])->find();
                            if($saleOrder){
                                $arr[$key]['sale_number'] = $saleOrder['number'];
                                $arr[$key]['delivery_number'] = $delivery['number'];
                            } else {
                                $arr[$key]['sale_number'] = '销售订单不存在';
                                $arr[$key]['delivery_number'] = $delivery['number'];
                            }
                        } else {
                            $arr[$key]['sale_number'] = '发货单不存在';
                            $arr[$key]['delivery_number'] = '发货单不存在';
                        }
                    } else {
                        // 如果delivery_id为0，尝试从备注信息中解析（兼容旧数据）
                        $deliveryNumber = '';
                        if(strpos($item['data'], '发货单【') !== false){
                            preg_match('/发货单【(.+?)】/', $item['data'], $matches);
                            if(isset($matches[1])){
                                $deliveryNumber = $matches[1];
                                $delivery = db('delivery_class')->where(['number' => $deliveryNumber])->find();
                                if($delivery){
                                    $saleOrder = db('saleclass')->where(['id' => $delivery['sale_id']])->find();
                                    if($saleOrder){
                                        $arr[$key]['sale_number'] = $saleOrder['number'];
                                        $arr[$key]['delivery_number'] = $deliveryNumber;
                                    }
                                }
                            }
                        }

                        // 如果还是没有找到，设置默认值
                        if(!isset($arr[$key]['sale_number'])){
                            $arr[$key]['sale_number'] = '无关联';
                            $arr[$key]['delivery_number'] = '无关联';
                        }
                    }
                } else {
                    // 非销售出库单，设置空值
                    $arr[$key]['sale_number'] = '';
                    $arr[$key]['delivery_number'] = '';
                }
            }

            $resule=[
                'code'=>0,
                'msg'=>'获取成功',
                'count'=>$count,
                'data'=>$arr
            ];//返回数据
        }else{
            $resule=['state'=>'error','info'=>'传入参数不完整!'];
        }
        return json($resule);
    }
    //详情
    public function info(){
        $input=input('get.');
        //数据完整性判断
        if(isset_full($input,'id')){
            $class=Otsaleclass::where(['id'=>$input['id']])->find();
            $info=Otsaleinfo::with('roominfo,goodsinfo,warehouseinfo')->where(['pid'=>$input['id']])->select()->toarray();
            foreach ($info as $info_key=>$info_vo) {
                //改造串码数据
                $info[$info_key]['roominfo']['serialinfo']=implode(",",arraychange(searchdata($info_vo['roominfo']['serialinfo'],['type|nod'=>['eq',0]]),'code'));
            }
            $this->assign('class',$class);
            $this->assign('info',$info);
            return $this->fetch('main');
        }else{
            $resule=['state'=>'error','info'=>'传入参数不完整!'];
        }
        return json($resule);
    }
    //审核
    public function auditing($arr=[],$auto=false){
        (empty($arr))&&($arr=input('post.arr'));//兼容多态审核
        if(empty($arr)){
            $resule=['state'=>'error','info'=>'传入参数不完整!'];
        }else{
            $class_data=[];//初始化CLASS数据
            $info_data=[];//初始化INFO数据
            //数据检验
            foreach ($arr as $arr_vo) {
                $class=Otsaleclass::where(['id'=>$arr_vo])->find();
                $info=Otsaleinfo::where(['pid'=>$arr_vo])->select();
                //判断操作类型
                if(empty($class['type']['nod'])){
                    //审核操作
                    foreach ($info as $info_key=>$info_vo) {
                        if(!empty($info_vo['serial'])){
                            $serial_sql=['code'=>['in',explode(',',$info_vo['serial'])],'type'=>['neq',0]];
                            $serial=Serial::where($serial_sql)->find();//查找串码状态为非未销售
                            if(!empty($serial)){
                                $auto&&(push_log('自动审核其他出库单[ '.$class['number'].' ]失败,原因:第'.($info_key+1).'行串码状态不正确!'));//日志
                                return json(['state'=>'error','info'=>'审核-其他出库单[ '.$class['number'].' ]失败,原因:第'.($info_key+1).'行串码状态不正确!']);
                                exit;
                            }
                        }
                    }
                }else{
                    //反审核操作
                    foreach ($info as $info_key=>$info_vo) {
                        if(!empty($info_vo['serial'])){
                            $serial_sql=['code'=>['in',explode(',',$info_vo['serial'])],'type'=>['neq',1]];
                            $serial=Serial::where($serial_sql)->find();//查找串码状态为非已销售
                            if(!empty($serial)){
                                return json(['state'=>'error','info'=>'反审核-其他出库单[ '.$class['number'].' ]第'.($info_key+1).'行串码状态不正确!']);
                                exit;
                            }
                        }
                    }
                }
                $class_data[$arr_vo]=$class;//转存CLASS数据
                $info_data[$arr_vo]=$info;//转存INFO数据
            }
            //实际操作
            foreach ($arr as $arr_vo) {
                $class=$class_data[$arr_vo];//读取CLASS数据
                $info=$info_data[$arr_vo];//读取INFO数据
                //判断操作类型
                if(empty($class['type']['nod'])){
                    //审核操作
                    foreach ($info as $info_vo) {
                        //设置仓储信息
                        Room::where(['id'=>$info_vo['room']])->setDec('nums',$info_vo['nums']);//更新仓储数据[-]
                        //新增仓储详情
                        $roominfo_sql['pid']=$info_vo['room'];
                        $roominfo_sql['type']=8;
                        $roominfo_sql['class']=$arr_vo;
                        $roominfo_sql['info']=$info_vo['id'];
                        $roominfo_sql['nums']=$info_vo['nums'];
                        Roominfo::create($roominfo_sql);
                        //操作串码信息
                        if (!empty($info_vo['serial'])){
                            $serial_arr=explode(',',$info_vo['serial']);//分割串码信息
                            foreach ($serial_arr as $serial_arr_vo) {
                                //设置串码信息
                                $serial=Serial::where(['code'=>$serial_arr_vo])->find();//获取串码信息
                                Serial::update(['id'=>$serial['id'],'type'=>1]);
                                //新增串码详情
                                Serialinfo::create (['pid'=>$serial['id'],'type'=>7,'class'=>$arr_vo]);
                            }
                        }
                    }
                    Otsaleclass::update(['id'=>$arr_vo,'type'=>1,'auditinguser'=>Session('is_user_id'),'auditingtime'=>time()]);//更新CLASS数据
                    set_summary('otsale',$arr_vo,true);//更新统计表

                    // 如果是销售出库单，更新对应销售订单的发货状态
                    if($class['pagetype']['nod'] == 3 && $class['delivery_id'] > 0){
                        $this->updateSaleDeliveryStatusFromOutbound($class['delivery_id']);
                    }

                    push_log(($auto?'自动':'').'审核其他出库单[ '.$class['number'].' ]');
                }else{
                    //反审核操作
                    foreach ($info as $info_vo){
                        Room::where (['id'=>$info_vo['room']])->setInc('nums',$info_vo['nums']);//更新仓储数据[+]
                        if(!empty($info_vo['serial'])){
                            $serial=Serial::where(['code'=>['in',explode(',',$info_vo['serial'])]])->select();//获取串码数据
                            foreach ($serial as $serial_vo) {
                                //设置串码数据
                                Serial::update(['id'=>$serial_vo['id'],'type'=>0]);
                                Serialinfo::where(['pid'=>$serial_vo['id'],'type'=>7,'class'=>$arr_vo])->delete();//删除串码详情
                            }
                        }
                    }
                    Roominfo::where(['type'=>8,'class'=>$arr_vo])->delete();//删除仓储详情
                    Otsaleclass::update(['id'=>$arr_vo,'type'=>0,'auditinguser'=>0,'auditingtime'=>0]);//更新CLASS数据
                    set_summary('otsale',$arr_vo,false);//更新统计表

                    // 如果是销售出库单，更新对应销售订单的发货状态
                    if($class['pagetype']['nod'] == 3 && $class['delivery_id'] > 0){
                        $this->updateSaleDeliveryStatusFromOutbound($class['delivery_id']);
                    }

                    push_log ('反审核其他出库单[ '.$class['number'].' ]');
                }
            }
            $resule=['state'=>'success'];
        }
        return $auto?true:json($resule);
    }
    //删除信息
    public function del(){
        $input=input('post.');
        if(isset_full($input,'arr') && is_array($input['arr'])){
            $class=db('otsaleclass')->where(['id'=>['in',$input['arr']]])->select()->ToArray();//获取CLASS数据
            $data = searchdata($class,['type'=>['eq',1]]);//查询已审核单据
            //数据检验
            if(empty($data)){
                foreach ($class as $class_vo) {
                    push_log('删除其他出库单[ '.$class_vo['number'].' ]');//日志
                    Hook::listen('del_otsale',$class_vo['id']);//其他出库单删除行为
                }
                Otsaleclass::where(['id'=>['in',$input['arr']]])->delete();
                Otsaleinfo::where(['pid'=>['in',$input['arr']]])->delete();
                $resule=['state'=>'success'];
            }else{
                $resule=['state'=>'error','info'=>'其他出库单[ '.$data[0]['number'].' ]已审核,不可删除!'];
            }
        }else{
            $resule=['state'=>'error','info'=>'传入参数不完整!'];
        }
        return json($resule);
    }
    //导出报表信息
    public function exports(){
        $input=input('get.');
        if(isset($input['mode'])){
            push_log('导出其他出库单数据');//日志
            $sql=get_sql($input,[
                'name'=>'continue',
                'number'=>'full_like',
                'pagetype'=>'full_dec_1',
                'start_time'=>'stime',
                'end_time'=>'etime',
                'type'=>'full_dec_1',
                'warehouse'=>'continue',
                'user'=>'full_division_in',
                'data'=>'full_like',
            ],'otsaleclass');//构造SQL
            //处理名称搜索
            if(isset_full($input,'name')){
                $goods=get_db_field('goods',['name|py'=>['like','%'.$input['name'].'%']],'id');//取出商品表信息
                if(empty($goods)){
                    sql_assign($sql,'id',[]);//多表查询赋值处理
                }else{
                    $info=get_db_field('otsaleinfo',['goods'=>['in',$goods]],'pid');//取出详情表数据
                    sql_assign($sql,'id',$info);//多表查询赋值处理
                }
            }
            //处理仓库搜索
            if(isset_full($input,'warehouse')){
                $info=get_db_field('otsaleinfo',['warehouse'=>['in',explode(",",$input['warehouse'])]],'pid');//取出详情表数据
                sql_assign($sql,'id',$info,'intersect');//多表查询赋值处理
            }
            $sql['merchant']=['in',get_auth_merchant()];//补全授权商户数据
            $sql=auth('otsaleclass',$sql);//数据鉴权
            $arr = Otsaleclass::with('merchantinfo,userinfo')->where($sql)->order('id desc')->select();//查询数据
            //判断报表类型
            if(empty($input['mode'])){
                //简易报表
                $formfield=get_formfield('otsale_export','array');//获取字段配置
                //开始构造导出数据
                $excel=[];//初始化导出数据
                //1.填充标题数据
                array_push($excel,['type'=>'title','info'=>'其他出库单列表']);
                //2.构造表格数据
                $table_cell=[];//初始化表头数据
                //构造表头数据
                foreach ($formfield as $formfield_vo) {
                    $table_cell[$formfield_vo['key']]=$formfield_vo['text'];
                }
                $table_data=[];//初始化表内数据
                //构造表内数据
                foreach ($arr as $arr_vo) {
                    $row_data=[];
                    //循环字段配置
                    foreach ($formfield as $formfield_vo) {
                        $val='nod_initial';//初始化数据
                        //循环匹配数据源
                        foreach (explode('|',$formfield_vo['data']) as $source) {
                            $val=$val=='nod_initial'?$arr_vo[$source]:(isset($val[$source])?$val[$source]:'');
                        }
                        $row_data[$formfield_vo['key']]=$val;//数据赋值
                    }
                    array_push($table_data,$row_data);//加入行数据
                }
                array_push($excel,['type'=>'table','info'=>['cell'=>$table_cell,'data'=>$table_data]]);//填充表内数据
                //3.导出execl
                export_excel('其他出库单列表',$excel);
            }else{
                //详细报表
                $files=[];//初始化文件列表
                $formfield=get_formfield('otsale_exports','array');//获取字段配置
                //配置字段
                $sys=get_sys(['enable_batch','enable_serial']);
                empty($sys['enable_batch'])&&(arrs_key_del($formfield,['key','batch']));
                empty($sys['enable_serial'])&&(arrs_key_del($formfield,['key','serial']));
                //循环CLASS数据
                foreach ($arr as $arr_vo) {
                    $excel=[];//初始化导出数据
                    //1.填充标题数据
                    array_push($excel,['type'=>'title','info'=>'其他出库单']);
                    //2.添加基础字段
                    array_push($excel,['type'=>'node','info'=>[
                        '单据日期:'.$arr_vo['time'],
                        '',
                        '单据编号:'.$arr_vo['number'],
                        '',
                        '单据类型:'.$arr_vo['pagetype']['name']
                    ]]);
                    //3.构造表格数据
                    $info=Otsaleinfo::where(['pid'=>$arr_vo['id']])->select();
                    $table_cell=[];//初始化表头数据
                    //构造表头数据
                    foreach ($formfield as $formfield_vo) {
                        $table_cell[$formfield_vo['key']]=$formfield_vo['text'];
                    }
                    $table_data=[];//初始化表内数据
                    //构造表内数据
                    foreach ($info as $info_vo) {
                        $row_data=[];
                        //循环字段配置
                        foreach ($formfield as $formfield_vo) {
                            $val='nod_initial';//初始化数据
                            //循环匹配数据源
                            foreach (explode('|',$formfield_vo['data']) as $source) {
                                $val=$val=='nod_initial'?$info_vo[$source]:(isset($val[$source])?$val[$source]:'');
                            }
                            $row_data[$formfield_vo['key']]=$val;//数据赋值
                        }
                        array_push($table_data,$row_data);//加入行数据
                    }
                    array_push($excel,['type'=>'table','info'=>['cell'=>$table_cell,'data'=>$table_data]]);//填充表内数据
                    //4.添加基础字段
                    array_push($excel,['type'=>'node','info'=>[
                        '制单人:'.$arr_vo['userinfo']['name'],
                        '',
                        '备注信息:'.$arr_vo['data'],
                    ]]);
                    $path=export_excel($arr_vo['number'],$excel,false);//生成文件
                    array_push($files,$path);//添加文件路径数据
                }
                file_to_zip('其他出库单明细',$files);//打包输出数据
            }
        }else{
            $resule=['state'=>'error','info'=>'传入参数不完整!'];
        }
        return json($resule);
    }
    //打印
    public function prints(){
        $input=input('get.');
        if(isset_full($input,'id')){
            $print_name='otsale';//模板标识
            $class=Otsaleclass::where(['id'=>$input['id']])->find();
            $info=Otsaleinfo::where(['pid'=>$input['id']])->select();
            $sys=get_sys(['enable_batch','enable_serial','print_paper']);
            //1.获取字段信息
            $formfield=get_formfield('otsale_print','array');//获取字段配置
            //2.配置字段
            empty($sys['enable_batch'])&&(arrs_key_del($formfield,['key','batch']));
            empty($sys['enable_serial'])&&(arrs_key_del($formfield,['key','serial']));
            //3.构造表格数据
            $tab_html=get_print_tab($formfield,$info);
            //4.获取模板代码
            $print=get_print($print_name);
            $print_text=$print[empty($sys['print_paper'])?'paper4':'paper2'];
            //5.赋值数据
            $this->assign('class',$class);
            $this->assign('tab_html',$tab_html);
            $this->assign('print_name',$print_name);
            $this->assign('paper_type',$sys['print_paper']);
            $this->assign('print_text',$print_text);
            return $this->fetch();
        }else{
            $resule=['state'=>'error','info'=>'传入参数不完整!'];
        }
        return json($resule);
    }

    //修复销售出库单与发货单的关联关系
    public function fix_delivery_relation(){
        try {
            // 查找所有销售出库单（pagetype=3）且delivery_id=0的记录
            $otsaleRecords = db('otsaleclass')->where(['pagetype' => 3, 'delivery_id' => 0])->select();

            $fixedCount = 0;
            $totalCount = count($otsaleRecords);

            foreach($otsaleRecords as $record) {
                // 从备注信息中提取发货单号
                if(preg_match('/发货单【(.+?)】/', $record['data'], $matches)) {
                    $deliveryNumber = $matches[1];

                    // 查找对应的发货单
                    $delivery = db('delivery_class')->where(['number' => $deliveryNumber])->find();

                    if($delivery) {
                        // 更新其他出库单的delivery_id字段
                        db('otsaleclass')->where(['id' => $record['id']])->update(['delivery_id' => $delivery['id']]);
                        $fixedCount++;
                    }
                }
            }

            return json([
                'state' => 'success',
                'info' => "修复完成！总共处理 {$totalCount} 条记录，成功修复 {$fixedCount} 条关联关系"
            ]);

        } catch(\Exception $e) {
            return json([
                'state' => 'error',
                'info' => '修复失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 根据发货单ID更新对应销售订单的发货状态
     * @param int $deliveryId 发货单ID
     */
    private function updateSaleDeliveryStatusFromOutbound($deliveryId) {
        try {
            // 通过发货单ID获取销售订单ID
            $delivery = db('delivery_class')->where(['id' => $deliveryId])->find();
            if(!$delivery) {
                return;
            }

            $saleId = $delivery['sale_id'];

            // 获取销售订单的所有明细
            $saleInfos = db('saleinfo')->where(['pid' => $saleId])->select();

            $totalQty = 0;
            $outboundQty = 0;

            foreach($saleInfos as $saleInfo) {
                $totalQty += floatval($saleInfo['nums']);

                // 计算该明细的已出库数量
                $outboundQty += $this->getOutboundQtyBySaleInfo($saleInfo['id']);
            }

            // 计算发货状态
            $deliveryStatus = 0; // 未发货
            if($outboundQty > 0) {
                if($outboundQty >= $totalQty) {
                    $deliveryStatus = 2; // 已发货
                } else {
                    $deliveryStatus = 1; // 部分发货
                }
            }

            // 更新销售订单的发货状态
            db('saleclass')->where(['id' => $saleId])->update(['delivery_status' => $deliveryStatus]);

        } catch(\Exception $e) {
            // 记录错误但不影响主流程
            push_log('更新销售订单发货状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 通过销售订单明细ID获取已出库数量（基于已审核的销售出库单）
     * @param int $saleInfoId 销售订单明细ID
     * @return float 已出库数量
     */
    private function getOutboundQtyBySaleInfo($saleInfoId) {
        try {
            // 查找与该销售订单明细相关的所有发货单明细
            $deliveryInfos = db('delivery_info')->where(['sale_info_id' => $saleInfoId])->select();

            $totalOutboundQty = 0;

            foreach($deliveryInfos as $deliveryInfo) {
                // 查找与该发货单相关的已审核销售出库单
                $outboundRecords = db('otsaleinfo')
                    ->alias('oi')
                    ->join('otsaleclass oc', 'oi.pid = oc.id')
                    ->join('delivery_class dc', 'oc.delivery_id = dc.id')
                    ->where([
                        'dc.id' => $deliveryInfo['pid'], // 发货单ID
                        'oi.goods' => $deliveryInfo['goods'], // 商品ID
                        'oi.room' => $deliveryInfo['room'], // 仓储ID
                        'oc.type' => 1, // 已审核的出库单
                        'oc.pagetype' => 3 // 销售出库单
                    ])
                    ->field('oi.nums')
                    ->select();

                foreach($outboundRecords as $record) {
                    $totalOutboundQty += floatval($record['nums']);
                }
            }

            return $totalOutboundQty;

        } catch(\Exception $e) {
            return 0; // 出错时返回0
        }
    }
}