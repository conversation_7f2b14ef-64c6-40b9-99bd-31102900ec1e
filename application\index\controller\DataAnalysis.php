<?php
namespace app\index\controller;
use app\index\controller\Acl;

class DataAnalysis extends Acl {
    //数据分析模块
    
    //主视图
    public function main(){
        return $this->fetch();
    }
    
    //效率分析
    public function efficiency(){
        return $this->fetch();
    }
    
    //成本分析
    public function cost(){
        return $this->fetch();
    }
    
    //预测分析
    public function forecast(){
        return $this->fetch();
    }

    //质量分析
    public function quality(){
        return $this->fetch();
    }
    
    //综合仪表板
    public function dashboard(){
        return $this->fetch();
    }
    
    //获取效率分析数据
    public function get_efficiency_analysis(){
        $input = input('post.');
        $start_date = isset($input['start_date']) ? $input['start_date'] : date('Y-m-01');
        $end_date = isset($input['end_date']) ? $input['end_date'] : date('Y-m-d');
        $analysis_type = isset($input['analysis_type']) ? $input['analysis_type'] : 1;

        try {
            // 检查效率分析表是否存在
            try {
                db()->query("SELECT 1 FROM is_efficiency_analysis LIMIT 1");

                // 使用别名明确指定字段
                $where = [
                    'ea.merchant' => Session('is_merchant_id'),
                    'ea.analysis_date' => ['between', [$start_date, $end_date]],
                    'ea.analysis_type' => $analysis_type
                ];

                if(isset($input['machine_id']) && !empty($input['machine_id'])){
                    $where['ea.machine_id'] = $input['machine_id'];
                }

                if(isset($input['goods_id']) && !empty($input['goods_id'])){
                    $where['ea.goods_id'] = $input['goods_id'];
                }

                $list = db('efficiency_analysis')
                    ->alias('ea')
                    ->join('injection_machine im', 'ea.machine_id = im.id', 'left')
                    ->join('goods g', 'ea.goods_id = g.id', 'left')
                    ->join('user u', 'ea.operator_id = u.id', 'left')
                    ->join('work_shift ws', 'ea.shift_id = ws.id', 'left')
                    ->where($where)
                    ->field('ea.*,im.name as machine_name,g.name as goods_name,u.name as operator_name,ws.name as shift_name')
                    ->order('ea.analysis_date desc')
                    ->limit(input('limit', 15))
                    ->select();

                return json(['code' => 0, 'msg' => '', 'count' => count($list), 'data' => $list]);

            } catch(\Exception $e) {
                // 效率分析表不存在，从生产统计表获取数据
                $where = [
                    'ps.merchant' => Session('is_merchant_id'),
                    'ps.summary_date' => ['between', [$start_date, $end_date]]
                ];

                if(isset($input['machine_id']) && !empty($input['machine_id'])){
                    $where['ps.machine_id'] = $input['machine_id'];
                }

                if(isset($input['goods_id']) && !empty($input['goods_id'])){
                    $where['ps.goods_id'] = $input['goods_id'];
                }

                $list = db('production_summary')
                    ->alias('ps')
                    ->join('goods g', 'ps.goods_id = g.id', 'left')
                    ->where($where)
                    ->field('
                        ps.summary_date as analysis_date,
                        ps.machine_id,
                        ps.goods_id,
                        ps.plan_qty as planned_output,
                        ps.actual_qty as actual_output,
                        ps.completion_rate as output_efficiency,
                        ps.working_hours as actual_time,
                        ps.efficiency as time_efficiency,
                        ps.qualified_rate as quality_rate,
                        ps.efficiency as oee,
                        (100 - ps.qualified_rate) as defect_rate,
                        ps.downtime_hours,
                        g.name as goods_name
                    ')
                    ->order('ps.summary_date desc')
                    ->limit(input('limit', 15))
                    ->select();

                return json(['code' => 0, 'msg' => '', 'count' => count($list), 'data' => $list]);
            }

        } catch(\Exception $e) {
            return json(['code' => 1, 'msg' => '获取效率分析数据失败: ' . $e->getMessage(), 'data' => []]);
        }
    }
    
    //生成效率分析
    public function generate_efficiency_analysis(){
        $input = input('post.');
        $analysis_date = isset($input['analysis_date']) ? $input['analysis_date'] : date('Y-m-d');
        $analysis_type = isset($input['analysis_type']) ? $input['analysis_type'] : 1;
        
        try {
            $this->calculateEfficiencyAnalysis($analysis_date, $analysis_type);
            return json(['state' => 'success', 'info' => '效率分析生成成功']);
        } catch (\Exception $e) {
            return json(['state' => 'error', 'info' => '效率分析生成失败：' . $e->getMessage()]);
        }
    }
    
    //获取成本分析数据
    public function get_cost_analysis(){
        $input = input('post.');
        $start_date = isset($input['start_date']) ? $input['start_date'] : date('Y-m-01');
        $end_date = isset($input['end_date']) ? $input['end_date'] : date('Y-m-d');

        try {
            // 检查成本分析表是否存在
            try {
                db()->query("SELECT 1 FROM is_cost_analysis LIMIT 1");

                // 使用别名明确指定字段
                $where = [
                    'ca.merchant' => Session('is_merchant_id'),
                    'ca.analysis_date' => ['between', [$start_date, $end_date]]
                ];

                if(isset($input['goods_id']) && !empty($input['goods_id'])){
                    $where['ca.goods_id'] = $input['goods_id'];
                }

                $list = db('cost_analysis')
                    ->alias('ca')
                    ->join('goods g', 'ca.goods_id = g.id', 'left')
                    ->where($where)
                    ->field('ca.*,g.name as goods_name')
                    ->order('ca.analysis_date desc')
                    ->limit(input('limit', 15))
                    ->select();

                return json(['code' => 0, 'msg' => '', 'count' => count($list), 'data' => $list]);

            } catch(\Exception $e) {
                // 成本分析表不存在，返回空数据
                return json(['code' => 0, 'msg' => '成本分析表不存在', 'count' => 0, 'data' => []]);
            }

        } catch(\Exception $e) {
            return json(['code' => 1, 'msg' => '获取成本分析数据失败: ' . $e->getMessage(), 'data' => []]);
        }
    }
    
    //生成成本分析
    public function generate_cost_analysis(){
        $input = input('post.');
        $analysis_date = isset($input['analysis_date']) ? $input['analysis_date'] : date('Y-m-d');
        $analysis_period = isset($input['analysis_period']) ? $input['analysis_period'] : 'daily';
        
        try {
            $this->calculateCostAnalysis($analysis_date, $analysis_period);
            return json(['state' => 'success', 'info' => '成本分析生成成功']);
        } catch (\Exception $e) {
            return json(['state' => 'error', 'info' => '成本分析生成失败：' . $e->getMessage()]);
        }
    }

    //获取质量分析数据
    public function get_quality_analysis(){
        $input = input('post.');
        $start_date = isset($input['start_date']) ? $input['start_date'] : date('Y-m-01');
        $end_date = isset($input['end_date']) ? $input['end_date'] : date('Y-m-d');

        try {
            // 从生产统计表获取质量数据
            $quality_data = db('production_summary')
                ->where([
                    'merchant' => Session('is_merchant_id'),
                    'summary_date' => ['between', [$start_date, $end_date]]
                ])
                ->field('
                    summary_date,
                    SUM(actual_qty) as total_qty,
                    SUM(good_qty) as good_qty,
                    SUM(defect_qty) as defect_qty,
                    AVG(qualified_rate) as avg_qualified_rate
                ')
                ->group('summary_date')
                ->order('summary_date desc')
                ->select();

            // 计算总体质量指标
            $total_qty = 0;
            $total_good_qty = 0;
            $total_defect_qty = 0;

            foreach($quality_data as $item){
                $total_qty += $item['total_qty'];
                $total_good_qty += $item['good_qty'];
                $total_defect_qty += $item['defect_qty'];
            }

            $overall_qualified_rate = $total_qty > 0 ? ($total_good_qty / $total_qty) * 100 : 0;
            $overall_defect_rate = $total_qty > 0 ? ($total_defect_qty / $total_qty) * 100 : 0;

            $summary = [
                'total_production' => $total_qty,
                'total_qualified' => $total_good_qty,
                'total_defects' => $total_defect_qty,
                'overall_qualified_rate' => round($overall_qualified_rate, 2),
                'overall_defect_rate' => round($overall_defect_rate, 2)
            ];

            return json([
                'state' => 'success',
                'data' => [
                    'summary' => $summary,
                    'daily_data' => $quality_data
                ]
            ]);

        } catch (\Exception $e) {
            return json(['state' => 'error', 'info' => '获取质量分析数据失败：' . $e->getMessage()]);
        }
    }

    //获取产品成本对比数据
    public function get_product_cost_comparison(){
        $input = input('post.');
        $start_date = isset($input['start_date']) ? $input['start_date'] : date('Y-m-01');
        $end_date = isset($input['end_date']) ? $input['end_date'] : date('Y-m-d');

        try {
            // 从商品表和成本分析表获取产品成本对比数据
            $product_costs = db('goods')
                ->alias('g')
                ->join('cost_analysis ca', 'g.id = ca.goods_id', 'left')
                ->where([
                    'ca.analysis_date' => ['between', [$start_date, $end_date]]
                ])
                ->field('
                    g.id as goods_id,
                    g.name as goods_name,
                    g.sell as sale_price,
                    AVG(ca.raw_material_cost + ca.auxiliary_material_cost) as material_cost,
                    AVG(ca.direct_labor_cost + ca.indirect_labor_cost) as labor_cost,
                    AVG(ca.facility_cost + ca.management_cost + ca.quality_cost) as overhead_cost
                ')
                ->group('g.id')
                ->select();

            if(empty($product_costs)){
                // 如果没有成本分析数据，使用基础估算
                $products = db('goods')
                    ->field('id as goods_id, name as goods_name, sell as sale_price')
                    ->limit(10)
                    ->select();

                $formatted_data = [];
                foreach($products as $item){
                    $sale_price = floatval($item['sale_price']);
                    $estimated_material_cost = $sale_price * 0.5; // 估算材料成本50%
                    $estimated_labor_cost = $sale_price * 0.2;    // 估算人工成本20%
                    $estimated_overhead_cost = $sale_price * 0.15; // 估算制造费用15%

                    $formatted_data[] = [
                        'goods_id' => $item['goods_id'],
                        'goods_name' => $item['goods_name'],
                        'sale_price' => $sale_price,
                        'material_cost' => $estimated_material_cost,
                        'labor_cost' => $estimated_labor_cost,
                        'overhead_cost' => $estimated_overhead_cost
                    ];
                }

                return json(['state' => 'success', 'data' => $formatted_data]);
            }

            return json(['state' => 'success', 'data' => $product_costs]);

        } catch (\Exception $e) {
            return json(['state' => 'error', 'info' => '获取产品成本对比数据失败：' . $e->getMessage()]);
        }
    }
    
    //获取预测分析数据
    public function get_forecast_analysis(){
        $input = input('post.');

        try {
            // 检查预测分析表是否存在
            try {
                db()->query("SELECT 1 FROM is_forecast_analysis LIMIT 1");

                $where = [
                    'merchant' => Session('is_merchant_id')
                ];

                if(isset($input['forecast_type']) && !empty($input['forecast_type'])){
                    $where['forecast_type'] = $input['forecast_type'];
                }

                if(isset($input['target_type']) && !empty($input['target_type'])){
                    $where['target_type'] = $input['target_type'];
                }

                $list = db('forecast_analysis')
                    ->where($where)
                    ->order('forecast_date desc')
                    ->limit(input('limit', 15))
                    ->select();

                return json(['code' => 0, 'msg' => '', 'count' => count($list), 'data' => $list]);

            } catch(\Exception $e) {
                // 预测分析表不存在，返回空数据
                return json(['code' => 0, 'msg' => '预测分析表不存在', 'count' => 0, 'data' => []]);
            }

        } catch(\Exception $e) {
            return json(['code' => 1, 'msg' => '获取预测分析数据失败: ' . $e->getMessage(), 'data' => []]);
        }
    }
    
    //生成预测分析
    public function generate_forecast(){
        $input = input('post.');
        if(!isset_full($input, 'forecast_type') || !isset_full($input, 'target_id')){
            return json(['state' => 'error', 'info' => '参数不完整']);
        }
        
        try {
            $result = $this->calculateForecast($input);
            return json(['state' => 'success', 'data' => $result]);
        } catch (\Exception $e) {
            return json(['state' => 'error', 'info' => '预测分析失败：' . $e->getMessage()]);
        }
    }
    
    //获取概览数据
    public function get_overview_data(){
        try {
            $start_date = date('Y-m-d', strtotime('-30 days'));
            $end_date = date('Y-m-d');

            // 获取总订单数
            $total_orders = 0;
            try {
                $total_orders = db('production_order')
                    ->where(['merchant' => Session('is_merchant_id')])
                    ->count();
            } catch (\Exception $e) {
                // 生产订单表不存在，使用默认值
                $total_orders = 0;
            }

            // 获取生产统计数据（最近30天）
            $total_production = 0;
            $avg_efficiency = 0;
            $avg_quality = 0;

            try {
                $production_stats = db('production_summary')
                    ->where([
                        'merchant' => Session('is_merchant_id'),
                        'summary_date' => ['between', [$start_date, $end_date]]
                    ])
                    ->field('SUM(actual_qty) as total_qty, AVG(efficiency) as avg_eff, AVG(qualified_rate) as avg_qual')
                    ->find();

                if ($production_stats) {
                    $total_production = $production_stats['total_qty'] ?: 0;
                    $avg_efficiency = $production_stats['avg_eff'] ?: 0;
                    $avg_quality = $production_stats['avg_qual'] ?: 0;
                }
            } catch (\Exception $e) {
                // 生产统计表不存在，使用默认值
                $total_production = 0;
                $avg_efficiency = 0;
                $avg_quality = 0;
            }

            // 获取总成本（最近30天）
            $total_cost = 0;
            try {
                $total_cost = db('cost_analysis')
                    ->where([
                        'merchant' => Session('is_merchant_id'),
                        'analysis_date' => ['between', [$start_date, $end_date]]
                    ])
                    ->sum('total_cost');
            } catch (\Exception $e) {
                // 成本分析表不存在，基于产量估算成本
                $total_cost = $total_production * 12; // 假设单位成本12元
            }

            // 计算利润率（简化计算）
            $total_revenue = $total_production * 15; // 假设平均售价15元
            $profit_margin = $total_revenue > 0 ? (($total_revenue - $total_cost) / $total_revenue) * 100 : 0;

            return json([
                'state' => 'success',
                'data' => [
                    'total_orders' => intval($total_orders),
                    'total_production' => number_format($total_production, 0),
                    'avg_efficiency' => number_format($avg_efficiency, 1) . '%',
                    'avg_quality' => number_format($avg_quality, 1) . '%',
                    'total_cost' => '¥' . number_format($total_cost, 0),
                    'profit_margin' => number_format($profit_margin, 1) . '%'
                ]
            ]);
        } catch (\Exception $e) {
            return json(['state' => 'error', 'info' => '获取概览数据失败：' . $e->getMessage()]);
        }
    }

    //获取最新数据
    public function get_latest_data(){
        try {
            $latest_data = [];

            try {
                // 尝试从生产统计表获取数据
                $latest_data = db('production_summary')
                    ->alias('ps')
                    ->join('goods g', 'ps.goods_id = g.id', 'left')
                    ->where(['ps.merchant' => Session('is_merchant_id')])
                    ->field('ps.summary_date,ps.plan_qty,ps.actual_qty,ps.completion_rate,ps.qualified_rate,ps.efficiency,g.name as goods_name')
                    ->order('ps.summary_date desc, ps.id desc')
                    ->limit(10)
                    ->select();
            } catch (\Exception $e) {
                // 生产统计表不存在，返回空数据
                $latest_data = [];
            }

            $formatted_data = [];
            foreach($latest_data as $item){
                // 尝试获取关联的订单号
                $order_no = '无关联订单';
                try {
                    $order = db('production_order')
                        ->where(['goods_id' => $item['goods_id'] ?? 0])
                        ->order('id desc')
                        ->value('order_no');
                    if ($order) {
                        $order_no = $order;
                    }
                } catch (\Exception $e) {
                    // 订单表不存在，使用默认值
                }

                $formatted_data[] = [
                    'summary_date' => $item['summary_date'] ?? date('Y-m-d'),
                    'order_no' => $order_no,
                    'goods_name' => $item['goods_name'] ?? '未知商品',
                    'plan_qty' => number_format($item['plan_qty'] ?? 0, 0),
                    'actual_qty' => number_format($item['actual_qty'] ?? 0, 0),
                    'completion_rate' => number_format($item['completion_rate'] ?? 0, 1) . '%',
                    'qualified_rate' => number_format($item['qualified_rate'] ?? 0, 1) . '%',
                    'efficiency' => number_format($item['efficiency'] ?? 0, 1) . '%'
                ];
            }

            return json([
                'state' => 'success',
                'data' => $formatted_data
            ]);
        } catch (\Exception $e) {
            return json(['state' => 'error', 'info' => '获取最新数据失败：' . $e->getMessage()]);
        }
    }

    //获取仪表板数据
    public function get_dashboard_data(){
        $date = input('date', date('Y-m-d'));

        // 今日关键指标
        $today_kpi = $this->getTodayKPI($date);

        // 效率趋势
        $efficiency_trend = $this->getEfficiencyTrend($date, 7);

        // 质量趋势
        $quality_trend = $this->getQualityTrend($date, 7);

        // 成本分析
        $cost_analysis = $this->getCostAnalysis($date);

        // 设备状态
        $machine_status = $this->getMachineStatus();

        // 异常统计
        $exception_stats = $this->getExceptionStats($date);

        // 预测信息
        $forecast_info = $this->getLatestForecast();

        return json([
            'state' => 'success',
            'data' => [
                'today_kpi' => $today_kpi,
                'efficiency_trend' => $efficiency_trend,
                'quality_trend' => $quality_trend,
                'cost_analysis' => $cost_analysis,
                'machine_status' => $machine_status,
                'exception_stats' => $exception_stats,
                'forecast_info' => $forecast_info
            ]
        ]);
    }
    
    //计算效率分析
    private function calculateEfficiencyAnalysis($analysis_date, $analysis_type){
        $date_range = $this->getDateRange($analysis_date, $analysis_type);
        
        // 获取生产统计数据
        $summary_data = db('production_summary')
            ->alias('ps')
            ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
            ->join('goods g', 'ps.goods_id = g.id', 'left')
            ->join('user u', 'ps.operator_id = u.id', 'left')
            ->join('work_shift ws', 'ps.shift_id = ws.id', 'left')
            ->where([
                'ps.merchant' => Session('is_merchant_id'),
                'ps.summary_date' => ['between', $date_range]
            ])
            ->field('ps.*,im.name as machine_name,g.name as goods_name')
            ->select();
            
        // 按分析维度分组计算
        $grouped_data = [];
        foreach($summary_data as $item){
            $key = $this->getGroupKey($item, $analysis_type);
            if(!isset($grouped_data[$key])){
                $grouped_data[$key] = [
                    'machine_id' => $item['machine_id'],
                    'goods_id' => $item['goods_id'],
                    'operator_id' => $item['operator_id'],
                    'shift_id' => $item['shift_id'],
                    'planned_output' => 0,
                    'actual_output' => 0,
                    'planned_time' => 0,
                    'actual_time' => 0,
                    'total_cost' => 0,
                    'defect_qty' => 0,
                    'downtime_hours' => 0,
                    'exception_count' => 0
                ];
            }
            
            $grouped_data[$key]['planned_output'] += $item['plan_qty'];
            $grouped_data[$key]['actual_output'] += $item['actual_qty'];
            $grouped_data[$key]['planned_time'] += 8; // 假设标准8小时
            $grouped_data[$key]['actual_time'] += $item['working_hours'];
            $grouped_data[$key]['defect_qty'] += $item['defect_qty'];
            $grouped_data[$key]['downtime_hours'] += $item['downtime_hours'];
            $grouped_data[$key]['exception_count'] += $item['exception_count'];
        }
        
        // 计算效率指标并保存
        foreach($grouped_data as $key => $data){
            $efficiency_data = [
                'merchant' => Session('is_merchant_id'),
                'analysis_date' => $analysis_date,
                'analysis_type' => $analysis_type,
                'machine_id' => $data['machine_id'],
                'goods_id' => $data['goods_id'],
                'operator_id' => $data['operator_id'],
                'shift_id' => $data['shift_id'],
                'planned_output' => $data['planned_output'],
                'actual_output' => $data['actual_output'],
                'planned_time' => $data['planned_time'],
                'actual_time' => $data['actual_time'],
                'downtime_hours' => $data['downtime_hours'],
                'exception_count' => $data['exception_count'],
                'createtime' => time()
            ];
            
            // 计算效率指标
            if($data['planned_output'] > 0){
                $efficiency_data['output_efficiency'] = ($data['actual_output'] / $data['planned_output']) * 100;
            }
            
            if($data['planned_time'] > 0){
                $efficiency_data['time_efficiency'] = ($data['planned_time'] / $data['actual_time']) * 100;
            }
            
            // 计算OEE
            $availability = $data['actual_time'] > 0 ? (($data['actual_time'] - $data['downtime_hours']) / $data['actual_time']) : 0;
            $performance = $data['planned_output'] > 0 ? ($data['actual_output'] / $data['planned_output']) : 0;
            $quality = $data['actual_output'] > 0 ? (($data['actual_output'] - $data['defect_qty']) / $data['actual_output']) : 0;
            
            $efficiency_data['availability'] = $availability * 100;
            $efficiency_data['performance'] = $performance * 100;
            $efficiency_data['quality_rate'] = $quality * 100;
            $efficiency_data['oee'] = $availability * $performance * $quality * 100;
            
            if($data['actual_output'] > 0){
                $efficiency_data['defect_rate'] = ($data['defect_qty'] / $data['actual_output']) * 100;
            }
            
            // 检查是否已存在记录
            $existing = db('efficiency_analysis')
                ->where([
                    'analysis_date' => $analysis_date,
                    'analysis_type' => $analysis_type,
                    'machine_id' => $data['machine_id'],
                    'goods_id' => $data['goods_id'],
                    'operator_id' => $data['operator_id'],
                    'shift_id' => $data['shift_id']
                ])
                ->find();
                
            if($existing){
                db('efficiency_analysis')->where(['id' => $existing['id']])->update($efficiency_data);
            }else{
                db('efficiency_analysis')->insert($efficiency_data);
            }
        }
    }
    
    //计算成本分析
    private function calculateCostAnalysis($analysis_date, $analysis_period){
        $date_range = $this->getDateRange($analysis_date, $analysis_period);
        
        // 获取生产数据
        $production_data = db('production_summary')
            ->where([
                'merchant' => Session('is_merchant_id'),
                'summary_date' => ['between', $date_range]
            ])
            ->group('goods_id')
            ->field('goods_id,SUM(actual_qty) as total_qty,SUM(good_qty) as good_qty,SUM(defect_qty) as defect_qty')
            ->select();
            
        foreach($production_data as $item){
            $cost_data = [
                'merchant' => Session('is_merchant_id'),
                'analysis_date' => $analysis_date,
                'analysis_period' => $analysis_period,
                'goods_id' => $item['goods_id'],
                'production_qty' => $item['total_qty'],
                'createtime' => time()
            ];
            
            // 计算各项成本（这里需要根据实际业务逻辑计算）
            $cost_data['raw_material_cost'] = $this->calculateMaterialCost($item['goods_id'], $item['total_qty']);
            $cost_data['direct_labor_cost'] = $this->calculateLaborCost($item['goods_id'], $item['total_qty']);
            $cost_data['direct_machine_cost'] = $this->calculateMachineCost($item['goods_id'], $item['total_qty']);
            $cost_data['direct_energy_cost'] = $this->calculateEnergyCost($item['goods_id'], $item['total_qty']);
            
            $cost_data['total_direct_cost'] = $cost_data['raw_material_cost'] + $cost_data['direct_labor_cost'] + 
                                            $cost_data['direct_machine_cost'] + $cost_data['direct_energy_cost'];
            
            // 间接成本计算
            $cost_data['indirect_labor_cost'] = $cost_data['total_direct_cost'] * 0.15; // 15%
            $cost_data['facility_cost'] = $cost_data['total_direct_cost'] * 0.10; // 10%
            $cost_data['management_cost'] = $cost_data['total_direct_cost'] * 0.08; // 8%
            $cost_data['quality_cost'] = ($item['defect_qty'] / $item['total_qty']) * $cost_data['total_direct_cost'];
            
            $cost_data['total_indirect_cost'] = $cost_data['indirect_labor_cost'] + $cost_data['facility_cost'] + 
                                              $cost_data['management_cost'] + $cost_data['quality_cost'];
            
            $cost_data['total_cost'] = $cost_data['total_direct_cost'] + $cost_data['total_indirect_cost'];
            
            if($item['total_qty'] > 0){
                $cost_data['unit_cost'] = $cost_data['total_cost'] / $item['total_qty'];
            }
            
            // 成本结构分析
            if($cost_data['total_cost'] > 0){
                $cost_data['material_cost_rate'] = ($cost_data['raw_material_cost'] / $cost_data['total_cost']) * 100;
                $cost_data['labor_cost_rate'] = (($cost_data['direct_labor_cost'] + $cost_data['indirect_labor_cost']) / $cost_data['total_cost']) * 100;
                $cost_data['machine_cost_rate'] = ($cost_data['direct_machine_cost'] / $cost_data['total_cost']) * 100;
                $cost_data['overhead_cost_rate'] = ($cost_data['total_indirect_cost'] / $cost_data['total_cost']) * 100;
            }
            
            // 检查是否已存在记录
            $existing = db('cost_analysis')
                ->where([
                    'analysis_date' => $analysis_date,
                    'analysis_period' => $analysis_period,
                    'goods_id' => $item['goods_id']
                ])
                ->find();
                
            if($existing){
                db('cost_analysis')->where(['id' => $existing['id']])->update($cost_data);
            }else{
                db('cost_analysis')->insert($cost_data);
            }
        }
    }
    
    //获取日期范围
    private function getDateRange($date, $type){
        switch($type){
            case 1: // 日分析
            case 'daily':
                return [$date, $date];
            case 2: // 周分析
            case 'weekly':
                $start = date('Y-m-d', strtotime('monday this week', strtotime($date)));
                $end = date('Y-m-d', strtotime('sunday this week', strtotime($date)));
                return [$start, $end];
            case 3: // 月分析
            case 'monthly':
                $start = date('Y-m-01', strtotime($date));
                $end = date('Y-m-t', strtotime($date));
                return [$start, $end];
            case 4: // 年分析
            case 'yearly':
                $start = date('Y-01-01', strtotime($date));
                $end = date('Y-12-31', strtotime($date));
                return [$start, $end];
            default:
                return [$date, $date];
        }
    }
    
    //获取分组键
    private function getGroupKey($item, $analysis_type){
        switch($analysis_type){
            case 1: // 按设备+商品+班次
                return $item['machine_id'] . '_' . $item['goods_id'] . '_' . $item['shift_id'];
            case 2: // 按设备+商品
                return $item['machine_id'] . '_' . $item['goods_id'];
            case 3: // 按商品
                return $item['goods_id'];
            case 4: // 按设备
                return $item['machine_id'];
            default:
                return $item['machine_id'] . '_' . $item['goods_id'];
        }
    }
    
    //计算材料成本
    private function calculateMaterialCost($goods_id, $qty){
        // 这里应该根据配方和材料价格计算
        // 简化处理，返回固定单价
        return $qty * 8.5; // 假设材料成本8.5元/件
    }
    
    //计算人工成本
    private function calculateLaborCost($goods_id, $qty){
        // 根据工时和人工单价计算
        return $qty * 2.0; // 假设人工成本2.0元/件
    }
    
    //计算设备成本
    private function calculateMachineCost($goods_id, $qty){
        // 根据设备折旧和使用时间计算
        return $qty * 1.5; // 假设设备成本1.5元/件
    }
    
    //计算能源成本
    private function calculateEnergyCost($goods_id, $qty){
        // 根据能耗和电价计算
        return $qty * 0.8; // 假设能源成本0.8元/件
    }

    //计算预测分析
    private function calculateForecast($input){
        $forecast_type = $input['forecast_type'];
        $target_id = $input['target_id'];
        $target_type = isset($input['target_type']) ? $input['target_type'] : 'machine';
        $forecast_period = isset($input['forecast_period']) ? $input['forecast_period'] : 'next_7_days';
        $analysis_days = isset($input['analysis_days']) ? $input['analysis_days'] : 30;

        // 获取历史数据
        $historical_data = $this->getHistoricalData($forecast_type, $target_id, $target_type, $analysis_days);

        if(count($historical_data) < 7){
            throw new \Exception('历史数据不足，无法进行预测分析');
        }

        // 执行预测计算
        $forecast_result = $this->performForecast($historical_data, $forecast_type);

        // 保存预测结果
        $forecast_data = [
            'merchant' => Session('is_merchant_id'),
            'forecast_date' => date('Y-m-d'),
            'forecast_type' => $forecast_type,
            'forecast_period' => $forecast_period,
            'target_id' => $target_id,
            'target_type' => $target_type,
            'historical_data' => json_encode($historical_data),
            'data_points' => count($historical_data),
            'analysis_period_days' => $analysis_days,
            'forecast_value' => $forecast_result['forecast_value'],
            'confidence_level' => $forecast_result['confidence_level'],
            'upper_bound' => $forecast_result['upper_bound'],
            'lower_bound' => $forecast_result['lower_bound'],
            'trend' => $forecast_result['trend'],
            'model_type' => $forecast_result['model_type'],
            'model_accuracy' => $forecast_result['accuracy'],
            'r_squared' => $forecast_result['r_squared'],
            'mae' => $forecast_result['mae'],
            'rmse' => $forecast_result['rmse'],
            'key_factors' => json_encode($forecast_result['key_factors']),
            'recommendations' => $forecast_result['recommendations'],
            'createtime' => time()
        ];

        db('forecast_analysis')->insert($forecast_data);

        return $forecast_result;
    }

    //获取历史数据
    private function getHistoricalData($forecast_type, $target_id, $target_type, $days){
        $end_date = date('Y-m-d');
        $start_date = date('Y-m-d', strtotime("-{$days} days"));

        switch($forecast_type){
            case 1: // 产量预测
                return $this->getProductionHistoryData($target_id, $target_type, $start_date, $end_date);
            case 2: // 质量预测
                return $this->getQualityHistoryData($target_id, $target_type, $start_date, $end_date);
            case 3: // 成本预测
                return $this->getCostHistoryData($target_id, $target_type, $start_date, $end_date);
            case 4: // 设备预测
                return $this->getEquipmentHistoryData($target_id, $target_type, $start_date, $end_date);
            default:
                return [];
        }
    }

    //获取生产历史数据
    private function getProductionHistoryData($target_id, $target_type, $start_date, $end_date){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'summary_date' => ['between', [$start_date, $end_date]]
        ];

        if($target_type == 'machine'){
            $where['machine_id'] = $target_id;
        } elseif($target_type == 'goods'){
            $where['goods_id'] = $target_id;
        }

        $data = db('production_summary')
            ->where($where)
            ->field('summary_date,SUM(actual_qty) as value')
            ->group('summary_date')
            ->order('summary_date asc')
            ->select();

        return array_map(function($item){
            return [
                'date' => $item['summary_date'],
                'value' => floatval($item['value'])
            ];
        }, $data);
    }

    //获取质量历史数据
    private function getQualityHistoryData($target_id, $target_type, $start_date, $end_date){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'summary_date' => ['between', [$start_date, $end_date]]
        ];

        if($target_type == 'machine'){
            $where['machine_id'] = $target_id;
        } elseif($target_type == 'goods'){
            $where['goods_id'] = $target_id;
        }

        $data = db('production_summary')
            ->where($where)
            ->field('summary_date,AVG(qualified_rate) as value')
            ->group('summary_date')
            ->order('summary_date asc')
            ->select();

        return array_map(function($item){
            return [
                'date' => $item['summary_date'],
                'value' => floatval($item['value'])
            ];
        }, $data);
    }

    //执行预测计算
    private function performForecast($historical_data, $forecast_type){
        // 简化的线性回归预测
        $n = count($historical_data);
        $x_values = range(1, $n);
        $y_values = array_column($historical_data, 'value');

        // 计算线性回归参数
        $sum_x = array_sum($x_values);
        $sum_y = array_sum($y_values);
        $sum_xy = 0;
        $sum_x2 = 0;

        for($i = 0; $i < $n; $i++){
            $sum_xy += $x_values[$i] * $y_values[$i];
            $sum_x2 += $x_values[$i] * $x_values[$i];
        }

        $slope = ($n * $sum_xy - $sum_x * $sum_y) / ($n * $sum_x2 - $sum_x * $sum_x);
        $intercept = ($sum_y - $slope * $sum_x) / $n;

        // 预测下一个值
        $next_x = $n + 1;
        $forecast_value = $slope * $next_x + $intercept;

        // 计算预测区间
        $residuals = [];
        for($i = 0; $i < $n; $i++){
            $predicted = $slope * $x_values[$i] + $intercept;
            $residuals[] = $y_values[$i] - $predicted;
        }

        $mse = array_sum(array_map(function($r){ return $r * $r; }, $residuals)) / $n;
        $rmse = sqrt($mse);
        $mae = array_sum(array_map('abs', $residuals)) / $n;

        // 计算R平方
        $y_mean = $sum_y / $n;
        $ss_tot = array_sum(array_map(function($y) use ($y_mean){ return pow($y - $y_mean, 2); }, $y_values));
        $ss_res = array_sum(array_map(function($r){ return $r * $r; }, $residuals));
        $r_squared = 1 - ($ss_res / $ss_tot);

        // 预测区间（95%置信度）
        $confidence_interval = 1.96 * $rmse;
        $upper_bound = $forecast_value + $confidence_interval;
        $lower_bound = $forecast_value - $confidence_interval;

        // 判断趋势
        $trend = $slope > 0.1 ? '上升' : ($slope < -0.1 ? '下降' : '平稳');

        // 生成建议
        $recommendations = $this->generateRecommendations($forecast_type, $trend, $forecast_value, $historical_data);

        return [
            'forecast_value' => round($forecast_value, 2),
            'confidence_level' => 95.0,
            'upper_bound' => round($upper_bound, 2),
            'lower_bound' => round($lower_bound, 2),
            'trend' => $trend,
            'model_type' => 'linear_regression',
            'accuracy' => round(max(0, (1 - $mae / $y_mean) * 100), 2),
            'r_squared' => round($r_squared, 4),
            'mae' => round($mae, 2),
            'rmse' => round($rmse, 2),
            'key_factors' => [
                'historical_trend' => $trend,
                'data_volatility' => round($rmse / $y_mean * 100, 2),
                'seasonal_pattern' => 'none'
            ],
            'recommendations' => $recommendations
        ];
    }

    //生成优化建议
    private function generateRecommendations($forecast_type, $trend, $forecast_value, $historical_data){
        $recommendations = [];

        switch($forecast_type){
            case 1: // 产量预测
                if($trend == '下降'){
                    $recommendations[] = '产量呈下降趋势，建议检查设备状态和工艺参数';
                    $recommendations[] = '考虑增加设备维护频次，提高设备可用率';
                } elseif($trend == '上升'){
                    $recommendations[] = '产量呈上升趋势，建议确保原料供应充足';
                    $recommendations[] = '关注质量控制，避免因产量提升影响质量';
                }
                break;

            case 2: // 质量预测
                if($trend == '下降'){
                    $recommendations[] = '质量呈下降趋势，建议加强过程检验';
                    $recommendations[] = '检查工艺参数稳定性，必要时重新调试';
                } elseif($trend == '上升'){
                    $recommendations[] = '质量持续改善，建议总结经验并推广';
                }
                break;

            case 3: // 成本预测
                if($trend == '上升'){
                    $recommendations[] = '成本呈上升趋势，建议分析成本构成';
                    $recommendations[] = '重点关注材料成本和能源消耗';
                }
                break;
        }

        return implode('; ', $recommendations);
    }

    //获取今日关键指标
    private function getTodayKPI($date){
        $kpi_data = db('performance_kpi')
            ->where([
                'merchant' => Session('is_merchant_id'),
                'kpi_date' => $date,
                'kpi_period' => 'daily'
            ])
            ->select();

        $kpi_summary = [
            'total_score' => 0,
            'target_achievement' => 0,
            'categories' => []
        ];

        $total_weighted_score = 0;
        $total_weight = 0;
        $achievement_count = 0;
        $total_count = 0;

        foreach($kpi_data as $kpi){
            $category = $kpi['category'];
            if(!isset($kpi_summary['categories'][$category])){
                $kpi_summary['categories'][$category] = [
                    'name' => $category,
                    'score' => 0,
                    'weight' => 0,
                    'items' => []
                ];
            }

            $kpi_summary['categories'][$category]['items'][] = [
                'name' => $kpi['kpi_name'],
                'target' => $kpi['target_value'],
                'actual' => $kpi['actual_value'],
                'achievement_rate' => $kpi['achievement_rate'],
                'score' => $kpi['score'],
                'status' => $kpi['status'],
                'trend' => $kpi['trend']
            ];

            $kpi_summary['categories'][$category]['score'] += $kpi['weighted_score'];
            $kpi_summary['categories'][$category]['weight'] += $kpi['weight'];

            $total_weighted_score += $kpi['weighted_score'];
            $total_weight += $kpi['weight'];

            if($kpi['achievement_rate'] >= 100){
                $achievement_count++;
            }
            $total_count++;
        }

        $kpi_summary['total_score'] = $total_weight > 0 ? round($total_weighted_score, 2) : 0;
        $kpi_summary['target_achievement'] = $total_count > 0 ? round(($achievement_count / $total_count) * 100, 1) : 0;

        return $kpi_summary;
    }

    //获取效率趋势
    private function getEfficiencyTrend($date, $days){
        $start_date = date('Y-m-d', strtotime("-{$days} days", strtotime($date)));

        $trend_data = db('production_summary')
            ->where([
                'merchant' => Session('is_merchant_id'),
                'summary_date' => ['between', [$start_date, $date]]
            ])
            ->field('summary_date,AVG(efficiency) as avg_efficiency,AVG(efficiency) as avg_oee')
            ->group('summary_date')
            ->order('summary_date asc')
            ->select();

        // 转换为数组
        $trend_array = $trend_data ? $trend_data->toArray() : [];

        return [
            'dates' => array_column($trend_array, 'summary_date'),
            'efficiency' => array_map(function($item){ return round($item['avg_efficiency'], 1); }, $trend_array),
            'oee' => array_map(function($item){ return round($item['avg_oee'], 1); }, $trend_array)
        ];
    }

    //获取质量趋势
    private function getQualityTrend($date, $days){
        $start_date = date('Y-m-d', strtotime("-{$days} days", strtotime($date)));

        $trend_data = db('production_summary')
            ->where([
                'merchant' => Session('is_merchant_id'),
                'summary_date' => ['between', [$start_date, $date]]
            ])
            ->field('summary_date,AVG(qualified_rate) as avg_qualified_rate,(100-AVG(qualified_rate)) as avg_defect_rate')
            ->group('summary_date')
            ->order('summary_date asc')
            ->select();

        // 转换为数组
        $trend_array = $trend_data ? $trend_data->toArray() : [];

        return [
            'dates' => array_column($trend_array, 'summary_date'),
            'qualified_rate' => array_map(function($item){ return round($item['avg_qualified_rate'], 1); }, $trend_array),
            'defect_rate' => array_map(function($item){ return round($item['avg_defect_rate'], 1); }, $trend_array)
        ];
    }

    //获取成本分析
    private function getCostAnalysis($date){
        try {
            // 检查表是否存在
            try {
                db()->query("SELECT 1 FROM is_cost_analysis LIMIT 1");

                $cost_data = db('cost_analysis')
                    ->where([
                        'merchant' => Session('is_merchant_id'),
                        'analysis_date' => $date
                    ])
                    ->select();

                $total_cost = 0;
                $cost_breakdown = [
                    'material' => 0,
                    'labor' => 0,
                    'machine' => 0,
                    'overhead' => 0
                ];

                foreach($cost_data as $item){
                    $total_cost += $item['total_cost'];
                    $cost_breakdown['material'] += $item['raw_material_cost'] + $item['auxiliary_material_cost'];
                    $cost_breakdown['labor'] += $item['direct_labor_cost'] + $item['indirect_labor_cost'];
                    $cost_breakdown['machine'] += $item['direct_machine_cost'] + $item['indirect_machine_cost'];
                    $cost_breakdown['overhead'] += $item['facility_cost'] + $item['management_cost'];
                }

                // 计算成本占比
                if($total_cost > 0){
                    foreach($cost_breakdown as $key => $value){
                        $cost_breakdown[$key] = round(($value / $total_cost) * 100, 1);
                    }
                }

                return [
                    'total_cost' => round($total_cost, 2),
                    'breakdown' => $cost_breakdown
                ];

            } catch(\Exception $e) {
                // 表不存在，返回默认数据
                return [
                    'total_cost' => 0,
                    'breakdown' => [
                        'material' => 0,
                        'labor' => 0,
                        'machine' => 0,
                        'overhead' => 0
                    ]
                ];
            }

        } catch(\Exception $e) {
            return [
                'total_cost' => 0,
                'breakdown' => [
                    'material' => 0,
                    'labor' => 0,
                    'machine' => 0,
                    'overhead' => 0
                ]
            ];
        }
    }

    //获取设备状态
    private function getMachineStatus(){
        $machines = db('injection_machine')
            ->where([
                'merchant' => Session('is_merchant_id'),
                'status' => 1
            ])
            ->select();

        $status_summary = [
            'total' => count($machines),
            'running' => 0,
            'idle' => 0,
            'maintenance' => 0,
            'fault' => 0
        ];

        // 获取当前生产状态
        $current_schedules = db('production_schedule')
            ->where([
                'merchant' => Session('is_merchant_id'),
                'schedule_date' => date('Y-m-d'),
                'status' => ['in', [1, 3]] // 生产中或暂停
            ])
            ->column('machine_id');

        foreach($machines as $machine){
            if(in_array($machine['id'], $current_schedules)){
                $status_summary['running']++;
            } else {
                $status_summary['idle']++;
            }
        }

        return $status_summary;
    }

    //获取异常统计
    private function getExceptionStats($date){
        try {
            // 检查表是否存在
            try {
                db()->query("SELECT 1 FROM is_production_exception LIMIT 1");

                $exceptions = db('production_exception')
                    ->where([
                        'merchant' => Session('is_merchant_id'),
                        'exception_date' => $date
                    ])
                    ->field('exception_type,COUNT(*) as count')
                    ->group('exception_type')
                    ->select();

                $exception_types = [
                    'equipment_failure' => '设备故障',
                    'quality_issue' => '质量问题',
                    'material_shortage' => '物料短缺',
                    'process_abnormal' => '工艺异常',
                    'safety_incident' => '安全事故',
                    'other' => '其他'
                ];

                $stats = [];
                foreach($exceptions as $item){
                    $stats[] = [
                        'type' => $exception_types[$item['exception_type']] ?? '未知',
                        'count' => $item['count']
                    ];
                }

                return $stats;

            } catch(\Exception $e) {
                // 表不存在，返回空数据
                return [];
            }

        } catch(\Exception $e) {
            return [];
        }
    }

    //获取最新预测信息
    private function getLatestForecast(){
        try {
            // 检查表是否存在
            try {
                db()->query("SELECT 1 FROM is_forecast_analysis LIMIT 1");

                $forecasts = db('forecast_analysis')
                    ->where([
                        'merchant' => Session('is_merchant_id')
                    ])
                    ->order('forecast_date desc')
                    ->limit(5)
                    ->select();

                $forecast_types = [
                    'production' => '产量预测',
                    'quality' => '质量预测',
                    'cost' => '成本预测',
                    'efficiency' => '效率预测'
                ];

                $forecast_info = [];
                foreach($forecasts as $item){
                    $forecast_info[] = [
                        'type' => $forecast_types[$item['forecast_type']] ?? '未知',
                        'forecast_value' => $item['forecast_value'],
                        'trend' => $item['trend'] ?? '稳定',
                        'confidence_level' => $item['confidence_level'] ?? 0,
                        'forecast_date' => $item['forecast_date']
                    ];
                }

                return $forecast_info;

            } catch(\Exception $e) {
                // 表不存在，返回空数据
                return [];
            }

        } catch(\Exception $e) {
            return [];
        }
    }

    //获取成本历史数据
    private function getCostHistoryData($target_id, $target_type, $start_date, $end_date){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'analysis_date' => ['between', [$start_date, $end_date]]
        ];

        if($target_type == 'goods'){
            $where['goods_id'] = $target_id;
        }

        $data = db('cost_analysis')
            ->where($where)
            ->field('analysis_date,AVG(unit_cost) as value')
            ->group('analysis_date')
            ->order('analysis_date asc')
            ->select();

        return array_map(function($item){
            return [
                'date' => $item['analysis_date'],
                'value' => floatval($item['value'])
            ];
        }, $data);
    }

    //获取设备历史数据
    private function getEquipmentHistoryData($target_id, $target_type, $start_date, $end_date){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'summary_date' => ['between', [$start_date, $end_date]]
        ];

        if($target_type == 'machine'){
            $where['machine_id'] = $target_id;
        }

        $data = db('production_summary')
            ->where($where)
            ->field('summary_date,AVG(efficiency) as value')
            ->group('summary_date')
            ->order('summary_date asc')
            ->select();

        return array_map(function($item){
            return [
                'date' => $item['summary_date'],
                'value' => floatval($item['value'])
            ];
        }, $data);
    }
}
