<?php
namespace app\index\model;
use think\Model;

class FormulaDetail extends Model{
    //配方明细表
    protected $table = 'is_formula_detail';
    
    //关联配方信息
    public function formula(){
        return $this->hasOne('app\index\model\ProductionFormula', 'id', 'formula_id');
    }
    
    //物料类型读取器
    protected function getMaterialTypeAttr($val, $data){
        $types = [
            'raw' => '原料',
            'colorant' => '色膏', 
            'additive' => '添加剂'
        ];
        return isset($types[$val]) ? $types[$val] : $val;
    }
    
    //物料类型原始值读取器
    protected function getMaterialTypeValueAttr($val, $data){
        return $data['material_type'];
    }
    
    //配比显示
    protected function getRatioTextAttr($val, $data){
        return $data['ratio'] . '%';
    }
    
    //每公斤用量显示
    protected function getWeightPerKgTextAttr($val, $data){
        return $data['weight_per_kg'] . 'g/kg';
    }
    
    //计算指定重量下的用量
    public function calculateWeight($total_weight){
        return $total_weight * $this->ratio / 100;
    }
    
    //验证配比数据
    public function validateRatio(){
        return $this->ratio > 0 && $this->ratio <= 100;
    }
}
