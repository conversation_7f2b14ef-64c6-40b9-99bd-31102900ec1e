<?php
namespace app\index\model;
use think\Model;

class ProductionReport extends Model{
    //生产报工表
    protected $table = 'is_production_report';

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'createtime';
    protected $updateTime = false; // 表中没有updatetime字段

    protected $type = [
        'more' => 'json'
    ];
    
    //关联排产计划
    public function scheduleinfo(){
        return $this->hasOne('app\index\model\ProductionSchedule', 'id', 'schedule_id');
    }
    
    //关联设备信息
    public function machineinfo(){
        return $this->hasOne('app\index\model\InjectionMachine', 'id', 'machine_id');
    }
    
    //关联班次信息
    public function shiftinfo(){
        return $this->hasOne('app\index\model\WorkShift', 'id', 'shift_id');
    }
    
    //关联商品信息
    public function goodsinfo(){
        return $this->hasOne('app\index\model\Goods', 'id', 'goods_id');
    }
    
    //关联操作员信息
    public function operatorinfo(){
        return $this->hasOne('app\index\model\User', 'id', 'operator_id');
    }
    
    //关联报工人信息
    public function reporterinfo(){
        return $this->hasOne('app\index\model\User', 'id', 'reporter');
    }
    
    //状态读取器
    protected function getStatusAttr($val, $data){
        $status = [
            '0' => '待审核',
            '1' => '已审核',
            '2' => '已驳回'
        ];
        return isset($status[$val]) ? $status[$val] : '未知';
    }
    
    //状态原始值读取器
    protected function getStatusValueAttr($val, $data){
        return $data['status'];
    }
    
    //创建时间读取器
    protected function getCreatetimeAttr($val, $data){
        return $val ? date('Y-m-d H:i:s', $val) : '';
    }
    
    //报工日期读取器
    protected function getReportDateAttr($val, $data){
        return $val ? $val : '';
    }
    
    //报工时间读取器
    protected function getReportTimeAttr($val, $data){
        return $val ? $val : '';
    }
    
    //计算完成率
    public function getCompletionRate(){
        if($this->plan_qty <= 0){
            return 0;
        }
        
        return round(($this->actual_qty / $this->plan_qty) * 100, 2);
    }
    
    //计算合格率
    public function getQualifiedRate(){
        if($this->actual_qty <= 0){
            return 0;
        }
        
        return round(($this->good_qty / $this->actual_qty) * 100, 2);
    }
    
    //计算效率
    public function getEfficiency(){
        $total_hours = $this->working_hours + $this->downtime_hours;
        if($total_hours <= 0){
            return 0;
        }
        
        return round(($this->working_hours / $total_hours) * 100, 2);
    }
    
    //获取报工统计信息
    public function getReportStatistics(){
        return [
            'basic_info' => [
                'report_date' => $this->report_date,
                'report_time' => $this->report_time,
                'machine_name' => $this->machineinfo ? $this->machineinfo->name : '',
                'shift_name' => $this->shiftinfo ? $this->shiftinfo->name : '',
                'goods_name' => $this->goodsinfo ? $this->goodsinfo->name : '',
                'operator_name' => $this->operatorinfo ? $this->operatorinfo->name : ''
            ],
            'production_data' => [
                'plan_qty' => $this->plan_qty,
                'actual_qty' => $this->actual_qty,
                'good_qty' => $this->good_qty,
                'defect_qty' => $this->defect_qty,
                'completion_rate' => $this->getCompletionRate(),
                'qualified_rate' => $this->getQualifiedRate()
            ],
            'time_data' => [
                'working_hours' => $this->working_hours,
                'downtime_hours' => $this->downtime_hours,
                'efficiency' => $this->getEfficiency()
            ]
        ];
    }
    
    //根据日期获取报工记录
    public static function getByDate($date, $machine_id = null, $shift_id = null){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'report_date' => $date
        ];
        
        if($machine_id){
            $where['machine_id'] = $machine_id;
        }
        
        if($shift_id){
            $where['shift_id'] = $shift_id;
        }
        
        return self::where($where)
            ->with(['machineinfo', 'shiftinfo', 'goodsinfo', 'operatorinfo'])
            ->order('report_time desc')
            ->select();
    }
    
    //根据设备获取报工记录
    public static function getByMachine($machine_id, $start_date = null, $end_date = null){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'machine_id' => $machine_id
        ];
        
        if($start_date){
            $where[] = ['report_date', '>=', $start_date];
        }
        
        if($end_date){
            $where[] = ['report_date', '<=', $end_date];
        }
        
        return self::where($where)
            ->with(['shiftinfo', 'goodsinfo', 'operatorinfo'])
            ->order('report_date desc, report_time desc')
            ->select();
    }
    
    //根据操作员获取报工记录
    public static function getByOperator($operator_id, $start_date = null, $end_date = null){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'operator_id' => $operator_id
        ];
        
        if($start_date){
            $where[] = ['report_date', '>=', $start_date];
        }
        
        if($end_date){
            $where[] = ['report_date', '<=', $end_date];
        }
        
        return self::where($where)
            ->with(['machineinfo', 'shiftinfo', 'goodsinfo'])
            ->order('report_date desc, report_time desc')
            ->select();
    }
    
    //验证报工数据
    public function validateReportData(){
        $errors = [];
        
        //检查数量逻辑
        if($this->good_qty + $this->defect_qty != $this->actual_qty){
            $errors[] = '合格数量 + 不良数量 应等于实际产量';
        }
        
        if($this->actual_qty > $this->plan_qty * 1.2){
            $errors[] = '实际产量超出计划产量过多，请检查';
        }
        
        //检查时间逻辑
        if($this->working_hours + $this->downtime_hours > 24){
            $errors[] = '工作时长 + 停机时长 不能超过24小时';
        }
        
        if($this->working_hours <= 0){
            $errors[] = '工作时长必须大于0';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
