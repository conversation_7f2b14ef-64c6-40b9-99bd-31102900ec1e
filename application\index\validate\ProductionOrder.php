<?php
namespace app\index\validate;
use think\Validate;

class ProductionOrder extends Validate{
    protected $rule = [
        'order_no' => 'require|max:32',
        'goods_id' => 'require|integer|gt:0',
        'plan_qty' => 'require|float|gt:0',
        'plan_start_date' => 'require|date',
        'plan_end_date' => 'require|date',
        'priority' => 'require|in:1,2,3,4',
        'formula_id' => 'integer|egt:0',
        'mold_id' => 'integer|egt:0',
        'process_id' => 'integer|egt:0',
        'data' => 'max:256'
    ];
    
    protected $message = [
        'order_no.require' => '订单号不能为空',
        'order_no.max' => '订单号长度不能超过32个字符',
        'goods_id.require' => '请选择生产商品',
        'goods_id.integer' => '商品ID必须为整数',
        'goods_id.gt' => '请选择有效的商品',
        'plan_qty.require' => '计划数量不能为空',
        'plan_qty.float' => '计划数量必须为数字',
        'plan_qty.gt' => '计划数量必须大于0',
        'plan_start_date.require' => '计划开始日期不能为空',
        'plan_start_date.date' => '计划开始日期格式不正确',
        'plan_end_date.require' => '计划完成日期不能为空',
        'plan_end_date.date' => '计划完成日期格式不正确',
        'priority.require' => '请选择优先级',
        'priority.in' => '优先级值不正确',
        'formula_id.integer' => '配方ID必须为整数',
        'mold_id.integer' => '模具ID必须为整数',
        'process_id.integer' => '工艺参数ID必须为整数',
        'data.max' => '备注信息长度不能超过256个字符'
    ];
    
    protected $scene = [
        'add' => ['order_no', 'goods_id', 'plan_qty', 'plan_start_date', 'plan_end_date', 'priority', 'formula_id', 'mold_id', 'process_id', 'data'],
        'edit' => ['order_no', 'goods_id', 'plan_qty', 'plan_start_date', 'plan_end_date', 'priority', 'formula_id', 'mold_id', 'process_id', 'data']
    ];
    
    //自定义验证规则
    protected function checkDateRange($value, $rule, $data){
        if(isset($data['plan_start_date']) && isset($data['plan_end_date'])){
            if(strtotime($data['plan_end_date']) <= strtotime($data['plan_start_date'])){
                return '计划完成日期必须晚于计划开始日期';
            }
        }
        return true;
    }
    
    //验证商品是否存在
    protected function checkGoods($value, $rule, $data){
        $goods = db('goods')->where(['id' => $value, 'merchant' => Session('is_merchant_id')])->find();
        if(!$goods){
            return '选择的商品不存在';
        }
        return true;
    }
    
    //验证配方是否存在且可用
    protected function checkFormula($value, $rule, $data){
        if(empty($value)) return true; //配方可以为空
        
        $formula = db('production_formula')
            ->where([
                'id' => $value,
                'merchant' => Session('is_merchant_id'),
                'status' => 1
            ])
            ->find();
            
        if(!$formula){
            return '选择的配方不存在或不可用';
        }
        
        //检查配方是否适用于该商品
        if(isset($data['goods_id']) && $formula['goods_id'] != $data['goods_id']){
            return '选择的配方不适用于该商品';
        }
        
        return true;
    }
    
    //验证模具是否存在且可用
    protected function checkMold($value, $rule, $data){
        if(empty($value)) return true; //模具可以为空
        
        $mold = db('mold')
            ->where([
                'id' => $value,
                'merchant' => Session('is_merchant_id'),
                'status' => 1
            ])
            ->find();
            
        if(!$mold){
            return '选择的模具不存在或不可用';
        }
        
        //检查模具是否适用于该商品
        if(isset($data['goods_id']) && $mold['goods_id'] != $data['goods_id']){
            return '选择的模具不适用于该商品';
        }
        
        return true;
    }
}
