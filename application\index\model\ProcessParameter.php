<?php
namespace app\index\model;
use think\Model;

class ProcessParameter extends Model{
    //工艺参数表
    protected $table = 'is_process_parameter';
    
    protected $type = [
        'more' => 'json'
    ];
    
    //关联商品信息
    public function goodsinfo(){
        return $this->hasOne('app\index\model\Goods', 'id', 'goods_id');
    }

    //关联模具信息
    public function moldinfo(){
        return $this->hasOne('app\index\model\Mold', 'id', 'mold_id');
    }

    //关联设备信息
    public function machineinfo(){
        return $this->hasOne('app\index\model\InjectionMachine', 'id', 'machine_id');
    }
    
    //状态读取器
    protected function getStatusAttr($val, $data){
        $status = [
            '0' => '停用',
            '1' => '启用'
        ];
        return isset($status[$val]) ? $status[$val] : '未知';
    }
    
    //状态原始值读取器
    protected function getStatusValueAttr($val, $data){
        return $data['status'];
    }
    
    //创建时间读取器
    protected function getCreatetimeAttr($val, $data){
        return $val ? date('Y-m-d H:i:s', $val) : '';
    }
    
    //更新时间读取器
    protected function getUpdatetimeAttr($val, $data){
        return $val ? date('Y-m-d H:i:s', $val) : '';
    }
    
    //温度格式化
    protected function getBarrelTemp1TextAttr($val, $data){
        return $data['barrel_temp_1'] > 0 ? $data['barrel_temp_1'] . '°C' : '';
    }
    
    protected function getBarrelTemp2TextAttr($val, $data){
        return $data['barrel_temp_2'] > 0 ? $data['barrel_temp_2'] . '°C' : '';
    }
    
    protected function getBarrelTemp3TextAttr($val, $data){
        return $data['barrel_temp_3'] > 0 ? $data['barrel_temp_3'] . '°C' : '';
    }
    
    protected function getNozzleTempTextAttr($val, $data){
        return $data['nozzle_temp'] > 0 ? $data['nozzle_temp'] . '°C' : '';
    }
    
    protected function getMoldTempUpperTextAttr($val, $data){
        return $data['mold_temp_upper'] > 0 ? $data['mold_temp_upper'] . '°C' : '';
    }
    
    protected function getMoldTempLowerTextAttr($val, $data){
        return $data['mold_temp_lower'] > 0 ? $data['mold_temp_lower'] . '°C' : '';
    }
    
    //压力格式化
    protected function getInjectionPressureTextAttr($val, $data){
        return $data['injection_pressure'] > 0 ? $data['injection_pressure'] . 'bar' : '';
    }
    
    protected function getHoldingPressureTextAttr($val, $data){
        return $data['holding_pressure'] > 0 ? $data['holding_pressure'] . 'bar' : '';
    }
    
    protected function getClampingForceTextAttr($val, $data){
        return $data['clamping_force'] > 0 ? $data['clamping_force'] . 'T' : '';
    }
    
    //时间格式化
    protected function getInjectionTimeTextAttr($val, $data){
        return $data['injection_time'] > 0 ? $data['injection_time'] . 's' : '';
    }
    
    protected function getHoldingTimeTextAttr($val, $data){
        return $data['holding_time'] > 0 ? $data['holding_time'] . 's' : '';
    }
    
    protected function getCoolingTimeTextAttr($val, $data){
        return $data['cooling_time'] > 0 ? $data['cooling_time'] . 's' : '';
    }
    
    protected function getCycleTimeTextAttr($val, $data){
        return $data['cycle_time'] > 0 ? $data['cycle_time'] . 's' : '';
    }
    
    //获取完整的工艺参数信息
    public function getFullParameters(){
        return [
            'basic_info' => [
                'name' => $this->name,
                'code' => $this->code,
                'goods_name' => $this->goodsinfo ? $this->goodsinfo->name : '',
                'mold_name' => $this->moldinfo ? $this->moldinfo->name : '',
                'machine_name' => $this->machineinfo ? $this->machineinfo->name : ''
            ],
            'temperature' => [
                'barrel_temp_1' => $this->barrel_temp_1_text,
                'barrel_temp_2' => $this->barrel_temp_2_text,
                'barrel_temp_3' => $this->barrel_temp_3_text,
                'nozzle_temp' => $this->nozzle_temp_text,
                'mold_temp_upper' => $this->mold_temp_upper_text,
                'mold_temp_lower' => $this->mold_temp_lower_text
            ],
            'pressure' => [
                'injection_pressure' => $this->injection_pressure_text,
                'holding_pressure' => $this->holding_pressure_text,
                'clamping_force' => $this->clamping_force_text
            ],
            'timing' => [
                'injection_time' => $this->injection_time_text,
                'holding_time' => $this->holding_time_text,
                'cooling_time' => $this->cooling_time_text,
                'cycle_time' => $this->cycle_time_text
            ],
            'other' => [
                'shot_weight' => $this->shot_weight > 0 ? $this->shot_weight . 'g' : '',
                'screw_speed' => $this->screw_speed > 0 ? $this->screw_speed . 'rpm' : '',
                'back_pressure' => $this->back_pressure > 0 ? $this->back_pressure . 'bar' : '',
                'decompression' => $this->decompression > 0 ? $this->decompression . 'mm' : ''
            ]
        ];
    }
    
    //验证工艺参数的合理性
    public function validateParameters(){
        $errors = [];
        
        //检查温度参数
        if($this->barrel_temp_1 > 0 && $this->barrel_temp_2 > 0 && $this->barrel_temp_1 >= $this->barrel_temp_2){
            $errors[] = '料筒温度1应小于料筒温度2';
        }
        
        if($this->barrel_temp_2 > 0 && $this->barrel_temp_3 > 0 && $this->barrel_temp_2 >= $this->barrel_temp_3){
            $errors[] = '料筒温度2应小于料筒温度3';
        }
        
        //检查压力参数
        if($this->injection_pressure > 0 && $this->holding_pressure > 0 && $this->holding_pressure > $this->injection_pressure){
            $errors[] = '保压压力不应大于注射压力';
        }
        
        //检查时间参数
        if($this->injection_time > 0 && $this->holding_time > 0 && $this->cooling_time > 0){
            $total_time = $this->injection_time + $this->holding_time + $this->cooling_time;
            if($this->cycle_time > 0 && abs($this->cycle_time - $total_time) > 5){
                $errors[] = '周期时间与各阶段时间总和差异过大';
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    //根据商品ID获取工艺参数
    public static function getByGoodsId($goods_id){
        return self::where(['goods_id' => $goods_id, 'status' => 1])
            ->with(['moldinfo', 'machineinfo'])
            ->order('id desc')
            ->select();
    }
    
    //根据模具ID获取工艺参数
    public static function getByMoldId($mold_id){
        return self::where(['mold_id' => $mold_id, 'status' => 1])
            ->with(['goodsinfo', 'machineinfo'])
            ->order('id desc')
            ->select();
    }
    
    //复制工艺参数
    public function copyTo($new_goods_id, $new_mold_id = null){
        $data = $this->toArray();
        unset($data['id']);
        unset($data['createtime']);
        unset($data['updatetime']);
        
        $data['goods_id'] = $new_goods_id;
        if($new_mold_id){
            $data['mold_id'] = $new_mold_id;
        }
        $data['name'] = $data['name'] . '_副本';
        $data['code'] = $data['code'] . '_COPY_' . date('YmdHis');
        $data['createtime'] = time();
        $data['updatetime'] = time();
        
        return self::create($data);
    }
}
