-- 修复 purchaseinfo 表 room 字段默认值问题
-- 执行日期: 2024-12-31
-- 问题描述: SQLSTATE[HY000]: General error: 1364 Field 'room' doesn't have a default value
-- 解决方案: 为 room 字段添加默认值 0（表示未分配仓储位置）

USE ZS_ERP;

-- 1. 检查当前 room 字段的定义
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'ZS_ERP' 
  AND TABLE_NAME = 'is_purchaseinfo' 
  AND COLUMN_NAME = 'room';

-- 2. 修改 room 字段，添加默认值
ALTER TABLE `is_purchaseinfo` 
MODIFY COLUMN `room` int(11) NOT NULL DEFAULT 0 COMMENT '仓储位置ID[0:未分配|其他:具体仓储位置]';

-- 3. 更新现有记录中 room 为 NULL 的数据
UPDATE `is_purchaseinfo` SET `room` = 0 WHERE `room` IS NULL;

-- 4. 同时检查和修复其他可能的字段
-- 检查 batch 字段
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'ZS_ERP' 
  AND TABLE_NAME = 'is_purchaseinfo' 
  AND COLUMN_NAME = 'batch';

-- 修复 batch 字段（如果需要）
ALTER TABLE `is_purchaseinfo` 
MODIFY COLUMN `batch` varchar(32) NOT NULL DEFAULT '' COMMENT '批次号';

-- 检查 serial 字段
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'ZS_ERP' 
  AND TABLE_NAME = 'is_purchaseinfo' 
  AND COLUMN_NAME = 'serial';

-- 修复 serial 字段（如果需要）
ALTER TABLE `is_purchaseinfo` 
MODIFY COLUMN `serial` text COMMENT '串码信息';

-- 检查 data 字段
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'ZS_ERP' 
  AND TABLE_NAME = 'is_purchaseinfo' 
  AND COLUMN_NAME = 'data';

-- 修复 data 字段（如果需要）
ALTER TABLE `is_purchaseinfo` 
MODIFY COLUMN `data` varchar(128) NOT NULL DEFAULT '' COMMENT '备注信息';

-- 5. 验证修复结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'ZS_ERP' 
  AND TABLE_NAME = 'is_purchaseinfo' 
  AND COLUMN_NAME IN ('room', 'batch', 'serial', 'data');

-- 6. 检查数据完整性
SELECT COUNT(*) as total_records FROM `is_purchaseinfo`;
SELECT COUNT(*) as null_room_records FROM `is_purchaseinfo` WHERE `room` IS NULL;
SELECT COUNT(*) as unassigned_room_records FROM `is_purchaseinfo` WHERE `room` = 0;

-- 修复完成提示
SELECT '购货单详情表字段修复完成！room 字段现在有默认值 0，表示未分配仓储位置' as status;
