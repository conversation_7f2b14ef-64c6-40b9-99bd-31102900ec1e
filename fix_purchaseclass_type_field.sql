-- 修复 purchaseclass 表 type 字段默认值问题
-- 执行日期: 2024-12-31
-- 问题描述: SQLSTATE[HY000]: General error: 1364 Field 'type' doesn't have a default value
-- 解决方案: 为 type 字段添加默认值 0（未审核状态）

USE ZS_ERP;

-- 1. 检查当前 type 字段的定义
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'ZS_ERP' 
  AND TABLE_NAME = 'is_purchaseclass' 
  AND COLUMN_NAME = 'type';

-- 2. 修改 type 字段，添加默认值
ALTER TABLE `is_purchaseclass` 
MODIFY COLUMN `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '审核状态[0:未审核|1:已审核]';

-- 3. 更新现有记录中 type 为 NULL 的数据
UPDATE `is_purchaseclass` SET `type` = 0 WHERE `type` IS NULL;

-- 4. 验证修复结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'ZS_ERP' 
  AND TABLE_NAME = 'is_purchaseclass' 
  AND COLUMN_NAME = 'type';

-- 5. 检查数据完整性
SELECT COUNT(*) as total_records FROM `is_purchaseclass`;
SELECT COUNT(*) as null_type_records FROM `is_purchaseclass` WHERE `type` IS NULL;
SELECT COUNT(*) as unaudited_records FROM `is_purchaseclass` WHERE `type` = 0;
SELECT COUNT(*) as audited_records FROM `is_purchaseclass` WHERE `type` = 1;

-- 修复完成提示
SELECT '修复完成！type 字段现在有默认值 0，表示未审核状态' as status;
