<?php
namespace app\index\model;
use think\Model;

class ProductionFormula extends Model{
    //生产配方表
    protected $table = 'is_production_formula';
    
    protected $type = [
        'more' => 'json'
    ];
    
    //关联商品信息
    public function goodsinfo(){
        return $this->hasOne('app\index\model\Goods', 'id', 'goods_id');
    }

    //关联创建人信息
    public function creatorinfo(){
        return $this->hasOne('app\index\model\User', 'id', 'creator');
    }

    //关联审核人信息
    public function auditorinfo(){
        return $this->hasOne('app\index\model\User', 'id', 'auditor');
    }

    //关联配方明细
    public function details(){
        return $this->hasMany('app\index\model\FormulaDetail', 'formula_id', 'id')->order('sort asc, id asc');
    }
    
    //状态读取器
    protected function getStatusAttr($val, $data){
        $status = ['0' => '停用', '1' => '启用'];
        return isset($status[$val]) ? $status[$val] : '未知';
    }
    
    //状态原始值读取器
    protected function getStatusValueAttr($val, $data){
        return $data['status'];
    }
    
    //创建时间读取器
    protected function getCreatetimeAttr($val, $data){
        return $val ? date('Y-m-d H:i:s', $val) : '';
    }
    
    //创建时间原始值读取器
    protected function getCreatetimeValueAttr($val, $data){
        return $data['createtime'];
    }
    
    //审核时间读取器
    protected function getAuditTimeAttr($val, $data){
        return $val ? date('Y-m-d H:i:s', $val) : '';
    }
    
    //审核时间原始值读取器
    protected function getAuditTimeValueAttr($val, $data){
        return $data['audit_time'];
    }
    
    //获取配方完整信息(包含明细)
    public function getFullInfo(){
        $formula = $this->toArray();
        $formula['details'] = $this->details()->select()->toArray();
        return $formula;
    }
    
    //获取配方成本计算
    public function getCostCalculation($base_weight = 1000){
        $details = $this->details()->select();
        $total_cost = 0;
        $cost_breakdown = [];
        
        foreach($details as $detail){
            //这里需要根据实际的物料价格计算
            //假设从商品表或物料表获取价格
            $material_price = $this->getMaterialPrice($detail->material_code);
            $weight = $base_weight * $detail->ratio / 100;
            $cost = $weight * $material_price / 1000; //转换为公斤计算
            
            $cost_breakdown[] = [
                'material_name' => $detail->material_name,
                'ratio' => $detail->ratio,
                'weight' => $weight,
                'price' => $material_price,
                'cost' => $cost
            ];
            
            $total_cost += $cost;
        }
        
        return [
            'total_cost' => $total_cost,
            'breakdown' => $cost_breakdown,
            'base_weight' => $base_weight
        ];
    }
    
    //获取物料价格(需要根据实际业务逻辑实现)
    private function getMaterialPrice($material_code){
        //这里可以从商品表或专门的物料价格表获取
        //暂时返回默认价格
        return 10.00; //每公斤10元
    }
    
    //验证配方比例总和
    public function validateRatioSum(){
        $details = $this->details()->select();
        $total_ratio = 0;
        
        foreach($details as $detail){
            $total_ratio += $detail->ratio;
        }
        
        //允许一定的误差范围
        return abs($total_ratio - 100) <= 0.01;
    }
    
    //获取配方使用统计
    public function getUsageStats($start_date = null, $end_date = null){
        if(!$start_date) $start_date = date('Y-m-01');
        if(!$end_date) $end_date = date('Y-m-d');
        
        $stats = db('production_order')
            ->where([
                'formula_id' => $this->id,
                'plan_start_date' => ['between', [$start_date, $end_date]]
            ])
            ->field('COUNT(*) as order_count, SUM(plan_qty) as total_qty')
            ->find();
            
        return $stats;
    }
    
    //检查配方是否可以删除
    public function canDelete(){
        //检查是否有生产订单使用
        $order_count = db('production_order')->where(['formula_id' => $this->id])->count();
        return $order_count == 0;
    }
    
    //生成配方标签(用于打印)
    public function generateLabel(){
        $details = $this->details()->select()->toArray();
        
        return [
            'formula_info' => [
                'name' => $this->name,
                'code' => $this->code,
                'version' => $this->version,
                'goods_name' => $this->goodsinfo ? $this->goodsinfo->name : '',
                'create_date' => date('Y-m-d', $this->createtime_value)
            ],
            'materials' => $details,
            'qr_code' => $this->generateQRCode()
        ];
    }
    
    //生成配方二维码
    private function generateQRCode(){
        //生成包含配方信息的二维码内容
        $qr_data = [
            'type' => 'formula',
            'id' => $this->id,
            'code' => $this->code,
            'version' => $this->version
        ];
        
        return json_encode($qr_data);
    }
}
