<?php
namespace app\index\controller;
use app\index\controller\Acl;
use app\index\model\ProductionSchedule as ProductionScheduleModel;
use app\index\model\ProductionOrder;

class ProductionSchedule extends Acl {
    //生产排产管理模块
    
    //主视图
    public function main(){
        return $this->fetch();
    }
    
    //排产看板
    public function board(){
        return $this->fetch();
    }
    
    //获取排产列表
    public function get_list(){
        $input = input('post.');
        $where = auth('production_schedule', []);
        
        //搜索条件
        if(isset_full($input, 'schedule_date')){
            $where[] = ['schedule_date', '=', $input['schedule_date']];
        }
        if(isset_full($input, 'machine_id')){
            $where[] = ['machine_id', '=', $input['machine_id']];
        }
        if(isset_full($input, 'shift_id')){
            $where[] = ['shift_id', '=', $input['shift_id']];
        }
        if(isset_full($input, 'status')){
            $where[] = ['status', '=', $input['status']];
        }
        if(isset_full($input, 'start_date')){
            $where[] = ['schedule_date', '>=', $input['start_date']];
        }
        if(isset_full($input, 'end_date')){
            $where[] = ['schedule_date', '<=', $input['end_date']];
        }
        
        $list = ProductionScheduleModel::with(['orderinfo', 'machineinfo', 'shiftinfo', 'operatorinfo'])
            ->where($where)
            ->order('schedule_date asc, plan_start_time asc')
            ->paginate(input('limit', 15))
            ->toArray();
            
        return json(['code' => 0, 'msg' => '', 'count' => $list['total'], 'data' => $list['data']]);
    }
    
    //获取排产看板数据
    public function get_board_data(){
        $input = input('post.');
        $date = isset($input['date']) ? $input['date'] : date('Y-m-d');
        
        //获取当日排产数据
        $schedules = db('production_schedule')
            ->alias('ps')
            ->join('production_order po', 'ps.production_order_id = po.id', 'left')
            ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
            ->join('goods g', 'po.goods_id = g.id', 'left')
            ->join('work_shift ws', 'ps.shift_id = ws.id', 'left')
            ->join('user u', 'ps.operator_id = u.id', 'left')
            ->where([
                'ps.merchant' => Session('is_merchant_id'),
                'ps.schedule_date' => $date
            ])
            ->field('ps.*,po.order_no,im.name as machine_name,g.name as goods_name,
                     ws.name as shift_name,u.name as operator_name')
            ->order('ps.plan_start_time asc')
            ->select()
            ->toArray();
            
        //按设备分组
        $board_data = [];
        foreach($schedules as $schedule){
            $machine_id = $schedule['machine_id'];
            if(!isset($board_data[$machine_id])){
                $board_data[$machine_id] = [
                    'machine_id' => $machine_id,
                    'machine_name' => $schedule['machine_name'],
                    'schedules' => []
                ];
            }
            
            //计算进度
            if($schedule['plan_qty'] > 0){
                $schedule['progress'] = round(($schedule['actual_qty'] / $schedule['plan_qty']) * 100, 1);
            }else{
                $schedule['progress'] = 0;
            }
            
            $board_data[$machine_id]['schedules'][] = $schedule;
        }
        
        return json(['state' => 'success', 'data' => array_values($board_data)]);
    }
    
    //获取甘特图数据
    public function get_gantt_data(){
        $input = input('post.');
        $start_date = isset($input['start_date']) ? $input['start_date'] : date('Y-m-d');
        $end_date = isset($input['end_date']) ? $input['end_date'] : date('Y-m-d', strtotime('+7 days'));
        
        $schedules = db('production_schedule')
            ->alias('ps')
            ->join('production_order po', 'ps.production_order_id = po.id', 'left')
            ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
            ->join('goods g', 'po.goods_id = g.id', 'left')
            ->where([
                'ps.merchant' => Session('is_merchant_id'),
                'ps.schedule_date' => ['between', [$start_date, $end_date]]
            ])
            ->field('ps.*,po.order_no,im.name as machine_name,g.name as goods_name')
            ->order('ps.schedule_date asc, ps.plan_start_time asc')
            ->select()
            ->toArray();
            
        //转换为甘特图格式
        $gantt_data = [];
        foreach($schedules as $schedule){
            $gantt_data[] = [
                'id' => $schedule['id'],
                'text' => $schedule['order_no'] . ' - ' . $schedule['goods_name'],
                'start_date' => $schedule['plan_start_time'],
                'end_date' => $schedule['plan_end_time'],
                'duration' => (strtotime($schedule['plan_end_time']) - strtotime($schedule['plan_start_time'])) / 3600,
                'progress' => $schedule['plan_qty'] > 0 ? ($schedule['actual_qty'] / $schedule['plan_qty']) : 0,
                'machine' => $schedule['machine_name'],
                'status' => $schedule['status'],
                'color' => $this->getStatusColor($schedule['status'])
            ];
        }
        
        return json(['state' => 'success', 'data' => $gantt_data]);
    }
    
    //产能分析
    public function capacity_analysis(){
        $input = input('post.');
        $date = isset($input['date']) ? $input['date'] : date('Y-m-d');
        
        $analysis = db('capacity_analysis')
            ->alias('ca')
            ->join('injection_machine im', 'ca.machine_id = im.id', 'left')
            ->join('work_shift ws', 'ca.shift_id = ws.id', 'left')
            ->where([
                'ca.merchant' => Session('is_merchant_id'),
                'ca.analysis_date' => $date
            ])
            ->field('ca.*,im.name as machine_name,ws.name as shift_name')
            ->order('im.name asc, ws.sort asc')
            ->select()
            ->toArray();
            
        return json(['state' => 'success', 'data' => $analysis]);
    }
    
    //排产冲突检测
    public function conflict_detection(){
        $input = input('post.');
        $date = isset($input['date']) ? $input['date'] : date('Y-m-d');
        
        //检测设备冲突
        $conflicts = $this->detectScheduleConflicts($date);
        
        return json(['state' => 'success', 'data' => $conflicts]);
    }
    
    //新增排产页面
    public function add(){
        return $this->fetch();
    }

    //新增|更新排产计划
    public function set(){
        $input = input('post.');

        try {
            // 判断是新增还是更新：如果没有id或id为空，则为新增；否则为更新
            if(!isset($input['id']) || empty($input['id'])){
                //新增排产

                // 检查Session数据
                $merchant_id = Session('is_merchant_id');
                $user_id = Session('is_user_id');

                if(empty($merchant_id)){
                    return json(['state' => 'error', 'info' => '商户信息缺失，请重新登录']);
                }
                if(empty($user_id)){
                    return json(['state' => 'error', 'info' => '用户信息缺失，请重新登录']);
                }

                // 基础验证
                if(empty($input['production_order_id'])){
                    return json(['state' => 'error', 'info' => '生产订单ID不能为空']);
                }
                if(empty($input['machine_id'])){
                    return json(['state' => 'error', 'info' => '请选择生产设备']);
                }
                if(empty($input['shift_id'])){
                    return json(['state' => 'error', 'info' => '请选择生产班次']);
                }
                if(empty($input['schedule_date'])){
                    return json(['state' => 'error', 'info' => '排产日期不能为空']);
                }
                if(empty($input['plan_qty']) || $input['plan_qty'] <= 0){
                    return json(['state' => 'error', 'info' => '计划数量必须大于0']);
                }

                // 验证排产量不能超过订单计划数量
                $order = db('production_order')
                    ->where(['id' => $input['production_order_id'], 'merchant' => $merchant_id])
                    ->find();

                if(!$order){
                    return json(['state' => 'error', 'info' => '生产订单不存在']);
                }

                // 验证生产订单状态
                $status_text_to_value = [
                    '待排产' => 0,
                    '已排产' => 1,
                    '生产中' => 2,
                    '已完成' => 3,
                    '已取消' => 4
                ];

                $status_value = is_string($order['status']) ?
                    ($status_text_to_value[$order['status']] ?? 0) :
                    (int)$order['status'];

                if($status_value > 1){
                    $status_names = ['待排产', '已排产', '生产中', '已完成', '已取消'];
                    $status_name = $status_names[$status_value] ?? '未知';
                    return json(['state' => 'error', 'info' => '只能为待排产或已排产状态的订单进行排产，当前状态：' . $status_name]);
                }

                // 计算该订单已排产的总数量
                $scheduled_qty = db('production_schedule')
                    ->where([
                        'production_order_id' => $input['production_order_id'],
                        'merchant' => $merchant_id,
                        'status' => ['in', [0, 1, 3]] // 待生产、生产中、暂停状态的排产
                    ])
                    ->sum('plan_qty');

                $scheduled_qty = $scheduled_qty ?: 0;
                $order_plan_qty = floatval($order['plan_qty']);
                $max_allowed_qty = $order_plan_qty * 1.1; // 最大允许排产数量（110%）
                $total_scheduled_qty = $scheduled_qty + floatval($input['plan_qty']);

                if($total_scheduled_qty > $max_allowed_qty){
                    return json([
                        'state' => 'error',
                        'info' => sprintf(
                            '排产数量超限！订单数量：%.2f，最大允许排产：%.2f（110%%），已排产：%.2f，本次排产：%.2f，总计：%.2f',
                            $order_plan_qty,
                            $max_allowed_qty,
                            $scheduled_qty,
                            floatval($input['plan_qty']),
                            $total_scheduled_qty
                        )
                    ]);
                }
                if(empty($input['plan_start_time'])){
                    return json(['state' => 'error', 'info' => '计划开始时间不能为空']);
                }
                if(empty($input['plan_end_time'])){
                    return json(['state' => 'error', 'info' => '计划结束时间不能为空']);
                }

                // 处理时间格式
                $start_time = $input['plan_start_time'];
                $end_time = $input['plan_end_time'];

                // 检查时间格式并处理
                $plan_start_datetime = $this->formatDateTime($input['schedule_date'], $start_time);
                $plan_end_datetime = $this->formatDateTime($input['schedule_date'], $end_time);

                // 构建排产数据（只包含数据库表中存在的字段）
                $schedule_data = [
                    'merchant' => $merchant_id,
                    'production_order_id' => $input['production_order_id'],
                    'machine_id' => $input['machine_id'],
                    'shift_id' => $input['shift_id'],
                    'schedule_date' => $input['schedule_date'],
                    'plan_qty' => $input['plan_qty'],
                    'plan_start_time' => $plan_start_datetime,
                    'plan_end_time' => $plan_end_datetime,
                    'operator_id' => isset($input['operator_id']) && !empty($input['operator_id']) ? $input['operator_id'] : null,
                    'status' => 0, // 待生产
                    'scheduler' => $user_id,
                    'data' => isset($input['data']) ? $input['data'] : '',
                    'createtime' => time()
                ];

                // 如果传入了模具ID，将其添加到备注信息中（因为表中没有mold_id字段）
                if(isset($input['mold_id']) && !empty($input['mold_id'])){
                    $mold_info = db('mold')->where('id', $input['mold_id'])->field('name')->find();
                    if($mold_info){
                        $schedule_data['data'] = $schedule_data['data'] . ' [模具: ' . $mold_info['name'] . ']';
                    }
                }

                // 添加调试信息
                file_put_contents('debug_schedule_insert.log', date('Y-m-d H:i:s') . " - Insert Data: " . json_encode($schedule_data) . "\n", FILE_APPEND);

                $schedule_id = db('production_schedule')->insertGetId($schedule_data);

                // 记录插入结果
                file_put_contents('debug_schedule_insert.log', date('Y-m-d H:i:s') . " - Insert Result: " . $schedule_id . "\n", FILE_APPEND);

                if($schedule_id){
                    push_log('新增排产计划[ ID:'.$schedule_id.' ]');

                    // 验证数据是否真的插入了
                    $verify = db('production_schedule')->where('id', $schedule_id)->find();
                    file_put_contents('debug_schedule_insert.log', date('Y-m-d H:i:s') . " - Verify Data: " . json_encode($verify) . "\n", FILE_APPEND);

                    // 同步更新生产订单状态
                    $this->syncOrderStatus($input['production_order_id']);

                    return json(['state' => 'success', 'info' => '排产成功', 'id' => $schedule_id]);
                } else {
                    // 获取数据库错误信息
                    $error = db()->getLastSql();
                    $dbError = db()->getError();
                    file_put_contents('debug_schedule_insert.log', date('Y-m-d H:i:s') . " - SQL: " . $error . "\n", FILE_APPEND);
                    file_put_contents('debug_schedule_insert.log', date('Y-m-d H:i:s') . " - DB Error: " . $dbError . "\n", FILE_APPEND);
                    return json(['state' => 'error', 'info' => '排产失败：' . ($dbError ?: '数据插入失败')]);
                }

            } else {
                //更新排产

                // 检查排产是否存在
                $schedule = db('production_schedule')
                    ->where(['id' => $input['id'], 'merchant' => Session('is_merchant_id')])
                    ->find();

                if(!$schedule){
                    return json(['state' => 'error', 'info' => '排产计划不存在']);
                }

                // 检查状态是否允许修改
                if($schedule['status'] == 2){
                    return json(['state' => 'error', 'info' => '已完成的排产不能修改']);
                }
                if($schedule['status'] == 4){
                    return json(['state' => 'error', 'info' => '已取消的排产不能修改']);
                }

                // 基础验证
                if(isset($input['plan_qty']) && $input['plan_qty'] <= 0){
                    return json(['state' => 'error', 'info' => '计划数量必须大于0']);
                }

                // 如果更新排产数量，验证不能超过订单计划数量
                if(isset($input['plan_qty'])){
                    // 获取生产订单信息
                    $order = db('production_order')
                        ->where(['id' => $schedule['production_order_id'], 'merchant' => Session('is_merchant_id')])
                        ->find();

                    if(!$order){
                        return json(['state' => 'error', 'info' => '关联的生产订单不存在']);
                    }

                    // 计算该订单已排产的总数量（排除当前正在更新的排产）
                    $scheduled_qty = db('production_schedule')
                        ->where([
                            'production_order_id' => $schedule['production_order_id'],
                            'merchant' => Session('is_merchant_id'),
                            'status' => ['in', [0, 1, 3]], // 待生产、生产中、暂停状态的排产
                            'id' => ['neq', $input['id']] // 排除当前排产
                        ])
                        ->sum('plan_qty');

                    $scheduled_qty = $scheduled_qty ?: 0;
                    $remaining_qty = $order['plan_qty'] - $scheduled_qty;

                    if($input['plan_qty'] > $remaining_qty){
                        return json([
                            'state' => 'error',
                            'info' => '排产数量不能超过剩余可排产数量。订单计划数量：' . $order['plan_qty'] .
                                     '，其他已排产数量：' . $scheduled_qty .
                                     '，剩余可排产数量：' . $remaining_qty
                        ]);
                    }
                }

                // 构建更新数据
                $update_data = [];
                $allowed_fields = ['machine_id', 'shift_id', 'schedule_date', 'plan_qty', 'plan_start_time', 'plan_end_time', 'operator_id', 'data'];

                foreach($allowed_fields as $field){
                    if(isset($input[$field])){
                        if($field == 'plan_start_time' || $field == 'plan_end_time'){
                            $date = isset($input['schedule_date']) ? $input['schedule_date'] : $schedule['schedule_date'];
                            $update_data[$field] = $this->formatDateTime($date, $input[$field]);
                        } else {
                            $update_data[$field] = $input[$field];
                        }
                    }
                }

                if(!empty($update_data)){
                    $result = db('production_schedule')
                        ->where(['id' => $input['id'], 'merchant' => Session('is_merchant_id')])
                        ->update($update_data);

                    if($result !== false){
                        push_log('更新排产计划[ ID:'.$input['id'].' ]');

                        // 同步更新生产订单状态
                        $this->syncOrderStatus($schedule['production_order_id']);

                        return json(['state' => 'success', 'info' => '更新成功']);
                    } else {
                        return json(['state' => 'error', 'info' => '更新失败']);
                    }
                } else {
                    return json(['state' => 'error', 'info' => '没有需要更新的数据']);
                }
            }

        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '操作失败: ' . $e->getMessage()]);
        }
    }

    //测试数据插入
    public function test_insert(){
        $test_data = [
            'merchant' => 1,
            'production_order_id' => 1,
            'machine_id' => 1,
            'shift_id' => 1,
            'schedule_date' => '2024-12-03',
            'plan_qty' => 5000.00,
            'plan_start_time' => '2024-12-03 11:32:00',
            'plan_end_time' => '2024-12-03 11:38:00',
            'status' => 0,
            'scheduler' => 1,
            'data' => '测试数据',
            'createtime' => time()
        ];

        try {
            $result = db('production_schedule')->insert($test_data);
            return json(['state' => 'success', 'info' => '测试插入成功', 'result' => $result]);
        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '测试插入失败: ' . $e->getMessage()]);
        }
    }

    //开始生产
    public function start_production(){
        $input = input('post.');

        try {
            if(!isset($input['id']) || empty($input['id'])){
                return json(['state' => 'error', 'info' => '参数不完整']);
            }

            $schedule = db('production_schedule')
                ->where(['id' => $input['id'], 'merchant' => Session('is_merchant_id')])
                ->find();

            if(!$schedule){
                return json(['state' => 'error', 'info' => '排产计划不存在']);
            }

            if($schedule['status'] != 0){
                return json(['state' => 'error', 'info' => '只有待生产状态的排产才能开始生产']);
            }

            $update_data = [
                'status' => 1, // 生产中
                'actual_start_time' => date('Y-m-d H:i:s')
            ];

            $result = db('production_schedule')
                ->where(['id' => $input['id'], 'merchant' => Session('is_merchant_id')])
                ->update($update_data);

            if($result !== false){
                push_log('开始生产[ 排产ID:'.$input['id'].' ]');

                // 同步更新生产订单状态
                $this->syncOrderStatus($schedule['production_order_id']);

                return json(['state' => 'success', 'info' => '开始生产成功']);
            } else {
                return json(['state' => 'error', 'info' => '开始生产失败']);
            }

        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '开始生产失败: ' . $e->getMessage()]);
        }
    }

    //暂停生产
    public function pause_production(){
        $input = input('post.');

        try {
            if(!isset($input['id']) || empty($input['id'])){
                return json(['state' => 'error', 'info' => '参数不完整']);
            }

            $schedule = db('production_schedule')
                ->where(['id' => $input['id'], 'merchant' => Session('is_merchant_id')])
                ->find();

            if(!$schedule){
                return json(['state' => 'error', 'info' => '排产计划不存在']);
            }

            if($schedule['status'] != 1){
                return json(['state' => 'error', 'info' => '只有生产中状态的排产才能暂停']);
            }

            $update_data = [
                'status' => 3 // 暂停
            ];

            $result = db('production_schedule')
                ->where(['id' => $input['id'], 'merchant' => Session('is_merchant_id')])
                ->update($update_data);

            if($result !== false){
                push_log('暂停生产[ 排产ID:'.$input['id'].' ]');
                return json(['state' => 'success', 'info' => '暂停生产成功']);
            } else {
                return json(['state' => 'error', 'info' => '暂停生产失败']);
            }

        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '暂停生产失败: ' . $e->getMessage()]);
        }
    }

    //恢复生产
    public function resume_production(){
        $input = input('post.');

        try {
            if(!isset($input['id']) || empty($input['id'])){
                return json(['state' => 'error', 'info' => '参数不完整']);
            }

            $schedule = db('production_schedule')
                ->where(['id' => $input['id'], 'merchant' => Session('is_merchant_id')])
                ->find();

            if(!$schedule){
                return json(['state' => 'error', 'info' => '排产计划不存在']);
            }

            if($schedule['status'] != 3){
                return json(['state' => 'error', 'info' => '只有暂停状态的排产才能恢复生产']);
            }

            $update_data = [
                'status' => 1 // 生产中
            ];

            $result = db('production_schedule')
                ->where(['id' => $input['id'], 'merchant' => Session('is_merchant_id')])
                ->update($update_data);

            if($result !== false){
                push_log('恢复生产[ 排产ID:'.$input['id'].' ]');
                return json(['state' => 'success', 'info' => '恢复生产成功']);
            } else {
                return json(['state' => 'error', 'info' => '恢复生产失败']);
            }

        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '恢复生产失败: ' . $e->getMessage()]);
        }
    }

    //完成生产
    public function complete_production(){
        $input = input('post.');

        try {
            if(!isset($input['id']) || empty($input['id'])){
                return json(['state' => 'error', 'info' => '参数不完整']);
            }

            $schedule = db('production_schedule')
                ->where(['id' => $input['id'], 'merchant' => Session('is_merchant_id')])
                ->find();

            if(!$schedule){
                return json(['state' => 'error', 'info' => '排产计划不存在']);
            }

            if($schedule['status'] != 1){
                return json(['state' => 'error', 'info' => '只有生产中状态的排产才能完成生产']);
            }

            $update_data = [
                'status' => 2, // 已完成
                'actual_end_time' => date('Y-m-d H:i:s'),
                'actual_qty' => $schedule['plan_qty'] // 默认实际产量等于计划产量
            ];

            $result = db('production_schedule')
                ->where(['id' => $input['id'], 'merchant' => Session('is_merchant_id')])
                ->update($update_data);

            if($result !== false){
                push_log('完成生产[ 排产ID:'.$input['id'].' ]');

                // 同步更新生产订单状态
                $this->syncOrderStatus($schedule['production_order_id']);

                return json(['state' => 'success', 'info' => '完成生产成功']);
            } else {
                return json(['state' => 'error', 'info' => '完成生产失败']);
            }

        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '完成生产失败: ' . $e->getMessage()]);
        }
    }

    //格式化日期时间
    private function formatDateTime($date, $time){
        // 如果时间已经包含日期部分（如：2024-12-02 11:32:00），直接返回
        if(strpos($time, ' ') !== false || strlen($time) > 8){
            return $time;
        }

        // 如果时间格式是 HH:MM，补充秒数
        if(strlen($time) == 5){
            $time .= ':00';
        }

        // 组合日期和时间
        return $date . ' ' . $time;
    }

    //检查时间冲突
    private function checkTimeConflict($data){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'machine_id' => $data['machine_id'],
            'shift_id' => $data['shift_id'],
            'schedule_date' => $data['schedule_date'],
            'status' => ['in', [0, 1, 3]] // 待生产、生产中、暂停
        ];

        // 如果是更新，排除自己
        if(isset($data['id']) && !empty($data['id'])){
            $where['id'] = ['neq', $data['id']];
        }

        $existing = db('production_schedule')->where($where)->find();

        if($existing){
            return '该设备在指定班次和日期已有排产安排';
        }

        return false;
    }

    //删除排产计划
    public function del(){
        $input = input('post.');

        try {
            if(!isset($input['id']) || empty($input['id'])){
                return json(['state' => 'error', 'info' => '参数不完整']);
            }

            // 检查排产是否存在
            $schedule = db('production_schedule')
                ->where(['id' => $input['id'], 'merchant' => Session('is_merchant_id')])
                ->find();

            if(!$schedule){
                return json(['state' => 'error', 'info' => '排产计划不存在']);
            }

            // 检查状态是否允许删除
            if($schedule['status'] == 1){
                return json(['state' => 'error', 'info' => '生产中的排产不能删除']);
            }
            if($schedule['status'] == 2){
                return json(['state' => 'error', 'info' => '已完成的排产不能删除']);
            }

            $result = db('production_schedule')
                ->where(['id' => $input['id'], 'merchant' => Session('is_merchant_id')])
                ->delete();

            if($result){
                push_log('删除排产计划[ ID:'.$input['id'].' ]');

                // 同步更新生产订单状态
                $this->syncOrderStatus($schedule['production_order_id']);

                return json(['state' => 'success', 'info' => '删除成功']);
            } else {
                return json(['state' => 'error', 'info' => '删除失败']);
            }

        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '删除失败: ' . $e->getMessage()]);
        }
    }

    //批量删除排产计划
    public function batch_del(){
        $input = input('post.');

        try {
            if(!isset($input['arr']) || !is_array($input['arr']) || empty($input['arr'])){
                return json(['state' => 'error', 'info' => '请选择要删除的排产计划']);
            }

            $success_count = 0;
            $error_messages = [];
            $affected_orders = []; // 收集受影响的订单ID

            foreach($input['arr'] as $id){
                // 检查排产是否存在
                $schedule = db('production_schedule')
                    ->where(['id' => $id, 'merchant' => Session('is_merchant_id')])
                    ->find();

                if(!$schedule){
                    $error_messages[] = "ID:{$id} 排产计划不存在";
                    continue;
                }

                // 检查状态是否允许删除
                if($schedule['status'] == 1){
                    $error_messages[] = "ID:{$id} 生产中的排产不能删除";
                    continue;
                }
                if($schedule['status'] == 2){
                    $error_messages[] = "ID:{$id} 已完成的排产不能删除";
                    continue;
                }

                $result = db('production_schedule')
                    ->where(['id' => $id, 'merchant' => Session('is_merchant_id')])
                    ->delete();

                if($result){
                    $success_count++;
                    $affected_orders[] = $schedule['production_order_id']; // 记录受影响的订单
                    push_log('删除排产计划[ ID:'.$id.' ]');
                } else {
                    $error_messages[] = "ID:{$id} 删除失败";
                }
            }

            // 同步更新所有受影响订单的状态
            foreach(array_unique($affected_orders) as $order_id){
                $this->syncOrderStatus($order_id);
            }

            if($success_count > 0){
                $message = "成功删除 {$success_count} 条排产计划";
                if(!empty($error_messages)){
                    $message .= "，部分失败：" . implode('；', $error_messages);
                }
                return json(['state' => 'success', 'info' => $message]);
            } else {
                return json(['state' => 'error', 'info' => '删除失败：' . implode('；', $error_messages)]);
            }

        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '批量删除失败: ' . $e->getMessage()]);
        }
    }

    //获取排产详情
    public function get_info(){
        $input = input('post.');

        try {
            if(!isset($input['id']) || empty($input['id'])){
                return json(['state' => 'error', 'info' => '参数不完整']);
            }

            $schedule = db('production_schedule')
                ->alias('ps')
                ->join('production_order po', 'ps.production_order_id = po.id', 'left')
                ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
                ->join('work_shift ws', 'ps.shift_id = ws.id', 'left')
                ->join('user u', 'ps.operator_id = u.id', 'left')
                ->join('goods g', 'po.goods_id = g.id', 'left')
                ->where(['ps.id' => $input['id'], 'ps.merchant' => Session('is_merchant_id')])
                ->field('ps.*,po.order_no,po.goods_id,im.name as machine_name,ws.name as shift_name,u.name as operator_name,g.name as goods_name')
                ->find();

            if(!$schedule){
                return json(['state' => 'error', 'info' => '排产计划不存在']);
            }

            return json(['state' => 'success', 'data' => $schedule]);

        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '获取失败: ' . $e->getMessage()]);
        }
    }

    //编辑排产页面
    public function edit(){
        $id = input('id');
        if(!$id){
            $this->error('参数错误');
        }

        // 获取排产信息
        $schedule = db('production_schedule')
            ->alias('ps')
            ->join('production_order po', 'ps.production_order_id = po.id', 'left')
            ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
            ->join('work_shift ws', 'ps.shift_id = ws.id', 'left')
            ->join('user u', 'ps.operator_id = u.id', 'left')
            ->join('goods g', 'po.goods_id = g.id', 'left')
            ->where(['ps.id' => $id, 'ps.merchant' => Session('is_merchant_id')])
            ->field('ps.*,po.order_no,po.goods_id,im.name as machine_name,ws.name as shift_name,u.name as operator_name,g.name as goods_name')
            ->find();

        if(!$schedule){
            $this->error('排产计划不存在');
        }

        $this->assign('schedule', $schedule);
        return $this->fetch();
    }

    //查看排产详情页面
    public function view(){
        $id = input('id');
        $order_id = input('order_id');

        if(!$id && !$order_id){
            $this->error('参数错误：需要提供排产计划ID或生产订单ID');
        }

        // 构建查询条件
        $where = ['ps.merchant' => Session('is_merchant_id')];
        if($id){
            $where['ps.id'] = $id;
        } else {
            $where['ps.production_order_id'] = $order_id;
        }

        // 获取排产信息
        $schedules = db('production_schedule')
            ->alias('ps')
            ->join('production_order po', 'ps.production_order_id = po.id', 'left')
            ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
            ->join('work_shift ws', 'ps.shift_id = ws.id', 'left')
            ->join('user u', 'ps.operator_id = u.id', 'left')
            ->join('goods g', 'po.goods_id = g.id', 'left')
            ->where($where)
            ->field('ps.*,po.order_no,po.goods_id,po.plan_qty,po.status as order_status,im.name as machine_name,ws.name as shift_name,u.name as operator_name,g.name as goods_name')
            ->order('ps.plan_start_time asc')
            ->select();

        if(empty($schedules)){
            if($order_id){
                $this->error('该生产订单暂无排产计划');
            } else {
                $this->error('排产计划不存在');
            }
        }

        // 如果是通过order_id查询，可能有多个排产计划
        if($order_id){
            $this->assign('schedules', $schedules);
            $this->assign('order_id', $order_id);
            $this->assign('view_type', 'order');
        } else {
            $this->assign('schedule', $schedules[0]);
            $this->assign('view_type', 'single');
        }

        return $this->fetch();
    }

    //手动排产表单页面
    public function manual_form(){
        $order_id = input('order_id');
        if(!$order_id){
            $this->error('订单ID不能为空');
        }

        // 获取订单信息
        $order = ProductionOrder::with(['goodsinfo', 'formulainfo', 'moldinfo'])
            ->find($order_id);
        if(!$order){
            $this->error('订单不存在');
        }

        $this->assign('order', $order);
        return $this->fetch();
    }

    //调整排产页面
    public function adjust(){
        $order_id = input('order_id');
        if(!$order_id){
            $this->error('订单ID不能为空');
        }

        // 获取订单信息和现有排产
        $order = ProductionOrder::with(['goodsinfo', 'schedules'])
            ->find($order_id);
        if(!$order){
            $this->error('订单不存在');
        }

        $this->assign('order', $order);
        return $this->fetch();
    }

    //调整排产
    public function adjust_schedule(){
        $input = input('post.');
        if(isset_full($input, 'schedule_id')){
            $schedule = ProductionScheduleModel::find($input['schedule_id']);
            if($schedule && $schedule->status == 0){
                //更新排产信息
                if(isset($input['plan_start_time'])){
                    $schedule->plan_start_time = $input['plan_start_time'];
                }
                if(isset($input['plan_end_time'])){
                    $schedule->plan_end_time = $input['plan_end_time'];
                }
                if(isset($input['machine_id'])){
                    $schedule->machine_id = $input['machine_id'];
                }
                if(isset($input['shift_id'])){
                    $schedule->shift_id = $input['shift_id'];
                }
                if(isset($input['operator_id'])){
                    $schedule->operator_id = $input['operator_id'];
                }
                
                $schedule->save();
                
                push_log('调整排产[ ID:'.$input['schedule_id'].' ]');
                return json(['state' => 'success']);
            }else{
                return json(['state' => 'error', 'info' => '排产状态不允许调整']);
            }
        }else{
            return json(['state' => 'error', 'info' => '传入参数不完整']);
        }
    }
    
    //取消排产
    public function cancel_schedule(){
        $input = input('post.');
        // 支持两种参数名：id 和 schedule_id
        $schedule_id = isset($input['id']) ? $input['id'] : (isset($input['schedule_id']) ? $input['schedule_id'] : null);

        if(!$schedule_id){
            return json(['state' => 'error', 'info' => '传入参数不完整']);
        }

        try {
            $schedule = db('production_schedule')
                ->where(['id' => $schedule_id, 'merchant' => Session('is_merchant_id')])
                ->find();

            if(!$schedule){
                return json(['state' => 'error', 'info' => '排产计划不存在']);
            }

            if($schedule['status'] == 2){
                return json(['state' => 'error', 'info' => '已完成的排产不能取消']);
            }
            if($schedule['status'] == 4){
                return json(['state' => 'error', 'info' => '排产已经是取消状态']);
            }

            $result = db('production_schedule')
                ->where(['id' => $schedule_id, 'merchant' => Session('is_merchant_id')])
                ->update(['status' => 4]);

            if($result !== false){
                push_log('取消排产[ ID:'.$schedule_id.' ]');
                return json(['state' => 'success', 'info' => '取消排产成功']);
            } else {
                return json(['state' => 'error', 'info' => '取消排产失败']);
            }

        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '取消排产失败: ' . $e->getMessage()]);
        }
    }
    
    //批量排产
    public function batch_schedule(){
        $input = input('post.');
        if(isset_full($input, 'orders') && is_array($input['orders'])){
            $results = [];
            
            foreach($input['orders'] as $order_id){
                $order = ProductionOrder::get($order_id);
                if($order && $order->status == 0){
                    //获取排产建议
                    $suggestions = $this->getAutoScheduleSuggestion($order);
                    if(!empty($suggestions)){
                        //使用最优建议进行排产
                        $best_suggestion = $suggestions[0];
                        $this->createScheduleFromSuggestion($order, $best_suggestion);
                        $results[] = ['order_id' => $order_id, 'status' => 'success'];
                    }else{
                        $results[] = ['order_id' => $order_id, 'status' => 'failed', 'reason' => '无可用排产方案'];
                    }
                }else{
                    $results[] = ['order_id' => $order_id, 'status' => 'failed', 'reason' => '订单状态不正确'];
                }
            }
            
            return json(['state' => 'success', 'data' => $results]);
        }else{
            return json(['state' => 'error', 'info' => '传入参数不完整']);
        }
    }
    
    //获取状态颜色
    private function getStatusColor($status){
        $colors = [
            0 => '#f0f0f0', //待生产-灰色
            1 => '#52c41a', //生产中-绿色
            2 => '#1890ff', //已完成-蓝色
            3 => '#faad14', //暂停-橙色
            4 => '#ff4d4f'  //取消-红色
        ];
        
        return $colors[$status] ?? '#f0f0f0';
    }
    
    //检测排产冲突
    private function detectScheduleConflicts($date){
        $conflicts = [];
        
        //获取当日排产
        $schedules = db('production_schedule')
            ->where([
                'merchant' => Session('is_merchant_id'),
                'schedule_date' => $date,
                'status' => ['in', [0, 1, 3]]
            ])
            ->select()
            ->toArray();
            
        //检测设备冲突
        $machine_schedules = [];
        foreach($schedules as $schedule){
            $key = $schedule['machine_id'] . '_' . $schedule['shift_id'];
            if(!isset($machine_schedules[$key])){
                $machine_schedules[$key] = [];
            }
            $machine_schedules[$key][] = $schedule;
        }
        
        foreach($machine_schedules as $key => $group){
            if(count($group) > 1){
                $conflicts[] = [
                    'type' => 'machine_conflict',
                    'description' => '设备时间冲突',
                    'schedules' => $group
                ];
            }
        }
        
        return $conflicts;
    }
    
    //更新取消排产后的产能
    private function updateCapacityAfterCancel($schedule){
        $capacity = db('capacity_analysis')
            ->where([
                'machine_id' => $schedule->machine_id,
                'analysis_date' => $schedule->schedule_date,
                'shift_id' => $schedule->shift_id
            ])
            ->find();
            
        if($capacity){
            $released_hours = (strtotime($schedule->plan_end_time) - strtotime($schedule->plan_start_time)) / 3600;
            
            db('capacity_analysis')
                ->where(['id' => $capacity['id']])
                ->update([
                    'planned_hours' => max(0, $capacity['planned_hours'] - $released_hours),
                    'remaining_hours' => min($capacity['available_hours'], $capacity['remaining_hours'] + $released_hours),
                    'utilization_rate' => max(0, (($capacity['planned_hours'] - $released_hours) / $capacity['available_hours']) * 100)
                ]);
        }
    }

    //获取订单排产信息
    public function get_order_schedule_info(){
        $input = input('post.');
        $order_id = $input['order_id'] ?? '';

        if(empty($order_id)){
            return json(['state' => 'error', 'info' => '订单ID不能为空']);
        }

        // 获取生产订单信息
        $order = db('production_order')
            ->where(['id' => $order_id, 'merchant' => Session('is_merchant_id')])
            ->find();

        if(!$order){
            return json(['state' => 'error', 'info' => '生产订单不存在']);
        }

        // 计算已排产数量
        $scheduled_qty = db('production_schedule')
            ->where([
                'production_order_id' => $order_id,
                'merchant' => Session('is_merchant_id'),
                'status' => ['in', [0, 1, 3]] // 待生产、生产中、暂停状态的排产
            ])
            ->sum('plan_qty');

        $scheduled_qty = $scheduled_qty ?: 0;
        $remaining_qty = $order['plan_qty'] - $scheduled_qty;

        return json([
            'state' => 'success',
            'data' => [
                'plan_qty' => $order['plan_qty'],
                'scheduled_qty' => $scheduled_qty,
                'remaining_qty' => $remaining_qty
            ]
        ]);
    }

    //验证排产数量
    public function validate_schedule_quantity(){
        $input = input('post.');
        $order_id = $input['order_id'] ?? '';
        $plan_qty = $input['plan_qty'] ?? 0;

        if(empty($order_id)){
            return json(['state' => 'error', 'info' => '订单ID不能为空']);
        }

        if($plan_qty <= 0){
            return json(['state' => 'error', 'info' => '计划数量必须大于0']);
        }

        // 获取生产订单信息
        $order = db('production_order')
            ->where(['id' => $order_id, 'merchant' => Session('is_merchant_id')])
            ->find();

        if(!$order){
            return json(['state' => 'error', 'info' => '生产订单不存在']);
        }

        // 计算已排产数量
        $scheduled_qty = db('production_schedule')
            ->where([
                'production_order_id' => $order_id,
                'merchant' => Session('is_merchant_id'),
                'status' => ['in', [0, 1, 3]] // 待生产、生产中、暂停状态的排产
            ])
            ->sum('plan_qty');

        $scheduled_qty = $scheduled_qty ?: 0;
        $order_plan_qty = floatval($order['plan_qty']);

        // 计算最大允许排产数量（订单数量的110%）
        $max_allowed_qty = $order_plan_qty * 1.1;
        $total_scheduled_qty = $scheduled_qty + $plan_qty;

        if($total_scheduled_qty > $max_allowed_qty){
            return json([
                'state' => 'error',
                'info' => sprintf(
                    '排产数量超限！订单数量：%.2f，最大允许排产：%.2f（110%%），已排产：%.2f，本次排产：%.2f，总计：%.2f',
                    $order_plan_qty,
                    $max_allowed_qty,
                    $scheduled_qty,
                    $plan_qty,
                    $total_scheduled_qty
                )
            ]);
        }

        $remaining_qty = $max_allowed_qty - $scheduled_qty;

        return json([
            'state' => 'success',
            'info' => sprintf(
                '验证通过！订单数量：%.2f，已排产：%.2f，本次排产：%.2f，剩余可排产：%.2f',
                $order_plan_qty,
                $scheduled_qty,
                $plan_qty,
                $remaining_qty - $plan_qty
            )
        ]);
    }

    //同步生产订单状态
    private function syncOrderStatus($order_id){
        if(empty($order_id)){
            return false;
        }

        try {
            // 获取订单信息
            $order = db('production_order')
                ->where(['id' => $order_id, 'merchant' => Session('is_merchant_id')])
                ->find();

            if(!$order){
                return false;
            }

            // 获取该订单的所有排产记录
            $schedules = db('production_schedule')
                ->where([
                    'production_order_id' => $order_id,
                    'merchant' => Session('is_merchant_id')
                ])
                ->select();

            $new_status = $this->calculateOrderStatus($order, $schedules);

            // 如果状态有变化，则更新
            if($order['status'] != $new_status){
                db('production_order')
                    ->where(['id' => $order_id, 'merchant' => Session('is_merchant_id')])
                    ->update(['status' => $new_status]);

                push_log('同步生产订单状态[ 订单号:'.$order['order_no'].' 状态:'.$new_status.' ]');
            }

            return true;

        } catch(\Exception $e) {
            // 记录错误但不影响主流程
            error_log('同步订单状态失败: ' . $e->getMessage());
            return false;
        }
    }

    //计算订单应该的状态
    private function calculateOrderStatus($order, $schedules){
        // 如果没有排产记录，状态为待排产
        if(empty($schedules)){
            return 0; // 待排产
        }

        // 统计各种状态的排产数量
        $status_counts = [
            0 => 0, // 待生产
            1 => 0, // 生产中
            2 => 0, // 已完成
            3 => 0, // 暂停
            4 => 0  // 已取消
        ];

        $total_scheduled_qty = 0;
        $total_completed_qty = 0;

        foreach($schedules as $schedule){
            $status = $schedule['status'];
            $status_counts[$status]++;

            if($status != 4){ // 排除已取消的排产
                $total_scheduled_qty += $schedule['plan_qty'];

                if($status == 2){ // 已完成的排产
                    $total_completed_qty += $schedule['plan_qty'];
                }
            }
        }

        // 判断订单状态
        // 如果所有排产都已完成，且完成数量达到订单计划数量
        if($status_counts[2] > 0 && $total_completed_qty >= $order['plan_qty']){
            return 3; // 已完成
        }

        // 如果有生产中的排产
        if($status_counts[1] > 0){
            return 2; // 生产中
        }

        // 如果有待生产或暂停的排产
        if($status_counts[0] > 0 || $status_counts[3] > 0){
            return 1; // 已排产
        }

        // 如果只有已取消的排产，回到待排产状态
        if($status_counts[4] > 0 && $total_scheduled_qty == 0){
            return 0; // 待排产
        }

        // 默认为已排产状态
        return 1;
    }

    //单个订单状态同步接口
    public function sync_order_status(){
        $input = input('post.');
        $order_id = $input['order_id'] ?? '';

        if(empty($order_id)){
            return json(['state' => 'error', 'info' => '订单ID不能为空']);
        }

        $result = $this->syncOrderStatus($order_id);

        if($result){
            return json(['state' => 'success', 'info' => '订单状态同步成功']);
        } else {
            return json(['state' => 'error', 'info' => '订单状态同步失败']);
        }
    }

    //获取待报工的排产列表
    public function get_report_list(){
        $input = input('post.');
        $schedule_date = $input['schedule_date'] ?? date('Y-m-d');
        $machine_id = $input['machine_id'] ?? '';
        $shift_id = $input['shift_id'] ?? '';

        $where = [
            'ps.merchant' => Session('is_merchant_id'),
            'ps.schedule_date' => $schedule_date
        ];

        if($machine_id){
            $where['ps.machine_id'] = $machine_id;
        }

        if($shift_id){
            $where['ps.shift_id'] = $shift_id;
        }

        try {
            $schedules = db('production_schedule')
                ->alias('ps')
                ->join('production_order po', 'ps.production_order_id = po.id', 'left')
                ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
                ->join('work_shift ws', 'ps.shift_id = ws.id', 'left')
                ->join('goods g', 'po.goods_id = g.id', 'left')
                ->where($where)
                ->field('ps.*, po.order_no, po.priority, im.name as machine_name,
                        ws.name as shift_name, g.name as goods_name,
                        po.plan_qty as order_plan_qty')
                ->order('ps.plan_start_time asc')
                ->select()
                ->toArray();

            // 处理数据，添加状态文本和进度信息
            foreach($schedules as &$schedule){
                // 状态文本
                $status_map = [
                    0 => '待生产',
                    1 => '生产中',
                    2 => '已完成',
                    3 => '暂停',
                    4 => '取消'
                ];
                $schedule['status_text'] = $status_map[$schedule['status']] ?? '未知';

                // 优先级文本
                $priority_map = [
                    1 => '紧急',
                    2 => '高',
                    3 => '普通',
                    4 => '低'
                ];
                $schedule['priority_text'] = $priority_map[$schedule['priority']] ?? '普通';

                // 计算进度
                if($schedule['plan_qty'] > 0){
                    $schedule['progress'] = round(($schedule['actual_qty'] / $schedule['plan_qty']) * 100, 2);
                } else {
                    $schedule['progress'] = 0;
                }

                // 格式化时间
                $schedule['plan_start_time_formatted'] = date('H:i', strtotime($schedule['plan_start_time']));
                $schedule['plan_end_time_formatted'] = date('H:i', strtotime($schedule['plan_end_time']));

                // 实际时间
                if($schedule['actual_start_time']){
                    $schedule['actual_start_time_formatted'] = date('H:i', strtotime($schedule['actual_start_time']));
                } else {
                    $schedule['actual_start_time_formatted'] = '';
                }

                if($schedule['actual_end_time']){
                    $schedule['actual_end_time_formatted'] = date('H:i', strtotime($schedule['actual_end_time']));
                } else {
                    $schedule['actual_end_time_formatted'] = '';
                }
            }

            return json(['state' => 'success', 'data' => $schedules]);

        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '查询排产列表失败: ' . $e->getMessage()]);
        }
    }

    //获取可排产的生产订单列表
    public function get_available_orders(){
        try {
            // 只获取待排产(0)和已排产(1)状态的订单，排除生产中(2)、已完成(3)、已取消(4)
            $orders = ProductionOrder::with('goodsinfo')
                ->where([
                    'merchant' => Session('is_merchant_id')
                ])
                ->where('status', 'in', ['待排产', '已排产', 0, 1]) // 兼容文本和数字状态
                ->field('id, order_no, goods_id, plan_qty, status')
                ->order('plan_start_date asc, priority asc')
                ->limit(1000)
                ->select();

            $result = [];
            foreach($orders as $order){
                // 状态转换
                $status_text_to_value = [
                    '待排产' => 0,
                    '已排产' => 1,
                    '生产中' => 2,
                    '已完成' => 3,
                    '已取消' => 4
                ];

                $status_value = is_string($order['status']) ?
                    ($status_text_to_value[$order['status']] ?? 0) :
                    (int)$order['status'];

                // 只允许待排产和已排产的订单
                if($status_value <= 1){
                    $result[] = [
                        'id' => $order['id'],
                        'order_no' => $order['order_no'],
                        'goods_id' => $order['goods_id'],
                        'goods_name' => $order['goodsinfo']['name'] ?? '未知商品',
                        'plan_qty' => $order['plan_qty'],
                        'status' => $order['status'],
                        'status_value' => $status_value
                    ];
                }
            }

            return json(['code' => 0, 'msg' => '获取成功', 'data' => $result]);

        } catch(\Exception $e) {
            return json(['code' => 1, 'msg' => '获取可排产订单失败: ' . $e->getMessage(), 'data' => []]);
        }
    }
}
