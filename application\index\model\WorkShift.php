<?php
namespace app\index\model;
use think\Model;

class WorkShift extends Model{
    //班次管理表
    protected $table = 'is_work_shift';
    
    //状态读取器
    protected function getStatusAttr($val, $data){
        $status = ['0' => '停用', '1' => '启用'];
        return isset($status[$val]) ? $status[$val] : '未知';
    }
    
    //状态原始值读取器
    protected function getStatusValueAttr($val, $data){
        return $data['status'];
    }
    
    //开始时间读取器
    protected function getStartTimeAttr($val, $data){
        return $val ? date('H:i', strtotime($val)) : '';
    }
    
    //结束时间读取器
    protected function getEndTimeAttr($val, $data){
        return $val ? date('H:i', strtotime($val)) : '';
    }
    
    //开始时间原始值读取器
    protected function getStartTimeValueAttr($val, $data){
        return $data['start_time'];
    }
    
    //结束时间原始值读取器
    protected function getEndTimeValueAttr($val, $data){
        return $data['end_time'];
    }
    
    //工作时间段读取器
    protected function getWorkPeriodAttr($val, $data){
        $start = $data['start_time'] ? date('H:i', strtotime($data['start_time'])) : '';
        $end = $data['end_time'] ? date('H:i', strtotime($data['end_time'])) : '';
        return $start && $end ? $start . ' - ' . $end : '';
    }
    
    //工作时长显示
    protected function getDurationTextAttr($val, $data){
        return $data['duration'] . '小时';
    }
    
    //检查时间是否跨天
    public function isCrossDay(){
        if($this->start_time && $this->end_time){
            $start = strtotime($this->start_time_value);
            $end = strtotime($this->end_time_value);
            return $end <= $start;
        }
        return false;
    }
    
    //获取班次在指定日期的实际工作时间
    public function getWorkTimeOnDate($date){
        $start_datetime = $date . ' ' . $this->start_time_value;
        $end_datetime = $date . ' ' . $this->end_time_value;
        
        // 如果跨天，结束时间加一天
        if($this->isCrossDay()){
            $end_datetime = date('Y-m-d H:i:s', strtotime($end_datetime . ' +1 day'));
        }
        
        return [
            'start' => $start_datetime,
            'end' => $end_datetime,
            'duration' => $this->duration
        ];
    }
    
    //检查指定时间是否在班次工作时间内
    public function isWorkingTime($datetime){
        $time = date('H:i:s', strtotime($datetime));
        $date = date('Y-m-d', strtotime($datetime));
        
        $work_time = $this->getWorkTimeOnDate($date);
        
        return $datetime >= $work_time['start'] && $datetime <= $work_time['end'];
    }
    
    //获取班次统计信息
    public function getStats($start_date = null, $end_date = null){
        if(!$start_date) $start_date = date('Y-m-01');
        if(!$end_date) $end_date = date('Y-m-d');
        
        // 统计该班次的生产计划数量
        $schedule_count = db('production_schedule')
            ->where([
                'shift_id' => $this->id,
                'plan_date' => ['between', [$start_date, $end_date]]
            ])
            ->count();
            
        // 统计该班次的完成订单数量
        $completed_count = db('production_schedule')
            ->where([
                'shift_id' => $this->id,
                'plan_date' => ['between', [$start_date, $end_date]],
                'status' => 'completed'
            ])
            ->count();
            
        return [
            'schedule_count' => $schedule_count,
            'completed_count' => $completed_count,
            'completion_rate' => $schedule_count > 0 ? round($completed_count / $schedule_count * 100, 2) : 0
        ];
    }
    
    //获取班次负荷情况
    public function getWorkload($date){
        $schedules = db('production_schedule')
            ->alias('ps')
            ->join('production_order po', 'ps.order_id = po.id')
            ->where([
                'ps.shift_id' => $this->id,
                'ps.plan_date' => $date
            ])
            ->field('ps.*, po.goods_name, po.plan_qty')
            ->select();
            
        $total_hours = 0;
        foreach($schedules as $schedule){
            $total_hours += $schedule['estimated_hours'] ?? 0;
        }
        
        $utilization_rate = $this->duration > 0 ? round($total_hours / $this->duration * 100, 2) : 0;
        
        return [
            'schedules' => $schedules,
            'total_hours' => $total_hours,
            'available_hours' => $this->duration,
            'utilization_rate' => $utilization_rate
        ];
    }
    
    //检查班次是否可以删除
    public function canDelete(){
        // 检查是否有生产计划使用
        $schedule_count = db('production_schedule')->where(['shift_id' => $this->id])->count();
        return $schedule_count == 0;
    }
    
    //获取班次交接信息
    public function getHandoverInfo($date){
        $prev_shift = $this->getPreviousShift();
        $next_shift = $this->getNextShift();
        
        return [
            'current' => [
                'name' => $this->name,
                'code' => $this->code,
                'period' => $this->work_period
            ],
            'previous' => $prev_shift ? [
                'name' => $prev_shift->name,
                'code' => $prev_shift->code,
                'period' => $prev_shift->work_period
            ] : null,
            'next' => $next_shift ? [
                'name' => $next_shift->name,
                'code' => $next_shift->code,
                'period' => $next_shift->work_period
            ] : null
        ];
    }
    
    //获取上一个班次
    private function getPreviousShift(){
        return self::where([
            'merchant' => $this->merchant,
            'status' => 1,
            'sort' => ['<', $this->sort]
        ])->order('sort desc')->find();
    }
    
    //获取下一个班次
    private function getNextShift(){
        return self::where([
            'merchant' => $this->merchant,
            'status' => 1,
            'sort' => ['>', $this->sort]
        ])->order('sort asc')->find();
    }
}
