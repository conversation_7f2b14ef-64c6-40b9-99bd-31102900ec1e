<?php
namespace app\index\validate;
use think\Validate;

class ProcessParameter extends Validate{
    protected $rule = [
        'name' => 'require|max:100',
        'code' => 'require|max:50',
        'goods_id' => 'require|integer',
        'mold_id' => 'integer',
        'machine_id' => 'integer',
        'barrel_temp_1' => 'number|between:0,500',
        'barrel_temp_2' => 'number|between:0,500',
        'barrel_temp_3' => 'number|between:0,500',
        'nozzle_temp' => 'number|between:0,500',
        'mold_temp_upper' => 'number|between:0,200',
        'mold_temp_lower' => 'number|between:0,200',
        'injection_pressure' => 'number|between:0,2000',
        'holding_pressure' => 'number|between:0,2000',
        'clamping_force' => 'number|between:0,5000',
        'injection_time' => 'number|between:0,60',
        'holding_time' => 'number|between:0,60',
        'cooling_time' => 'number|between:0,300',
        'cycle_time' => 'number|between:0,600',
        'shot_weight' => 'number|between:0,10000',
        'screw_speed' => 'number|between:0,500',
        'back_pressure' => 'number|between:0,100',
        'decompression' => 'number|between:0,50',
        'status' => 'in:0,1'
    ];
    
    protected $message = [
        'name.require' => '参数名称不能为空',
        'name.max' => '参数名称不能超过100个字符',
        'code.require' => '参数编码不能为空',
        'code.max' => '参数编码不能超过50个字符',
        'goods_id.require' => '请选择关联商品',
        'goods_id.integer' => '商品ID必须为整数',
        'mold_id.integer' => '模具ID必须为整数',
        'machine_id.integer' => '设备ID必须为整数',
        'barrel_temp_1.number' => '料筒温度1必须为数字',
        'barrel_temp_1.between' => '料筒温度1必须在0-500之间',
        'barrel_temp_2.number' => '料筒温度2必须为数字',
        'barrel_temp_2.between' => '料筒温度2必须在0-500之间',
        'barrel_temp_3.number' => '料筒温度3必须为数字',
        'barrel_temp_3.between' => '料筒温度3必须在0-500之间',
        'nozzle_temp.number' => '喷嘴温度必须为数字',
        'nozzle_temp.between' => '喷嘴温度必须在0-500之间',
        'mold_temp_upper.number' => '模具上温度必须为数字',
        'mold_temp_upper.between' => '模具上温度必须在0-200之间',
        'mold_temp_lower.number' => '模具下温度必须为数字',
        'mold_temp_lower.between' => '模具下温度必须在0-200之间',
        'injection_pressure.number' => '注射压力必须为数字',
        'injection_pressure.between' => '注射压力必须在0-2000之间',
        'holding_pressure.number' => '保压压力必须为数字',
        'holding_pressure.between' => '保压压力必须在0-2000之间',
        'clamping_force.number' => '锁模力必须为数字',
        'clamping_force.between' => '锁模力必须在0-5000之间',
        'injection_time.number' => '注射时间必须为数字',
        'injection_time.between' => '注射时间必须在0-60之间',
        'holding_time.number' => '保压时间必须为数字',
        'holding_time.between' => '保压时间必须在0-60之间',
        'cooling_time.number' => '冷却时间必须为数字',
        'cooling_time.between' => '冷却时间必须在0-300之间',
        'cycle_time.number' => '周期时间必须为数字',
        'cycle_time.between' => '周期时间必须在0-600之间',
        'shot_weight.number' => '射胶量必须为数字',
        'shot_weight.between' => '射胶量必须在0-10000之间',
        'screw_speed.number' => '螺杆转速必须为数字',
        'screw_speed.between' => '螺杆转速必须在0-500之间',
        'back_pressure.number' => '背压必须为数字',
        'back_pressure.between' => '背压必须在0-100之间',
        'decompression.number' => '松退量必须为数字',
        'decompression.between' => '松退量必须在0-50之间',
        'status.in' => '状态值不正确'
    ];
    
    protected $scene = [
        'add' => ['name', 'code', 'goods_id', 'mold_id', 'machine_id', 'status'],
        'edit' => ['name', 'code', 'goods_id', 'mold_id', 'machine_id', 'status']
    ];
}
