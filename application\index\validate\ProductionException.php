<?php
namespace app\index\validate;
use think\Validate;

class ProductionException extends Validate{
    protected $rule = [
        'machine_id' => 'require|integer',
        'exception_date' => 'require|date',
        'exception_type' => 'require|in:equipment_failure,quality_issue,material_shortage,process_abnormal,safety_incident,other',
        'exception_desc' => 'require|max:500',
        'severity' => 'require|in:1,2,3,4',
        'reporter_id' => 'integer',
        'handler_id' => 'integer',
        'handle_method' => 'max:500',
        'handle_result' => 'max:500',
        'status' => 'in:0,1,2,3'
    ];
    
    protected $message = [
        'machine_id.require' => '请选择相关设备',
        'machine_id.integer' => '设备ID必须为整数',
        'exception_date.require' => '请选择异常日期',
        'exception_date.date' => '异常日期格式不正确',
        'exception_type.require' => '请选择异常类型',
        'exception_type.in' => '异常类型值不正确',
        'exception_desc.require' => '请输入异常描述',
        'exception_desc.max' => '异常描述不能超过500个字符',
        'severity.require' => '请选择严重程度',
        'severity.in' => '严重程度值不正确',
        'reporter_id.integer' => '报告人ID必须为整数',
        'handler_id.integer' => '处理人ID必须为整数',
        'handle_method.max' => '处理方法不能超过500个字符',
        'handle_result.max' => '处理结果不能超过500个字符',
        'status.in' => '状态值不正确'
    ];
    
    protected $scene = [
        'add' => ['machine_id', 'exception_date', 'exception_type', 'exception_desc', 'severity'],
        'edit' => ['machine_id', 'exception_date', 'exception_type', 'exception_desc', 'severity', 'handle_method', 'handle_result', 'status']
    ];
}
