<?php
namespace app\index\model;
use think\Model;

class QualityDefect extends Model{
    //质量缺陷表
    protected $table = 'is_quality_defect';
    
    protected $type = [
        'more' => 'json'
    ];
    
    //关联商品信息
    public function goodsinfo(){
        return $this->hasOne('app\index\model\Goods', 'id', 'goods_id');
    }
    
    //关联设备信息
    public function machineinfo(){
        return $this->hasOne('app\index\model\InjectionMachine', 'id', 'machine_id');
    }
    
    //关联检验员信息
    public function inspectorinfo(){
        return $this->hasOne('app\index\model\User', 'id', 'inspector_id');
    }
    
    //状态读取器
    protected function getStatusAttr($val, $data){
        $status = [
            '0' => '待处理',
            '1' => '已处理',
            '2' => '已关闭'
        ];
        return isset($status[$val]) ? $status[$val] : '未知';
    }
    
    //状态原始值读取器
    protected function getStatusValueAttr($val, $data){
        return $data['status'];
    }
    
    //创建时间读取器
    protected function getCreatetimeAttr($val, $data){
        return $val ? date('Y-m-d H:i:s', $val) : '';
    }
    
    //缺陷日期读取器
    protected function getDefectDateAttr($val, $data){
        return $val ? $val : '';
    }
    
    //缺陷时间读取器
    protected function getDefectTimeAttr($val, $data){
        return $val ? $val : '';
    }
    
    //获取缺陷详细信息
    public function getDefectDetails(){
        return [
            'basic_info' => [
                'defect_date' => $this->defect_date,
                'defect_time' => $this->defect_time,
                'defect_type' => $this->defect_type,
                'goods_name' => $this->goodsinfo ? $this->goodsinfo->name : '',
                'machine_name' => $this->machineinfo ? $this->machineinfo->name : '',
                'inspector_name' => $this->inspectorinfo ? $this->inspectorinfo->name : ''
            ],
            'defect_data' => [
                'defect_desc' => $this->defect_desc,
                'defect_qty' => $this->defect_qty,
                'defect_rate' => $this->defect_rate,
                'cause_analysis' => $this->cause_analysis,
                'corrective_action' => $this->corrective_action
            ],
            'status_info' => [
                'status' => $this->status
            ]
        ];
    }
    
    //根据日期获取缺陷记录
    public static function getByDate($date, $goods_id = null, $machine_id = null){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'defect_date' => $date
        ];
        
        if($goods_id){
            $where['goods_id'] = $goods_id;
        }
        
        if($machine_id){
            $where['machine_id'] = $machine_id;
        }
        
        return self::where($where)
            ->with(['goodsinfo', 'machineinfo', 'inspectorinfo'])
            ->order('defect_time desc')
            ->select();
    }
    
    //根据商品获取缺陷记录
    public static function getByGoods($goods_id, $start_date = null, $end_date = null){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'goods_id' => $goods_id
        ];
        
        if($start_date){
            $where[] = ['defect_date', '>=', $start_date];
        }
        
        if($end_date){
            $where[] = ['defect_date', '<=', $end_date];
        }
        
        return self::where($where)
            ->with(['machineinfo', 'inspectorinfo'])
            ->order('defect_date desc, defect_time desc')
            ->select();
    }
    
    //根据设备获取缺陷记录
    public static function getByMachine($machine_id, $start_date = null, $end_date = null){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'machine_id' => $machine_id
        ];
        
        if($start_date){
            $where[] = ['defect_date', '>=', $start_date];
        }
        
        if($end_date){
            $where[] = ['defect_date', '<=', $end_date];
        }
        
        return self::where($where)
            ->with(['goodsinfo', 'inspectorinfo'])
            ->order('defect_date desc, defect_time desc')
            ->select();
    }
    
    //获取缺陷统计
    public static function getDefectStatistics($start_date, $end_date, $group_by = 'type'){
        $where = [
            'merchant' => Session('is_merchant_id'),
            ['defect_date', 'between', [$start_date, $end_date]]
        ];
        
        $field = 'defect_type,
                  COUNT(*) as defect_count,
                  SUM(defect_qty) as total_defect_qty,
                  AVG(defect_rate) as avg_defect_rate';
        
        $group = 'defect_type';
        $order = 'total_defect_qty desc';
        
        switch($group_by){
            case 'goods':
                $field = 'goods_id,' . str_replace('defect_type,', '', $field);
                $group = 'goods_id';
                break;
            case 'machine':
                $field = 'machine_id,' . str_replace('defect_type,', '', $field);
                $group = 'machine_id';
                break;
            case 'date':
                $field = 'defect_date,' . str_replace('defect_type,', '', $field);
                $group = 'defect_date';
                $order = 'defect_date asc';
                break;
        }
        
        return self::where($where)
            ->field($field)
            ->group($group)
            ->order($order)
            ->select();
    }
    
    //获取缺陷趋势分析
    public static function getDefectTrend($start_date, $end_date, $period = 'day'){
        $where = [
            'merchant' => Session('is_merchant_id'),
            ['defect_date', 'between', [$start_date, $end_date]]
        ];
        
        $field = 'defect_date,
                  COUNT(*) as daily_defect_count,
                  SUM(defect_qty) as daily_defect_qty,
                  AVG(defect_rate) as daily_avg_rate';
        
        $group = 'defect_date';
        
        if($period == 'week'){
            $field = 'YEARWEEK(defect_date) as week_period,
                      COUNT(*) as weekly_defect_count,
                      SUM(defect_qty) as weekly_defect_qty,
                      AVG(defect_rate) as weekly_avg_rate';
            $group = 'YEARWEEK(defect_date)';
        } elseif($period == 'month'){
            $field = 'DATE_FORMAT(defect_date, "%Y-%m") as month_period,
                      COUNT(*) as monthly_defect_count,
                      SUM(defect_qty) as monthly_defect_qty,
                      AVG(defect_rate) as monthly_avg_rate';
            $group = 'DATE_FORMAT(defect_date, "%Y-%m")';
        }
        
        return self::where($where)
            ->field($field)
            ->group($group)
            ->order('defect_date asc')
            ->select();
    }
    
    //获取TOP缺陷类型
    public static function getTopDefectTypes($start_date, $end_date, $limit = 10){
        $where = [
            'merchant' => Session('is_merchant_id'),
            ['defect_date', 'between', [$start_date, $end_date]]
        ];
        
        return self::where($where)
            ->field('defect_type,
                     COUNT(*) as occurrence_count,
                     SUM(defect_qty) as total_qty,
                     AVG(defect_rate) as avg_rate')
            ->group('defect_type')
            ->order('total_qty desc')
            ->limit($limit)
            ->select();
    }
    
    //获取缺陷改进建议
    public static function getImprovementSuggestions($start_date, $end_date){
        $suggestions = [];
        
        // 分析高频缺陷类型
        $top_defects = self::getTopDefectTypes($start_date, $end_date, 5);
        if(!empty($top_defects)){
            $suggestions[] = [
                'type' => 'high_frequency_defects',
                'title' => '高频缺陷改进',
                'description' => '发现' . count($top_defects) . '种高频缺陷类型需要重点关注',
                'details' => $top_defects,
                'priority' => 'high'
            ];
        }
        
        // 分析缺陷率高的设备
        $machine_defects = self::getDefectStatistics($start_date, $end_date, 'machine');
        $high_defect_machines = [];
        foreach($machine_defects as $machine){
            if($machine->avg_defect_rate > 5){ // 缺陷率超过5%
                $high_defect_machines[] = $machine;
            }
        }
        
        if(!empty($high_defect_machines)){
            $suggestions[] = [
                'type' => 'high_defect_machines',
                'title' => '设备缺陷率改进',
                'description' => '发现' . count($high_defect_machines) . '台设备缺陷率较高',
                'details' => $high_defect_machines,
                'priority' => 'medium'
            ];
        }
        
        // 分析缺陷趋势
        $trend = self::getDefectTrend($start_date, $end_date);
        $increasing_trend = 0;
        for($i = 1; $i < count($trend); $i++){
            if($trend[$i]->daily_defect_qty > $trend[$i-1]->daily_defect_qty){
                $increasing_trend++;
            }
        }
        
        if($increasing_trend > count($trend) * 0.6){
            $suggestions[] = [
                'type' => 'increasing_trend',
                'title' => '缺陷趋势预警',
                'description' => '缺陷数量呈上升趋势，需要加强质量控制',
                'details' => ['trend_ratio' => round($increasing_trend / count($trend) * 100, 2)],
                'priority' => 'high'
            ];
        }
        
        return $suggestions;
    }
}
