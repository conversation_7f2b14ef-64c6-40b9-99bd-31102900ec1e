<?php
namespace app\index\validate;
use think\Validate;

class ProductionReport extends Validate{
    protected $rule = [
        'schedule_id' => 'integer',
        'machine_id' => 'require|integer',
        'shift_id' => 'require|integer',
        'goods_id' => 'require|integer',
        'operator_id' => 'require|integer',
        'report_date' => 'require|date',
        'plan_qty' => 'integer|egt:0',
        'actual_qty' => 'require|integer|egt:0',
        'good_qty' => 'require|integer|egt:0',
        'defect_qty' => 'integer|egt:0',
        'working_hours' => 'number|between:0,24',
        'downtime_hours' => 'number|between:0,24'
    ];
    
    protected $message = [
        'schedule_id.integer' => '排产计划ID必须为整数',
        'machine_id.require' => '请选择生产设备',
        'machine_id.integer' => '设备ID必须为整数',
        'shift_id.require' => '请选择生产班次',
        'shift_id.integer' => '班次ID必须为整数',
        'goods_id.require' => '请选择生产商品',
        'goods_id.integer' => '商品ID必须为整数',
        'operator_id.require' => '请选择操作员',
        'operator_id.integer' => '操作员ID必须为整数',
        'report_date.require' => '请选择报工日期',
        'report_date.date' => '报工日期格式不正确',
        'plan_qty.integer' => '计划数量必须为整数',
        'plan_qty.egt' => '计划数量不能为负数',
        'actual_qty.require' => '请输入实际产量',
        'actual_qty.integer' => '实际产量必须为整数',
        'actual_qty.egt' => '实际产量不能为负数',
        'good_qty.require' => '请输入合格数量',
        'good_qty.integer' => '合格数量必须为整数',
        'good_qty.egt' => '合格数量不能为负数',
        'defect_qty.integer' => '不良数量必须为整数',
        'defect_qty.egt' => '不良数量不能为负数',
        'working_hours.number' => '工作时长必须为数字',
        'working_hours.between' => '工作时长必须在0-24小时之间',
        'downtime_hours.number' => '停机时长必须为数字',
        'downtime_hours.between' => '停机时长必须在0-24小时之间'
    ];
    
    protected $scene = [
        'add' => ['machine_id', 'shift_id', 'goods_id', 'operator_id', 'report_date', 'actual_qty', 'good_qty', 'defect_qty', 'working_hours', 'downtime_hours'],
        'edit' => ['machine_id', 'shift_id', 'goods_id', 'operator_id', 'report_date', 'actual_qty', 'good_qty', 'defect_qty', 'working_hours', 'downtime_hours']
    ];
}
