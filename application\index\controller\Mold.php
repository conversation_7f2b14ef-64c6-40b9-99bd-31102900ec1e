<?php
namespace app\index\controller;
use app\index\controller\Acl;
use app\index\model\Mold as MoldModel;

class Mold extends Acl {
    //模具管理模块
    
    //主视图
    public function main(){
        return $this->fetch();
    }

    //表单页面
    public function form(){
        $id = input('id', 0);
        $mold = [];
        if($id > 0){
            $mold = MoldModel::get($id);
            if(!$mold){
                $this->error('模具不存在!');
            }
            $mold = $mold->toArray();
        }
        $this->assign('mold', $mold);
        return $this->fetch();
    }

    //详情页面
    public function info(){
        $id = input('id', 0);
        if($id <= 0){
            $this->error('参数错误!');
        }

        $mold = MoldModel::get($id, ['goodsinfo']);
        if(!$mold){
            $this->error('模具不存在!');
        }

        $this->assign('mold', $mold);
        return $this->fetch();
    }
    
    //获取模具列表
    public function get_list(){
        $input = input('post.');
        $where = auth('mold', []);
        
        //搜索条件
        if(isset_full($input, 'name')){
            $where[] = ['name', 'like', '%'.$input['name'].'%'];
        }
        if(isset_full($input, 'code')){
            $where[] = ['code', 'like', '%'.$input['code'].'%'];
        }
        if(isset_full($input, 'status')){
            $where[] = ['status', '=', $input['status']];
        }
        if(isset_full($input, 'goods_id')){
            $where[] = ['goods_id', '=', $input['goods_id']];
        }
        
        $list = MoldModel::with(['goodsinfo'])
            ->where($where)
            ->order('id desc')
            ->paginate(input('limit', 15))
            ->toArray();
            
        return json(['code' => 0, 'msg' => '', 'count' => $list['total'], 'data' => $list['data']]);
    }
    
    //新增|更新模具
    public function set(){
        $input = input('post.');
        
        if(isset($input['id'])){
            if(empty($input['id'])){
                //新增
                $input['merchant'] = Session('is_merchant_id');
                $input['createtime'] = time();
                $vali = $this->validate($input, 'Mold');
                if($vali === true){
                    $create_info = MoldModel::create($input);
                    push_log('新增模具[ '.$create_info['name'].' ]');
                    $result = ['state' => 'success'];
                }else{
                    $result = ['state' => 'error', 'info' => $vali];
                }
            }else{
                //更新
                $vali = $this->validate($input, 'Mold');
                if($vali === true){
                    $update_info = MoldModel::update($input);
                    push_log('更新模具[ '.$update_info['name'].' ]');
                    $result = ['state' => 'success'];
                }else{
                    $result = ['state' => 'error', 'info' => $vali];
                }
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //删除模具
    public function del(){
        $input = input('post.');
        if(isset_full($input, 'id')){
            $mold = MoldModel::find($input['id']);
            if($mold){
                //检查是否有关联的生产订单
                $order_count = db('production_order')->where(['mold_id' => $input['id']])->count();
                if($order_count > 0){
                    return json(['state' => 'error', 'info' => '该模具已有生产订单记录，无法删除!']);
                }
                
                $mold->delete();
                push_log('删除模具[ '.$mold['name'].' ]');
                $result = ['state' => 'success'];
            }else{
                $result = ['state' => 'error', 'info' => '模具不存在!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //获取模具详情
    public function get_info(){
        $input = input('post.');
        if(isset_full($input, 'id')){
            $mold = MoldModel::get($input['id'], ['goodsinfo']);
            if($mold){
                $result = ['state' => 'success', 'data' => $mold];
            }else{
                $result = ['state' => 'error', 'info' => '模具不存在!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //更新模具状态
    public function update_status(){
        $input = input('post.');
        if(isset_full($input, 'id') && isset_full($input, 'status')){
            $mold = MoldModel::get($input['id']);
            if($mold){
                $mold->status = $input['status'];
                $mold->save();
                
                $status_text = ['0' => '停用', '1' => '正常', '2' => '维修', '3' => '报废'];
                push_log('更新模具[ '.$mold['name'].' ]状态为: '.$status_text[$input['status']]);
                $result = ['state' => 'success'];
            }else{
                $result = ['state' => 'error', 'info' => '模具不存在!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //获取可用模具列表(用于下拉选择)
    public function get_available(){
        $input = input('post.');

        try {
            // 构建基础查询条件
            $where = [];
            $where['merchant'] = Session('is_merchant_id');
            $where['status'] = 1; //只获取正常状态的模具

            if(isset_full($input, 'goods_id')){
                $where['goods_id'] = $input['goods_id'];
            }

            $list = db('mold')
                ->where($where)
                ->field('id,name,code,goods_id,cavity_count,cycle_time,weight_per_shot')
                ->order('name asc')
                ->select();

            // 如果需要商品名称，再次查询
            if($list){
                foreach($list as &$item){
                    $goods = db('goods')->where('id', $item['goods_id'])->field('name')->find();
                    $item['goods_name'] = $goods ? $goods['name'] : '';
                }
            }

            return json(['state' => 'success', 'data' => $list]);
        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '查询失败: ' . $e->getMessage()]);
        }
    }
    

    
    //模具使用统计
    public function usage_stats(){
        $input = input('post.');
        $start_date = isset($input['start_date']) ? $input['start_date'] : date('Y-m-01');
        $end_date = isset($input['end_date']) ? $input['end_date'] : date('Y-m-d');
        
        $where = auth('production_schedule', []);
        
        $stats = db('production_schedule')
            ->alias('ps')
            ->join('production_order po', 'ps.production_order_id = po.id', 'left')
            ->join('mold m', 'po.mold_id = m.id', 'left')
            ->join('goods g', 'po.goods_id = g.id', 'left')
            ->where($where)
            ->where('ps.schedule_date', 'between', [$start_date, $end_date]);

        if(isset_full($input, 'mold_id')){
            $stats = $stats->where('po.mold_id', '=', $input['mold_id']);
        }

        $stats = $stats->field('m.id as mold_id,m.name as mold_name,m.code as mold_code,
                     g.name as goods_name,
                     COUNT(ps.id) as usage_count,
                     SUM(ps.actual_qty) as total_qty,
                     AVG(ps.actual_qty/ps.plan_qty*100) as avg_efficiency')
            ->group('m.id')
            ->order('usage_count desc')
            ->paginate(input('limit', 15))
            ->toArray();
            
        return json(['code' => 0, 'msg' => '', 'count' => $stats['total'], 'data' => $stats['data']]);
    }
    
    //模具寿命预警
    public function life_alert(){
        $where = auth('mold', []);
        $where[] = ['status', '=', 1]; //正常状态
        
        $molds = MoldModel::where($where)->select();
        $alerts = [];
        
        foreach($molds as $mold){
            $alert = $mold->getLifeAlert();
            if($alert){
                $alerts[] = [
                    'mold_id' => $mold->id,
                    'mold_name' => $mold->name,
                    'mold_code' => $mold->code,
                    'total_shots' => $mold->total_shots,
                    'alert' => $alert
                ];
            }
        }
        
        return json(['state' => 'success', 'data' => $alerts]);
    }

    //获取模具维护记录列表
    public function get_maintenance_list(){
        $input = input('post.');
        $mold_id = isset($input['mold_id']) ? $input['mold_id'] : 0;

        if($mold_id <= 0){
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 使用模具表的扩展信息字段存储维护记录
        $mold = MoldModel::get($mold_id);
        if(!$mold){
            return json(['code' => 1, 'msg' => '模具不存在']);
        }

        $maintenance_records = [];
        if($mold->more){
            $more_data = json_decode($mold->more, true);
            if(isset($more_data['maintenance_records'])){
                $maintenance_records = $more_data['maintenance_records'];
                // 按日期倒序排列
                usort($maintenance_records, function($a, $b){
                    return strtotime($b['maintenance_date']) - strtotime($a['maintenance_date']);
                });
            }
        }

        // 分页处理
        $page = input('page', 1);
        $limit = input('limit', 15);
        $total = count($maintenance_records);
        $start = ($page - 1) * $limit;
        $data = array_slice($maintenance_records, $start, $limit);

        return json(['code' => 0, 'msg' => '', 'count' => $total, 'data' => $data]);
    }

    //维护记录页面
    public function maintenance(){
        $id = input('id', 0);
        if($id <= 0){
            $this->error('参数错误!');
        }

        $mold = MoldModel::get($id);
        if(!$mold){
            $this->error('模具不存在!');
        }

        $this->assign('mold', $mold);
        return $this->fetch();
    }

    //保存维护记录
    public function save_maintenance(){
        $input = input('post.');

        // 检查必需参数
        if(empty($input['mold_id']) || empty($input['maintenance_type']) ||
           empty($input['maintenance_date']) || empty($input['maintenance_content'])){
            return json(['code' => 1, 'msg' => '参数不完整']);
        }

        $mold = MoldModel::get($input['mold_id']);
        if(!$mold){
            return json(['code' => 1, 'msg' => '模具不存在']);
        }

        // 获取现有维护记录
        $more_data = $mold->more ? json_decode($mold->more, true) : [];
        if(!isset($more_data['maintenance_records'])){
            $more_data['maintenance_records'] = [];
        }

        // 创建新的维护记录
        $maintenance_record = [
            'id' => isset($input['id']) ? $input['id'] : uniqid(),
            'maintenance_type' => $input['maintenance_type'],
            'maintenance_date' => $input['maintenance_date'],
            'maintenance_content' => $input['maintenance_content'],
            'fault_description' => isset($input['fault_description']) ? $input['fault_description'] : '',
            'cost' => isset($input['cost']) ? floatval($input['cost']) : 0,
            'maintainer_name' => session('admin_user.name'),
            'maintainer_id' => session('admin_user.id'),
            'status' => isset($input['status']) ? intval($input['status']) : 2, // 默认已完成
            'data' => isset($input['data']) ? $input['data'] : '',
            'createtime' => date('Y-m-d H:i:s')
        ];

        // 如果是编辑，替换现有记录
        if(isset($input['id']) && $input['id']){
            $found = false;
            foreach($more_data['maintenance_records'] as $key => $record){
                if($record['id'] == $input['id']){
                    $more_data['maintenance_records'][$key] = $maintenance_record;
                    $found = true;
                    break;
                }
            }
            if(!$found){
                return json(['code' => 1, 'msg' => '维护记录不存在']);
            }
        } else {
            // 新增记录
            $more_data['maintenance_records'][] = $maintenance_record;
        }

        // 更新模具信息
        $mold->more = json_encode($more_data);
        $mold->maintenance_count = count($more_data['maintenance_records']);
        $mold->last_maintenance = $input['maintenance_date'];
        $mold->save();

        push_log('保存模具维护记录[ '.$mold['name'].' ]');
        return json(['code' => 0, 'msg' => '保存成功']);
    }

    //删除维护记录
    public function del_maintenance(){
        $input = input('post.');

        if(empty($input['mold_id']) || empty($input['maintenance_id'])){
            return json(['code' => 1, 'msg' => '参数不完整']);
        }

        $mold = MoldModel::get($input['mold_id']);
        if(!$mold){
            return json(['code' => 1, 'msg' => '模具不存在']);
        }

        $more_data = $mold->more ? json_decode($mold->more, true) : [];
        if(!isset($more_data['maintenance_records'])){
            return json(['code' => 1, 'msg' => '维护记录不存在']);
        }

        // 删除指定记录
        $found = false;
        foreach($more_data['maintenance_records'] as $key => $record){
            if($record['id'] == $input['maintenance_id']){
                unset($more_data['maintenance_records'][$key]);
                $more_data['maintenance_records'] = array_values($more_data['maintenance_records']); // 重新索引
                $found = true;
                break;
            }
        }

        if(!$found){
            return json(['code' => 1, 'msg' => '维护记录不存在']);
        }

        // 更新模具信息
        $mold->more = json_encode($more_data);
        $mold->maintenance_count = count($more_data['maintenance_records']);

        // 更新最后维护日期
        if(count($more_data['maintenance_records']) > 0){
            $latest_date = '';
            foreach($more_data['maintenance_records'] as $record){
                if($record['maintenance_date'] > $latest_date){
                    $latest_date = $record['maintenance_date'];
                }
            }
            $mold->last_maintenance = $latest_date;
        } else {
            $mold->last_maintenance = null;
        }

        $mold->save();

        push_log('删除模具维护记录[ '.$mold['name'].' ]');
        return json(['code' => 0, 'msg' => '删除成功']);
    }
}
