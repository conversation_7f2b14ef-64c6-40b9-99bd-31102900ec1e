<?php
namespace app\index\model;
use think\Model;

class Deliveryinfo extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'is_delivery_info';
    
    // 设置主键
    protected $pk = 'id';
    
    // 设置字段信息
    protected $field = [
        'id', 'pid', 'sale_info_id', 'room', 'goods', 'warehouse', 
        'serial', 'nums', 'price', 'total', 'data', 'more'
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = false;
    
    // JSON字段
    protected $json = ['more'];
    
    // 类型转换
    protected $type = [
        'nums' => 'float',
        'price' => 'float',
        'total' => 'float',
        'pid' => 'integer',
        'sale_info_id' => 'integer',
        'room' => 'integer',
        'goods' => 'integer',
        'warehouse' => 'integer'
    ];
    
    // 关联发货单主表
    public function deliveryclass()
    {
        return $this->belongsTo('Deliveryclass', 'pid', 'id');
    }
    
    // 关联销售订单详情
    public function saleinfo()
    {
        return $this->belongsTo('Saleinfo', 'sale_info_id', 'id');
    }
    
    // 关联商品信息
    public function goodsinfo()
    {
        return $this->belongsTo('Goods', 'goods', 'id');
    }
    
    // 关联仓库信息
    public function warehouseinfo()
    {
        return $this->belongsTo('Warehouse', 'warehouse', 'id');
    }
    
    // 关联仓储信息
    public function roominfo()
    {
        return $this->belongsTo('Room', 'room', 'id');
    }
}
