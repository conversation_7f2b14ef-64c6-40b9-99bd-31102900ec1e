<?php
namespace app\index\validate;
use think\Validate;
class Code extends Validate{
    //默认创建规则
    protected $rule = [
        ['name', 'require|RepeatName:create', '条码名称不可为空!|字段数据重复'],
        ['code', 'require', '条码内容不可为空!'],
        ['more', 'array', '扩展信息格式不正确!']
    ];
    //场景规则
    protected $scene = [
        'update'  =>  [
            'name'=>'require|RepeatName:update',
            'code'=>'require',
            'more'
        ]
    ];
    //条码名称重复性判断
    protected function RepeatName($val,$rule,$data){
        $sql['name']=$val;
        $rule=='update'&&($sql['id']=['neq',$data['id']]);
        $nod=db('code')->where($sql)->find();
        return empty($nod)?true:'条码名称[ '.$val.' ]已存在!';
    }
}