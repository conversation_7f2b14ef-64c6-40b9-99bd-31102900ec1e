<?php
namespace app\index\model;
use think\Model;

class ProductionException extends Model{
    //生产异常表
    protected $table = 'is_production_exception';
    
    protected $type = [
        'more' => 'json'
    ];
    
    //关联设备信息
    public function machineinfo(){
        return $this->hasOne('app\index\model\InjectionMachine', 'id', 'machine_id');
    }
    
    //关联报告人信息
    public function reporterinfo(){
        return $this->hasOne('app\index\model\User', 'id', 'reporter_id');
    }
    
    //关联处理人信息
    public function handlerinfo(){
        return $this->hasOne('app\index\model\User', 'id', 'handler_id');
    }
    
    //异常类型读取器
    protected function getExceptionTypeAttr($val, $data){
        $types = [
            'equipment_failure' => '设备故障',
            'quality_issue' => '质量问题',
            'material_shortage' => '物料短缺',
            'process_abnormal' => '工艺异常',
            'safety_incident' => '安全事故',
            'other' => '其他'
        ];
        return isset($types[$val]) ? $types[$val] : '未知';
    }
    
    //异常类型原始值读取器
    protected function getExceptionTypeValueAttr($val, $data){
        return $data['exception_type'];
    }
    
    //严重程度读取器
    protected function getSeverityAttr($val, $data){
        $severities = [
            '1' => '轻微',
            '2' => '一般',
            '3' => '严重',
            '4' => '紧急'
        ];
        return isset($severities[$val]) ? $severities[$val] : '未知';
    }
    
    //严重程度原始值读取器
    protected function getSeverityValueAttr($val, $data){
        return $data['severity'];
    }
    
    //状态读取器
    protected function getStatusAttr($val, $data){
        $status = [
            '0' => '待处理',
            '1' => '处理中',
            '2' => '已完成',
            '3' => '已关闭'
        ];
        return isset($status[$val]) ? $status[$val] : '未知';
    }
    
    //状态原始值读取器
    protected function getStatusValueAttr($val, $data){
        return $data['status'];
    }
    
    //创建时间读取器
    protected function getCreatetimeAttr($val, $data){
        return $val ? date('Y-m-d H:i:s', $val) : '';
    }
    
    //异常日期读取器
    protected function getExceptionDateAttr($val, $data){
        return $val ? $val : '';
    }
    
    //异常时间读取器
    protected function getExceptionTimeAttr($val, $data){
        return $val ? $val : '';
    }
    
    //处理开始时间读取器
    protected function getHandleStartTimeAttr($val, $data){
        return $val ? $val : '';
    }
    
    //处理结束时间读取器
    protected function getHandleEndTimeAttr($val, $data){
        return $val ? $val : '';
    }
    
    //计算处理时长
    public function getHandleDuration(){
        if(!$this->handle_start_time || !$this->handle_end_time){
            return 0;
        }
        
        $start = strtotime($this->exception_date . ' ' . $this->handle_start_time);
        $end = strtotime($this->exception_date . ' ' . $this->handle_end_time);
        
        return round(($end - $start) / 60, 2); // 返回分钟数
    }
    
    //获取异常详细信息
    public function getExceptionDetails(){
        return [
            'basic_info' => [
                'exception_date' => $this->exception_date,
                'exception_time' => $this->exception_time,
                'exception_type' => $this->exception_type,
                'severity' => $this->severity,
                'machine_name' => $this->machineinfo ? $this->machineinfo->name : '',
                'reporter_name' => $this->reporterinfo ? $this->reporterinfo->name : ''
            ],
            'description' => [
                'exception_desc' => $this->exception_desc,
                'impact_desc' => $this->impact_desc,
                'cause_analysis' => $this->cause_analysis
            ],
            'handling' => [
                'status' => $this->status,
                'handler_name' => $this->handlerinfo ? $this->handlerinfo->name : '',
                'handle_start_time' => $this->handle_start_time,
                'handle_end_time' => $this->handle_end_time,
                'handle_duration' => $this->getHandleDuration(),
                'handle_method' => $this->handle_method,
                'handle_result' => $this->handle_result
            ]
        ];
    }
    
    //根据日期获取异常记录
    public static function getByDate($date, $machine_id = null, $exception_type = null){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'exception_date' => $date
        ];
        
        if($machine_id){
            $where['machine_id'] = $machine_id;
        }
        
        if($exception_type){
            $where['exception_type'] = $exception_type;
        }
        
        return self::where($where)
            ->with(['machineinfo', 'reporterinfo', 'handlerinfo'])
            ->order('exception_time desc')
            ->select();
    }
    
    //根据设备获取异常记录
    public static function getByMachine($machine_id, $start_date = null, $end_date = null){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'machine_id' => $machine_id
        ];
        
        if($start_date){
            $where[] = ['exception_date', '>=', $start_date];
        }
        
        if($end_date){
            $where[] = ['exception_date', '<=', $end_date];
        }
        
        return self::where($where)
            ->with(['reporterinfo', 'handlerinfo'])
            ->order('exception_date desc, exception_time desc')
            ->select();
    }
    
    //根据状态获取异常记录
    public static function getByStatus($status, $start_date = null, $end_date = null){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'status' => $status
        ];
        
        if($start_date){
            $where[] = ['exception_date', '>=', $start_date];
        }
        
        if($end_date){
            $where[] = ['exception_date', '<=', $end_date];
        }
        
        return self::where($where)
            ->with(['machineinfo', 'reporterinfo', 'handlerinfo'])
            ->order('exception_date desc, exception_time desc')
            ->select();
    }
    
    //获取异常统计
    public static function getExceptionStatistics($start_date, $end_date, $group_by = 'date'){
        $where = [
            'merchant' => Session('is_merchant_id'),
            ['exception_date', 'between', [$start_date, $end_date]]
        ];
        
        $field = 'exception_date,
                  COUNT(*) as exception_count,
                  COUNT(CASE WHEN status = 0 THEN 1 END) as pending_count,
                  COUNT(CASE WHEN status = 1 THEN 1 END) as handling_count,
                  COUNT(CASE WHEN status = 2 THEN 1 END) as completed_count';
        
        $group = 'exception_date';
        $order = 'exception_date asc';
        
        switch($group_by){
            case 'type':
                $field = 'exception_type,' . str_replace('exception_date,', '', $field);
                $group = 'exception_type';
                $order = 'exception_count desc';
                break;
            case 'machine':
                $field = 'machine_id,' . str_replace('exception_date,', '', $field);
                $group = 'machine_id';
                $order = 'exception_count desc';
                break;
            case 'severity':
                $field = 'severity,' . str_replace('exception_date,', '', $field);
                $group = 'severity';
                $order = 'exception_count desc';
                break;
        }
        
        return self::where($where)
            ->field($field)
            ->group($group)
            ->order($order)
            ->select();
    }
    
    //获取处理效率统计
    public static function getHandlingEfficiency($start_date, $end_date){
        $where = [
            'merchant' => Session('is_merchant_id'),
            ['exception_date', 'between', [$start_date, $end_date]],
            ['status', '=', 2] // 已完成
        ];
        
        return self::where($where)
            ->field('handler_id,
                     COUNT(*) as handled_count,
                     AVG(TIMESTAMPDIFF(MINUTE, 
                         CONCAT(exception_date, " ", handle_start_time), 
                         CONCAT(exception_date, " ", handle_end_time))) as avg_handle_time')
            ->with(['handlerinfo'])
            ->group('handler_id')
            ->order('handled_count desc')
            ->select();
    }
    
    //获取高频异常分析
    public static function getFrequentExceptions($start_date, $end_date){
        $where = [
            'merchant' => Session('is_merchant_id'),
            ['exception_date', 'between', [$start_date, $end_date]]
        ];
        
        return self::where($where)
            ->field('exception_type, machine_id,
                     COUNT(*) as occurrence_count,
                     GROUP_CONCAT(DISTINCT severity) as severity_levels')
            ->with(['machineinfo'])
            ->group('exception_type, machine_id')
            ->having('occurrence_count > 1')
            ->order('occurrence_count desc')
            ->select();
    }
    
    //验证异常数据
    public function validateExceptionData(){
        $errors = [];
        
        //检查时间逻辑
        if($this->handle_start_time && $this->handle_end_time){
            if($this->handle_start_time >= $this->handle_end_time){
                $errors[] = '处理开始时间不能晚于或等于结束时间';
            }
        }
        
        //检查状态逻辑
        if($this->status > 0 && !$this->handler_id){
            $errors[] = '异常处理中或已完成时必须指定处理人';
        }
        
        if($this->status == 2 && !$this->handle_result){
            $errors[] = '异常已完成时必须填写处理结果';
        }
        
        //检查严重程度与处理时间的合理性
        if($this->severity == 4 && $this->getHandleDuration() > 60){
            $errors[] = '紧急异常处理时间过长，请检查';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
