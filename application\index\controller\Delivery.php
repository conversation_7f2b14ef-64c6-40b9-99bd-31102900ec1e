<?php
namespace app\index\controller;
use think\Hook;
use app\index\controller\Acl;
use app\index\model\Deliveryclass;
use app\index\model\Deliveryinfo;
use app\index\model\Saleclass;
use app\index\model\Saleinfo;
use app\index\model\Room;
use app\index\model\Roominfo;
use app\index\model\Serial;
use app\index\model\Serialinfo;

class Delivery extends Acl {
    //发货单模块
    //---------------(^_^)---------------//
    
    //主视图
    public function main(){
        $input = input('get.');

        // 如果传入了销售订单ID，预填销售订单信息
        if(isset_full($input, 'sale_id')){
            try {
                $saleOrder = db('saleclass')->where(['id' => $input['sale_id']])->find();
                if($saleOrder){
                    $this->assign('sale_order', $saleOrder);
                    $this->assign('preset_sale_id', $input['sale_id']);
                }
            } catch(\Exception $e) {
                // 忽略错误，继续加载页面
            }
        }

        // 设置扩展字段（如果需要的话）
        $this->assign('more_html', '');

        return $this->fetch();
    }
    
    //发货单列表
    public function delivery_list(){
        $input = input('post.');
        //数据完整性判断
        if(isset_full($input,'page') && isset_full($input,'limit')){
            $sql = get_sql($input,[
                'number'=>'full_like',
                'sale_number'=>'full_like',
                'customer'=>'full_like',
                'data'=>'full_like'
            ],'delivery_class');//构造SQL
            $sql = auth('delivery',$sql);//数据鉴权
            
            $count = Deliveryclass::where($sql)->count();//获取总条数
            $arr = Deliveryclass::where($sql)
                ->with(['saleinfo','customerinfo','userinfo','auditinguserinfo'])
                ->page($input['page'],$input['limit'])
                ->order('id desc')
                ->select();//查询分页数据
                
            $resule = [
                'code'=>0,
                'msg'=>'获取成功',
                'count'=>$count,
                'data'=>$arr
            ];
        } else {
            $resule = ['code'=>1,'msg'=>'传入参数不完整!','count'=>0,'data'=>[]];
        }
        return json($resule);
    }
    
    //新增|更新发货单
    public function set(){
        $input = input('post.');

        // 验证必需字段
        if(!isset_full($input, 'sale_id') || !isset_full($input, 'time') || !isset_full($input, 'number')){
            return json(['state'=>'error','info'=>'参数不完整']);
        }

        //验证发货单详情
        if(isset_full($input,'tab')){
            if(empty($input['tab'])){
                return json(['state'=>'error','info'=>'发货商品不可为空!']);
            }
        }else{
            return json(['state'=>'error','info'=>'发货商品不可为空!']);
        }
            
        try {
            // 开始事务
            db()->startTrans();

            // 获取销售订单信息
            $saleOrder = db('saleclass')->where(['id' => $input['sale_id']])->find();
            if(!$saleOrder){
                return json(['state'=>'error','info'=>'销售订单不存在']);
            }

            //验证操作类型
            if(empty($input['id'])){
                //新增发货单
                $deliveryData = [
                    'merchant' => Session('is_merchant_id'),
                    'sale_id' => $input['sale_id'],
                    'customer' => $saleOrder['customer'],
                    'time' => strtotime($input['time']),
                    'number' => $input['number'],
                    'total_qty' => 0, // 稍后计算
                    'user' => Session('is_user_id'),
                    'data' => $input['data'] ?? '',
                    'type' => 0,
                    'auditinguser' => 0,
                    'auditingtime' => 0
                ];

                $deliveryId = db('delivery_class')->insertGetId($deliveryData);

                // 创建发货单详情
                $totalQty = 0;
                foreach($input['tab'] as $item){
                    // 获取销售订单详情
                    $saleInfo = db('saleinfo')->where(['id' => $item['sale_info_id']])->find();
                    if(!$saleInfo){
                        throw new \Exception('销售订单明细不存在');
                    }

                    // 验证超发：检查本次发货数量 + 已出库数量是否超过订单数量
                    $currentOutbound = $this->getOutboundQtyBySaleInfo($item['sale_info_id']);
                    $orderQty = $saleInfo['nums'];
                    $thisDeliveryQty = $item['nums'];

                    if(($currentOutbound + $thisDeliveryQty) > $orderQty){
                        $goodsInfo = db('goods')->where(['id' => $saleInfo['goods']])->find();
                        $goodsName = $goodsInfo ? $goodsInfo['name'] : '商品ID:' . $saleInfo['goods'];
                        throw new \Exception('商品【' . $goodsName . '】发货数量超出限制。订单数量:' . $orderQty . '，已出库:' . $currentOutbound . '，本次发货:' . $thisDeliveryQty);
                    }

                    // 验证库存：检查库存是否充足
                    $room = db('room')->where(['id' => $saleInfo['room']])->find();
                    if(!$room){
                        throw new \Exception('仓储信息不存在');
                    }

                    if($room['nums'] < $thisDeliveryQty){
                        $goodsInfo = db('goods')->where(['id' => $saleInfo['goods']])->find();
                        $goodsName = $goodsInfo ? $goodsInfo['name'] : '商品ID:' . $saleInfo['goods'];
                        throw new \Exception('商品【' . $goodsName . '】库存不足。当前库存:' . $room['nums'] . '，发货数量:' . $thisDeliveryQty);
                    }

                    $deliveryInfoData = [
                        'pid' => $deliveryId,
                        'sale_info_id' => $item['sale_info_id'],
                        'room' => $saleInfo['room'],
                        'goods' => $saleInfo['goods'],
                        'warehouse' => $saleInfo['warehouse'],
                        'serial' => '',
                        'nums' => $item['nums'],
                        'price' => $saleInfo['price'],
                        'total' => $item['nums'] * $saleInfo['price'],
                        'data' => ''
                    ];

                    db('delivery_info')->insert($deliveryInfoData);
                    $totalQty += $item['nums'];
                }

                // 更新发货单总数量
                db('delivery_class')->where(['id' => $deliveryId])->update(['total_qty' => $totalQty]);

                // 创建对应的其他出库单（销售出库）
                $this->createSaleOutbound($deliveryId, $input, $saleOrder);

                // 提交事务
                db()->commit();

                push_log('新增发货单[ '.$input['number'].' ]');//日志
                $resule = ['state'=>'success', 'info' => '发货单创建成功，已自动生成销售出库单'];

            }else{
                // 更新逻辑暂时不实现
                $resule = ['state'=>'error','info'=>'暂不支持更新发货单'];
            }

        } catch(\Exception $e) {
            // 回滚事务
            db()->rollback();
            $resule = ['state'=>'error','info'=>'创建发货单失败: ' . $e->getMessage()];
        }
        return json($resule);
    }
    
    //删除发货单
    public function del(){
        $input = input('post.');
        if(isset_full($input,'arr')){
            foreach ($input['arr'] as $arr_vo) {
                $class = Deliveryclass::where(['id'=>$arr_vo])->find();
                if(!empty($class['type']['nod'])){
                    return json(['state'=>'error','info'=>'发货单[ '.$class['number'].' ]已审核,无法删除!']);
                    exit;
                }
            }
            //实际删除
            foreach ($input['arr'] as $arr_vo) {
                $class = Deliveryclass::where(['id'=>$arr_vo])->find();
                Deliveryclass::destroy($arr_vo);//删除主表
                Deliveryinfo::destroy(['pid'=>$arr_vo]);//删除详情表
                push_log('删除发货单[ '.$class['number'].' ]');//日志
            }
            $resule = ['state'=>'success'];
        }else{
            $resule = ['state'=>'error','info'=>'传入参数不完整!'];
        }
        return json($resule);
    }

    //审核发货单
    public function auditing($arr=[],$auto=false){
        (empty($arr))&&($arr=input('post.arr'));//兼容多态审核
        if(empty($arr)){
            $resule=['state'=>'error','info'=>'传入参数不完整!'];
        }else{
            $class_data=[];//初始化CLASS数据
            $info_data=[];//初始化INFO数据

            //数据检验
            foreach ($arr as $arr_vo) {
                $class=Deliveryclass::where(['id'=>$arr_vo])->find();
                $info=Deliveryinfo::where(['pid'=>$arr_vo])->select();

                //判断操作类型
                if(empty($class['type']['nod'])){
                    //审核操作 - 检查库存是否充足
                    foreach ($info as $info_key=>$info_vo) {
                        $room = Room::where(['id'=>$info_vo['room']])->find();
                        if(empty($room) || $room['nums'] < $info_vo['nums']){
                            $auto&&(push_log('自动审核发货单[ '.$class['number'].' ]失败,原因:第'.($info_key+1).'行库存不足!'));//日志
                            return json(['state'=>'error','info'=>'审核-发货单[ '.$class['number'].' ]失败,原因:第'.($info_key+1).'行库存不足!']);
                            exit;
                        }

                        //检查串码状态
                        if (!empty($info_vo['serial'])){
                            $serial_sql=['code'=>['in',explode(',',$info_vo['serial'])],'type'=>['neq',0]];
                            $serial=Serial::where($serial_sql)->find();//查找串码状态为非未销售
                            if(!empty($serial)){
                                $auto&&(push_log('自动审核发货单[ '.$class['number'].' ]失败,原因:第'.($info_key+1).'行串码状态不正确!'));//日志
                                return json(['state'=>'error','info'=>'审核-发货单[ '.$class['number'].' ]失败,原因:第'.($info_key+1).'行串码状态不正确!']);
                                exit;
                            }
                        }
                    }
                }else{
                    //反审核操作 - 检查串码状态
                    foreach ($info as $info_key=>$info_vo) {
                        if(!empty($info_vo['serial'])){
                            $serial_sql=['code'=>['in',explode(',',$info_vo['serial'])],'type'=>['neq',1]];
                            $serial=Serial::where($serial_sql)->find();//查找串码状态为非已销售
                            if(!empty($serial)){
                                return json(['state'=>'error','info'=>'反审核-发货单[ '.$class['number'].' ]第'.($info_key+1).'行串码状态不正确!']);
                                exit;
                            }
                        }
                    }
                }
                $class_data[$arr_vo]=$class;//转存CLASS数据
                $info_data[$arr_vo]=$info;//转存INFO数据
            }

            //实际操作
            foreach ($arr as $arr_vo) {
                $class=$class_data[$arr_vo];//读取CLASS数据
                $info=$info_data[$arr_vo];//读取INFO数据

                //判断操作类型
                if(empty($class['type']['nod'])){
                    //审核操作 - 扣减库存
                    foreach ($info as $info_vo) {
                        //扣减库存
                        Room::where(['id'=>$info_vo['room']])->setDec('nums',$info_vo['nums']);

                        //新增仓储详情
                        $roominfo_sql['pid']=$info_vo['room'];
                        $roominfo_sql['type']=2; // 销售出库
                        $roominfo_sql['class']=$arr_vo;
                        $roominfo_sql['info']=$info_vo['id'];
                        $roominfo_sql['nums']=$info_vo['nums'];
                        Roominfo::create($roominfo_sql);

                        //注释：不再在发货单审核时更新销售订单明细的已发货数量
                        //已发货数量现在通过已审核的销售出库单来计算
                        //Saleinfo::where(['id'=>$info_vo['sale_info_id']])->setInc('delivered_qty',$info_vo['nums']);

                        //操作串码信息
                        if (!empty($info_vo['serial'])){
                            $serial_arr=explode(',',$info_vo['serial']);//分割串码信息
                            foreach ($serial_arr as $serial_arr_vo) {
                                //设置串码信息
                                $serial=Serial::where(['code'=>$serial_arr_vo])->find();//获取串码信息
                                Serial::update(['id'=>$serial['id'],'type'=>1]);
                                //新增串码详情
                                Serialinfo::create (['pid'=>$serial['id'],'type'=>2,'class'=>$arr_vo]);
                            }
                        }
                    }

                    //更新发货单状态
                    Deliveryclass::update(['id'=>$arr_vo,'type'=>1,'auditinguser'=>Session('is_user_id'),'auditingtime'=>time()]);

                    //更新销售订单发货状态
                    $this->updateSaleDeliveryStatus($class['sale_id']);

                    push_log(($auto?'自动':'').'审核发货单[ '.$class['number'].' ]');
                }else{
                    //反审核操作 - 恢复库存
                    foreach ($info as $info_vo){
                        //恢复库存
                        Room::where (['id'=>$info_vo['room']])->setInc('nums',$info_vo['nums']);

                        //注释：不再在发货单反审核时恢复销售订单明细的已发货数量
                        //已发货数量现在通过已审核的销售出库单来计算
                        //Saleinfo::where(['id'=>$info_vo['sale_info_id']])->setDec('delivered_qty',$info_vo['nums']);

                        //恢复串码状态
                        if(!empty($info_vo['serial'])){
                            $serial=Serial::where(['code'=>['in',explode(',',$info_vo['serial'])]])->select();//获取串码数据
                            foreach ($serial as $serial_vo) {
                                //设置串码数据
                                Serial::update(['id'=>$serial_vo['id'],'type'=>0]);
                                Serialinfo::where(['pid'=>$serial_vo['id'],'type'=>2,'class'=>$arr_vo])->delete();//删除串码详情
                            }
                        }
                    }

                    //删除仓储详情
                    Roominfo::where(['type'=>2,'class'=>$arr_vo])->delete();

                    //更新发货单状态
                    Deliveryclass::update(['id'=>$arr_vo,'type'=>0,'auditinguser'=>0,'auditingtime'=>0]);

                    //更新销售订单发货状态
                    $this->updateSaleDeliveryStatus($class['sale_id']);

                    push_log ('反审核发货单[ '.$class['number'].' ]');
                }
            }
            $resule=['state'=>'success'];
        }
        return json($resule);
    }

    //更新销售订单发货状态（基于已审核的销售出库单）
    private function updateSaleDeliveryStatus($saleId){
        $saleInfo = Saleinfo::where(['pid'=>$saleId])->select();
        $totalQty = 0;
        $outboundQty = 0;

        foreach($saleInfo as $item){
            $totalQty += $item['nums'];
            // 通过已审核的销售出库单计算已出库数量
            $outboundQty += $this->getOutboundQtyBySaleInfo($item['id']);
        }

        $deliveryStatus = 0; // 未发货
        if($outboundQty > 0){
            if($outboundQty >= $totalQty){
                $deliveryStatus = 2; // 已发货
            }else{
                $deliveryStatus = 1; // 部分发货
            }
        }

        Saleclass::update(['id'=>$saleId,'delivery_status'=>$deliveryStatus]);
    }

    /**
     * 通过销售订单明细ID获取已出库数量（基于已审核的销售出库单）
     * @param int $saleInfoId 销售订单明细ID
     * @return float 已出库数量
     */
    private function getOutboundQtyBySaleInfo($saleInfoId) {
        try {
            // 查找与该销售订单明细相关的所有发货单明细
            $deliveryInfos = db('delivery_info')->where(['sale_info_id' => $saleInfoId])->select();

            $totalOutboundQty = 0;

            foreach($deliveryInfos as $deliveryInfo) {
                // 查找与该发货单相关的已审核销售出库单
                $outboundRecords = db('otsaleinfo')
                    ->alias('oi')
                    ->join('otsaleclass oc', 'oi.pid = oc.id')
                    ->join('delivery_class dc', 'oc.delivery_id = dc.id')
                    ->where([
                        'dc.id' => $deliveryInfo['pid'], // 发货单ID
                        'oi.goods' => $deliveryInfo['goods'], // 商品ID
                        'oi.room' => $deliveryInfo['room'], // 仓储ID
                        'oc.type' => 1, // 已审核的出库单
                        'oc.pagetype' => 3 // 销售出库单
                    ])
                    ->field('oi.nums')
                    ->select();

                foreach($outboundRecords as $record) {
                    $totalOutboundQty += floatval($record['nums']);
                }
            }

            return $totalOutboundQty;

        } catch(\Exception $e) {
            return 0; // 出错时返回0
        }
    }

    /**
     * 创建销售出库单
     * @param int $deliveryId 发货单ID
     * @param array $input 发货单数据
     * @param array $saleOrder 销售订单数据
     */
    private function createSaleOutbound($deliveryId, $input, $saleOrder) {
        // 创建其他出库单主表数据
        $outboundData = [
            'merchant' => Session('is_merchant_id'),
            'time' => strtotime($input['time']),
            'number' => 'XCK' . date('YmdHis') . rand(100, 999), // 销售出库单号
            'pagetype' => 3, // 销售出库单类型
            'delivery_id' => $deliveryId, // 关联发货单ID - 这里是关键修复
            'user' => Session('is_user_id'),
            'data' => '发货单【' . $input['number'] . '】自动生成，销售订单【' . $saleOrder['number'] . '】',
            'type' => 0, // 未审核状态
            'auditinguser' => 0,
            'auditingtime' => 0
        ];

        $outboundId = db('otsaleclass')->insertGetId($outboundData);

        // 创建其他出库单明细数据
        $deliveryInfos = db('delivery_info')->where(['pid' => $deliveryId])->select();
        foreach($deliveryInfos as $deliveryInfo) {
            $outboundInfoData = [
                'pid' => $outboundId,
                'room' => $deliveryInfo['room'],
                'goods' => $deliveryInfo['goods'],
                'warehouse' => $deliveryInfo['warehouse'],
                'serial' => $deliveryInfo['serial'],
                'nums' => $deliveryInfo['nums'],
                'data' => '发货单明细自动生成'
            ];

            db('otsaleinfo')->insert($outboundInfoData);
        }

        push_log('自动生成销售出库单[ ' . $outboundData['number'] . ' ]');
    }
}
