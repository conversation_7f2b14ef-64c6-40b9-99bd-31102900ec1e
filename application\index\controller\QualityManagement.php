<?php
namespace app\index\controller;
use app\index\controller\Acl;

class QualityManagement extends Acl {
    //质量管理模块
    
    //主视图
    public function main(){
        return $this->fetch();
    }

    //获取质量检验列表
    public function get_list(){
        $input = input('post.');
        $where = auth('quality_inspection', []);

        //筛选条件
        if(isset_full($input, 'start_date')){
            $where[] = ['inspection_date', '>=', $input['start_date']];
        }
        if(isset_full($input, 'end_date')){
            $where[] = ['inspection_date', '<=', $input['end_date']];
        }
        if(isset_full($input, 'goods_name')){
            $where[] = ['goods_name', 'like', '%'.$input['goods_name'].'%'];
        }
        if(isset_full($input, 'result')){
            $where[] = ['result', '=', $input['result']];
        }

        try {
            // 返回模拟数据，参考生产订单的格式
            $mockData = [
                [
                    'id' => 1,
                    'inspection_no' => 'QC202412010001',
                    'goods_name' => '硅胶手机壳',
                    'sample_qty' => 100,
                    'qualified_qty' => 95,
                    'qualified_rate' => 95.0,
                    'inspection_date' => '2024-12-01',
                    'result' => 1,
                    'inspector_name' => '张检验',
                    'createtime' => '2024-12-01 14:30:15',
                    'goodsinfo' => ['name' => '硅胶手机壳'],
                    'inspectorinfo' => ['name' => '张检验']
                ],
                [
                    'id' => 2,
                    'inspection_no' => 'QC202412010002',
                    'goods_name' => '硅胶保护套',
                    'sample_qty' => 80,
                    'qualified_qty' => 76,
                    'qualified_rate' => 95.0,
                    'inspection_date' => '2024-12-01',
                    'result' => 1,
                    'inspector_name' => '李检验',
                    'createtime' => '2024-12-01 15:20:30',
                    'goodsinfo' => ['name' => '硅胶保护套'],
                    'inspectorinfo' => ['name' => '李检验']
                ],
                [
                    'id' => 3,
                    'inspection_no' => 'QC202412010003',
                    'goods_name' => '硅胶密封圈',
                    'sample_qty' => 50,
                    'qualified_qty' => 45,
                    'qualified_rate' => 90.0,
                    'inspection_date' => '2024-12-01',
                    'result' => 0,
                    'inspector_name' => '王检验',
                    'createtime' => '2024-12-01 16:10:45',
                    'goodsinfo' => ['name' => '硅胶密封圈'],
                    'inspectorinfo' => ['name' => '王检验']
                ]
            ];

            $list = [
                'total' => count($mockData),
                'data' => $mockData
            ];

            return json(['code' => 0, 'msg' => '', 'count' => $list['total'], 'data' => $list['data']]);
        } catch(\Exception $e) {
            return json(['code' => 1, 'msg' => '查询失败: ' . $e->getMessage(), 'count' => 0, 'data' => []]);
        }
    }

    //添加质量检验记录
    public function add(){
        if(request()->isPost()){
            $input = input('post.');

            //验证必填字段
            if(!isset_full($input, 'schedule_id') || !isset_full($input, 'sample_qty') || !isset_full($input, 'qualified_qty')){
                return json(['state' => 'error', 'info' => '请填写完整的检验信息']);
            }

            // 暂时返回成功，等待创建质量检验表
            return json(['state' => 'success', 'info' => '质量检验记录添加成功']);
        }

        return $this->fetch();
    }

    //编辑质量检验记录
    public function edit(){
        $id = input('id');
        if(request()->isPost()){
            $input = input('post.');

            // 暂时返回成功，等待创建质量检验表
            return json(['state' => 'success', 'info' => '质量检验记录更新成功']);
        }

        // 返回模拟的检验记录数据
        $inspection = [
            'id' => $id,
            'schedule_id' => 1,
            'inspection_date' => '2024-12-01',
            'sample_qty' => 100,
            'qualified_qty' => 95,
            'defect_qty' => 5,
            'qualified_rate' => 95.0,
            'result' => 1,
            'inspection_method' => '外观检验',
            'inspection_standard' => 'GB/T 2918-1998',
            'defect_description' => '表面有轻微划痕',
            'corrective_action' => '加强包装保护',
            'remarks' => '整体质量良好',
            'inspector_id' => 1
        ];

        $this->assign('inspection', $inspection);
        return $this->fetch();
    }

    //查看质量检验记录
    public function view(){
        $id = input('id');

        // 返回模拟的检验记录数据（只读）
        $inspection = [
            'id' => $id,
            'inspection_no' => 'QC202412010001',
            'schedule_id' => 1,
            'inspection_date' => '2024-12-01',
            'sample_qty' => 100,
            'qualified_qty' => 95,
            'defect_qty' => 5,
            'qualified_rate' => 95.0,
            'result' => 1,
            'result_text' => '合格',
            'inspection_method' => '外观检验',
            'inspection_standard' => 'GB/T 2918-1998',
            'defect_description' => '表面有轻微划痕',
            'corrective_action' => '加强包装保护',
            'remarks' => '整体质量良好',
            'inspector_id' => 1,
            'inspector_name' => '张检验',
            'goods_name' => '硅胶手机壳',
            'createtime' => '2024-12-01 14:30:15'
        ];

        $this->assign('inspection', $inspection);
        return $this->fetch();
    }

    //删除质量检验记录
    public function delete(){
        $id = input('id');
        try {
            // 暂时返回成功，等待创建质量检验表
            return json(['state' => 'success', 'info' => '删除成功']);
        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '删除失败: ' . $e->getMessage()]);
        }
    }

    //批量删除
    public function batch_delete(){
        $ids = input('ids');
        try {
            // 暂时返回成功，等待创建质量检验表
            return json(['state' => 'success', 'info' => '批量删除成功']);
        } catch(\Exception $e) {
            return json(['state' => 'error', 'info' => '批量删除失败: ' . $e->getMessage()]);
        }
    }

    //质量统计报表
    public function report(){
        return $this->fetch();
    }

    //获取质量统计数据（简化版）
    public function get_statistics(){
        // 直接返回模拟数据
        $data = [
            'state' => 'success',
            'data' => [
                'total' => [
                    'total_inspections' => 25,
                    'total_samples' => 1250,
                    'total_qualified' => 1188,
                    'total_defects' => 62,
                    'avg_qualified_rate' => 95.04
                ],
                'daily' => [
                    ['inspection_date' => '2024-01-01', 'inspections' => 5, 'samples' => 250, 'qualified' => 238, 'qualified_rate' => 95.2],
                    ['inspection_date' => '2024-01-02', 'inspections' => 4, 'samples' => 200, 'qualified' => 190, 'qualified_rate' => 95.0],
                    ['inspection_date' => '2024-01-03', 'inspections' => 6, 'samples' => 300, 'qualified' => 285, 'qualified_rate' => 95.0]
                ]
            ]
        ];

        return json($data);
    }
    
    //质量标准管理
    public function standard(){
        return $this->fetch();
    }
    
    //质量检验记录
    public function inspection(){
        return $this->fetch();
    }
    
    //不良品管理
    public function defect(){
        return $this->fetch();
    }
    
    //获取质量标准列表
    public function get_standards(){
        $input = input('post.');
        $where = auth('quality_standard', []);
        
        //搜索条件
        if(isset_full($input, 'standard_name')){
            $where[] = ['standard_name', 'like', '%'.$input['standard_name'].'%'];
        }
        if(isset_full($input, 'goods_id')){
            $where[] = ['goods_id', '=', $input['goods_id']];
        }
        if(isset_full($input, 'status')){
            $where[] = ['status', '=', $input['status']];
        }
        
        $list = db('quality_standard')
            ->alias('qs')
            ->join('goods g', 'qs.goods_id = g.id', 'left')
            ->join('user u', 'qs.creator = u.id', 'left')
            ->where($where)
            ->field('qs.*,g.name as goods_name,u.name as creator_name')
            ->order('qs.createtime desc')
            ->paginate(input('limit', 15))
            ->toArray();
            
        return json(['code' => 0, 'msg' => '', 'count' => $list['total'], 'data' => $list['data']]);
    }
    
    //新增|更新质量标准
    public function set_standard(){
        $input = input('post.');
        
        if(isset($input['id'])){
            if(empty($input['id'])){
                //新增
                $input['merchant'] = Session('is_merchant_id');
                $input['creator'] = Session('is_user_id');
                $input['createtime'] = time();
                
                //开启事务
                db()->startTrans();
                try {
                    $standard_id = db('quality_standard')->insertGetId($input);
                    
                    //保存标准明细
                    if(isset($input['details']) && is_array($input['details'])){
                        foreach($input['details'] as $detail){
                            $detail['standard_id'] = $standard_id;
                            db('quality_standard_detail')->insert($detail);
                        }
                    }
                    
                    db()->commit();
                    push_log('新增质量标准[ '.$input['standard_name'].' ]');
                    $result = ['state' => 'success', 'data' => ['id' => $standard_id]];
                } catch (\Exception $e) {
                    db()->rollback();
                    $result = ['state' => 'error', 'info' => '保存失败：' . $e->getMessage()];
                }
            }else{
                //更新
                //开启事务
                db()->startTrans();
                try {
                    db('quality_standard')->where(['id' => $input['id']])->update($input);
                    
                    //删除原有明细
                    db('quality_standard_detail')->where(['standard_id' => $input['id']])->delete();
                    
                    //保存新明细
                    if(isset($input['details']) && is_array($input['details'])){
                        foreach($input['details'] as $detail){
                            $detail['standard_id'] = $input['id'];
                            db('quality_standard_detail')->insert($detail);
                        }
                    }
                    
                    db()->commit();
                    push_log('更新质量标准[ '.$input['standard_name'].' ]');
                    $result = ['state' => 'success'];
                } catch (\Exception $e) {
                    db()->rollback();
                    $result = ['state' => 'error', 'info' => '更新失败：' . $e->getMessage()];
                }
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //获取质量标准详情
    public function get_standard_info(){
        $input = input('post.');
        if(isset_full($input, 'id')){
            $standard = db('quality_standard')
                ->alias('qs')
                ->join('goods g', 'qs.goods_id = g.id', 'left')
                ->where(['qs.id' => $input['id']])
                ->field('qs.*,g.name as goods_name')
                ->find();
                
            if($standard){
                $details = db('quality_standard_detail')
                    ->where(['standard_id' => $input['id']])
                    ->order('sort asc')
                    ->select();
                    
                $standard['details'] = $details;
                return json(['state' => 'success', 'data' => $standard]);
            }else{
                return json(['state' => 'error', 'info' => '质量标准不存在']);
            }
        }else{
            return json(['state' => 'error', 'info' => '传入参数不完整']);
        }
    }
    
    //删除质量标准
    public function del_standard(){
        $input = input('post.');
        if(isset_full($input, 'id')){
            //开启事务
            db()->startTrans();
            try {
                $standard = db('quality_standard')->where(['id' => $input['id']])->find();
                if($standard){
                    //删除标准明细
                    db('quality_standard_detail')->where(['standard_id' => $input['id']])->delete();
                    //删除标准
                    db('quality_standard')->where(['id' => $input['id']])->delete();
                    
                    db()->commit();
                    push_log('删除质量标准[ '.$standard['standard_name'].' ]');
                    $result = ['state' => 'success'];
                }else{
                    $result = ['state' => 'error', 'info' => '质量标准不存在'];
                }
            } catch (\Exception $e) {
                db()->rollback();
                $result = ['state' => 'error', 'info' => '删除失败：' . $e->getMessage()];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整'];
        }
        
        return json($result);
    }
    
    //获取质量检验列表
    public function get_inspections(){
        $input = input('post.');
        $where = auth('quality_inspection', []);
        
        //搜索条件
        if(isset_full($input, 'schedule_id')){
            $where[] = ['qi.schedule_id', '=', $input['schedule_id']];
        }
        if(isset_full($input, 'inspection_type')){
            $where[] = ['qi.inspection_type', '=', $input['inspection_type']];
        }
        if(isset_full($input, 'inspector_id')){
            $where[] = ['qi.inspector_id', '=', $input['inspector_id']];
        }
        if(isset_full($input, 'result')){
            $where[] = ['qi.result', '=', $input['result']];
        }
        if(isset_full($input, 'start_date')){
            $where[] = ['qi.inspection_time', '>=', $input['start_date'].' 00:00:00'];
        }
        if(isset_full($input, 'end_date')){
            $where[] = ['qi.inspection_time', '<=', $input['end_date'].' 23:59:59'];
        }
        
        $list = db('quality_inspection')
            ->alias('qi')
            ->join('production_schedule ps', 'qi.schedule_id = ps.id', 'left')
            ->join('production_order po', 'ps.production_order_id = po.id', 'left')
            ->join('goods g', 'po.goods_id = g.id', 'left')
            ->join('user u', 'qi.inspector_id = u.id', 'left')
            ->where($where)
            ->field('qi.*,po.order_no,g.name as goods_name,u.name as inspector_name')
            ->order('qi.inspection_time desc')
            ->paginate(input('limit', 15))
            ->toArray();
            
        return json(['code' => 0, 'msg' => '', 'count' => $list['total'], 'data' => $list['data']]);
    }
    
    //获取检验详情
    public function get_inspection_info(){
        $input = input('post.');
        if(isset_full($input, 'id')){
            $inspection = db('quality_inspection')
                ->alias('qi')
                ->join('production_schedule ps', 'qi.schedule_id = ps.id', 'left')
                ->join('production_order po', 'ps.production_order_id = po.id', 'left')
                ->join('goods g', 'po.goods_id = g.id', 'left')
                ->join('user u', 'qi.inspector_id = u.id', 'left')
                ->where(['qi.id' => $input['id']])
                ->field('qi.*,po.order_no,g.name as goods_name,u.name as inspector_name')
                ->find();
                
            if($inspection){
                $details = db('quality_inspection_detail')
                    ->where(['inspection_id' => $input['id']])
                    ->order('sort asc')
                    ->select();
                    
                $inspection['details'] = $details;
                return json(['state' => 'success', 'data' => $inspection]);
            }else{
                return json(['state' => 'error', 'info' => '检验记录不存在']);
            }
        }else{
            return json(['state' => 'error', 'info' => '传入参数不完整']);
        }
    }
    
    //质量统计分析
    public function quality_statistics(){
        $input = input('post.');
        $start_date = isset($input['start_date']) ? $input['start_date'] : date('Y-m-01');
        $end_date = isset($input['end_date']) ? $input['end_date'] : date('Y-m-d');
        
        $where = [
            'merchant' => Session('is_merchant_id'),
            'summary_date' => ['between', [$start_date, $end_date]]
        ];
        
        //总体质量统计
        $overall_stats = db('production_summary')
            ->where($where)
            ->field('SUM(actual_qty) as total_qty, SUM(good_qty) as good_qty, 
                     SUM(defect_qty) as defect_qty, AVG(qualified_rate) as avg_qualified_rate')
            ->find();
            
        //按商品分组统计
        $goods_stats = db('production_summary')
            ->alias('ps')
            ->join('goods g', 'ps.goods_id = g.id', 'left')
            ->where($where)
            ->field('ps.goods_id,g.name as goods_name,SUM(ps.actual_qty) as total_qty,
                     SUM(ps.good_qty) as good_qty,SUM(ps.defect_qty) as defect_qty,
                     AVG(ps.qualified_rate) as avg_qualified_rate')
            ->group('ps.goods_id')
            ->select();
            
        //按设备分组统计
        $machine_stats = db('production_summary')
            ->alias('ps')
            ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
            ->where($where)
            ->field('ps.machine_id,im.name as machine_name,SUM(ps.actual_qty) as total_qty,
                     SUM(ps.good_qty) as good_qty,SUM(ps.defect_qty) as defect_qty,
                     AVG(ps.qualified_rate) as avg_qualified_rate')
            ->group('ps.machine_id')
            ->select();
            
        //不良类型统计
        $defect_type_stats = db('defect_record')
            ->where([
                'merchant' => Session('is_merchant_id'),
                'createtime' => ['between', [strtotime($start_date), strtotime($end_date.' 23:59:59')]]
            ])
            ->field('defect_type,COUNT(*) as count,SUM(defect_qty) as total_qty')
            ->group('defect_type')
            ->order('total_qty desc')
            ->select();
            
        return json([
            'state' => 'success',
            'data' => [
                'overall' => $overall_stats,
                'by_goods' => $goods_stats,
                'by_machine' => $machine_stats,
                'defect_types' => $defect_type_stats
            ]
        ]);
    }
    
    //质量趋势分析
    public function quality_trend(){
        $input = input('post.');
        $days = isset($input['days']) ? $input['days'] : 30;
        
        $dates = [];
        $qualified_rates = [];
        $defect_rates = [];
        
        for($i = $days - 1; $i >= 0; $i--){
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $dates[] = $date;
            
            $stats = db('production_summary')
                ->where([
                    'merchant' => Session('is_merchant_id'),
                    'summary_date' => $date
                ])
                ->field('SUM(actual_qty) as total_qty, SUM(good_qty) as good_qty, SUM(defect_qty) as defect_qty')
                ->find();
                
            if($stats['total_qty'] > 0){
                $qualified_rates[] = round(($stats['good_qty'] / $stats['total_qty']) * 100, 2);
                $defect_rates[] = round(($stats['defect_qty'] / $stats['total_qty']) * 100, 2);
            }else{
                $qualified_rates[] = 0;
                $defect_rates[] = 0;
            }
        }
        
        return json([
            'state' => 'success',
            'data' => [
                'dates' => $dates,
                'qualified_rates' => $qualified_rates,
                'defect_rates' => $defect_rates
            ]
        ]);
    }
}
