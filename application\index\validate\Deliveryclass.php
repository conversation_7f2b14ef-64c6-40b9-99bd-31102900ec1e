<?php
namespace app\index\validate;
use think\Validate;

class Deliveryclass extends Validate
{
    protected $rule = [
        'merchant'      => 'require|integer',
        'sale_id'       => 'require|integer',
        'customer'      => 'require|integer',
        'time'          => 'require|integer',
        'number'        => 'require|max:32',
        'total_qty'     => 'require|float|>=:0',
        'user'          => 'require|integer',
        'data'          => 'max:128',
        'type'          => 'integer|in:0,1',
        'auditinguser'  => 'integer',
        'auditingtime'  => 'integer'
    ];

    protected $message = [
        'merchant.require'      => '商户ID不能为空',
        'merchant.integer'      => '商户ID必须是整数',
        'sale_id.require'       => '销售订单ID不能为空',
        'sale_id.integer'       => '销售订单ID必须是整数',
        'customer.require'      => '客户ID不能为空',
        'customer.integer'      => '客户ID必须是整数',
        'time.require'          => '发货日期不能为空',
        'time.integer'          => '发货日期格式不正确',
        'number.require'        => '发货单号不能为空',
        'number.max'            => '发货单号不能超过32个字符',
        'total_qty.require'     => '发货总数量不能为空',
        'total_qty.float'       => '发货总数量必须是数字',
        'total_qty.>='          => '发货总数量不能小于0',
        'user.require'          => '制单人不能为空',
        'user.integer'          => '制单人ID必须是整数',
        'data.max'              => '备注信息不能超过128个字符',
        'type.integer'          => '审核状态必须是整数',
        'type.in'               => '审核状态值不正确',
        'auditinguser.integer'  => '审核人ID必须是整数',
        'auditingtime.integer'  => '审核时间格式不正确'
    ];

    protected $scene = [
        'add'   => ['merchant', 'sale_id', 'customer', 'time', 'number', 'total_qty', 'user', 'data'],
        'edit'  => ['sale_id', 'customer', 'time', 'number', 'total_qty', 'user', 'data']
    ];
}
