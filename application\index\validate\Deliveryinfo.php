<?php
namespace app\index\validate;
use think\Validate;

class Deliveryinfo extends Validate
{
    protected $rule = [
        'pid'           => 'require|integer',
        'sale_info_id'  => 'require|integer',
        'room'          => 'require|integer',
        'goods'         => 'require|integer',
        'warehouse'     => 'require|integer',
        'serial'        => 'max:1000',
        'nums'          => 'require|float|>:0',
        'price'         => 'require|float|>=:0',
        'total'         => 'require|float|>=:0',
        'data'          => 'max:128'
    ];

    protected $message = [
        'pid.require'           => '发货单ID不能为空',
        'pid.integer'           => '发货单ID必须是整数',
        'sale_info_id.require'  => '销售订单明细ID不能为空',
        'sale_info_id.integer'  => '销售订单明细ID必须是整数',
        'room.require'          => '仓储ID不能为空',
        'room.integer'          => '仓储ID必须是整数',
        'goods.require'         => '商品ID不能为空',
        'goods.integer'         => '商品ID必须是整数',
        'warehouse.require'     => '仓库ID不能为空',
        'warehouse.integer'     => '仓库ID必须是整数',
        'serial.max'            => '串号不能超过1000个字符',
        'nums.require'          => '发货数量不能为空',
        'nums.float'            => '发货数量必须是数字',
        'nums.>'                => '发货数量必须大于0',
        'price.require'         => '单价不能为空',
        'price.float'           => '单价必须是数字',
        'price.>='              => '单价不能小于0',
        'total.require'         => '总价不能为空',
        'total.float'           => '总价必须是数字',
        'total.>='              => '总价不能小于0',
        'data.max'              => '备注信息不能超过128个字符'
    ];
}
