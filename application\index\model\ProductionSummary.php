<?php
namespace app\index\model;
use think\Model;

class ProductionSummary extends Model{
    //生产统计表
    protected $table = 'is_production_summary';
    
    protected $type = [
        'more' => 'json'
    ];
    
    //关联设备信息
    public function machineinfo(){
        return $this->hasOne('app\index\model\InjectionMachine', 'id', 'machine_id');
    }
    
    //关联班次信息
    public function shiftinfo(){
        return $this->hasOne('app\index\model\WorkShift', 'id', 'shift_id');
    }
    
    //关联商品信息
    public function goodsinfo(){
        return $this->hasOne('app\index\model\Goods', 'id', 'goods_id');
    }
    
    //关联操作员信息
    public function operatorinfo(){
        return $this->hasOne('app\index\model\User', 'id', 'operator_id');
    }
    
    //创建时间读取器
    protected function getCreatetimeAttr($val, $data){
        return $val ? date('Y-m-d H:i:s', $val) : '';
    }
    
    //统计日期读取器
    protected function getSummaryDateAttr($val, $data){
        return $val ? $val : '';
    }
    
    //完成率读取器
    protected function getCompletionRateAttr($val, $data){
        return $val ? round($val, 2) : 0;
    }
    
    //合格率读取器
    protected function getQualifiedRateAttr($val, $data){
        return $val ? round($val, 2) : 0;
    }
    
    //效率读取器
    protected function getEfficiencyAttr($val, $data){
        return $val ? round($val, 2) : 0;
    }
    
    //获取完整统计信息
    public function getFullStatistics(){
        return [
            'basic_info' => [
                'summary_date' => $this->summary_date,
                'machine_name' => $this->machineinfo ? $this->machineinfo->name : '',
                'shift_name' => $this->shiftinfo ? $this->shiftinfo->name : '',
                'goods_name' => $this->goodsinfo ? $this->goodsinfo->name : '',
                'operator_name' => $this->operatorinfo ? $this->operatorinfo->name : ''
            ],
            'production_data' => [
                'plan_qty' => $this->plan_qty,
                'actual_qty' => $this->actual_qty,
                'good_qty' => $this->good_qty,
                'defect_qty' => $this->defect_qty,
                'completion_rate' => $this->completion_rate,
                'qualified_rate' => $this->qualified_rate
            ],
            'time_data' => [
                'working_hours' => $this->working_hours,
                'downtime_hours' => $this->downtime_hours,
                'efficiency' => $this->efficiency
            ]
        ];
    }
    
    //根据日期获取统计
    public static function getByDate($date, $machine_id = null, $shift_id = null){
        $where = [
            'merchant' => Session('is_merchant_id'),
            'summary_date' => $date
        ];
        
        if($machine_id){
            $where['machine_id'] = $machine_id;
        }
        
        if($shift_id){
            $where['shift_id'] = $shift_id;
        }
        
        return self::where($where)
            ->with(['machineinfo', 'shiftinfo', 'goodsinfo', 'operatorinfo'])
            ->order('machine_id asc, shift_id asc')
            ->select();
    }
    
    //根据日期范围获取统计
    public static function getByDateRange($start_date, $end_date, $group_by = 'date'){
        $where = [
            'merchant' => Session('is_merchant_id'),
            ['summary_date', 'between', [$start_date, $end_date]]
        ];
        
        $field = 'summary_date,
                  SUM(plan_qty) as total_plan_qty,
                  SUM(actual_qty) as total_actual_qty,
                  SUM(good_qty) as total_good_qty,
                  SUM(defect_qty) as total_defect_qty,
                  SUM(working_hours) as total_working_hours,
                  SUM(downtime_hours) as total_downtime_hours,
                  AVG(completion_rate) as avg_completion_rate,
                  AVG(qualified_rate) as avg_qualified_rate,
                  AVG(efficiency) as avg_efficiency';
        
        $group = 'summary_date';
        $order = 'summary_date asc';
        
        switch($group_by){
            case 'machine':
                $field = 'machine_id,' . str_replace('summary_date,', '', $field);
                $group = 'machine_id';
                $order = 'total_actual_qty desc';
                break;
            case 'goods':
                $field = 'goods_id,' . str_replace('summary_date,', '', $field);
                $group = 'goods_id';
                $order = 'total_actual_qty desc';
                break;
            case 'shift':
                $field = 'shift_id,' . str_replace('summary_date,', '', $field);
                $group = 'shift_id';
                $order = 'total_actual_qty desc';
                break;
        }
        
        return self::where($where)
            ->field($field)
            ->group($group)
            ->order($order)
            ->select();
    }
    
    //获取设备利用率统计
    public static function getMachineUtilization($start_date, $end_date){
        $where = [
            'merchant' => Session('is_merchant_id'),
            ['summary_date', 'between', [$start_date, $end_date]]
        ];
        
        return self::where($where)
            ->field('machine_id,
                     COUNT(*) as work_days,
                     SUM(working_hours) as total_working_hours,
                     SUM(downtime_hours) as total_downtime_hours,
                     AVG(efficiency) as avg_efficiency')
            ->with(['machineinfo'])
            ->group('machine_id')
            ->order('avg_efficiency desc')
            ->select();
    }
    
    //获取操作员绩效统计
    public static function getOperatorPerformance($start_date, $end_date){
        $where = [
            'merchant' => Session('is_merchant_id'),
            ['summary_date', 'between', [$start_date, $end_date]]
        ];
        
        return self::where($where)
            ->field('operator_id,
                     COUNT(*) as work_days,
                     SUM(actual_qty) as total_output,
                     SUM(good_qty) as total_good_qty,
                     AVG(qualified_rate) as avg_qualified_rate,
                     AVG(efficiency) as avg_efficiency')
            ->with(['operatorinfo'])
            ->group('operator_id')
            ->order('total_output desc')
            ->select();
    }
    
    //获取商品生产统计
    public static function getGoodsProduction($start_date, $end_date){
        $where = [
            'merchant' => Session('is_merchant_id'),
            ['summary_date', 'between', [$start_date, $end_date]]
        ];
        
        return self::where($where)
            ->field('goods_id,
                     SUM(plan_qty) as total_plan_qty,
                     SUM(actual_qty) as total_actual_qty,
                     SUM(good_qty) as total_good_qty,
                     SUM(defect_qty) as total_defect_qty,
                     AVG(completion_rate) as avg_completion_rate,
                     AVG(qualified_rate) as avg_qualified_rate')
            ->with(['goodsinfo'])
            ->group('goods_id')
            ->order('total_actual_qty desc')
            ->select();
    }
    
    //计算OEE指标
    public static function calculateOEE($start_date, $end_date, $machine_id = null){
        $where = [
            'merchant' => Session('is_merchant_id'),
            ['summary_date', 'between', [$start_date, $end_date]]
        ];
        
        if($machine_id){
            $where['machine_id'] = $machine_id;
        }
        
        $stats = self::where($where)
            ->field('SUM(plan_qty) as total_plan_qty,
                     SUM(actual_qty) as total_actual_qty,
                     SUM(good_qty) as total_good_qty,
                     SUM(working_hours) as total_working_hours,
                     SUM(downtime_hours) as total_downtime_hours')
            ->find();
        
        if(!$stats){
            return ['availability' => 0, 'performance' => 0, 'quality' => 0, 'oee' => 0];
        }
        
        // 可用性 = 运行时间 / 计划时间
        $total_hours = $stats->total_working_hours + $stats->total_downtime_hours;
        $availability = $total_hours > 0 ? $stats->total_working_hours / $total_hours : 0;
        
        // 性能 = 实际产量 / 计划产量
        $performance = $stats->total_plan_qty > 0 ? $stats->total_actual_qty / $stats->total_plan_qty : 0;
        
        // 质量 = 合格品数量 / 总产量
        $quality = $stats->total_actual_qty > 0 ? $stats->total_good_qty / $stats->total_actual_qty : 0;
        
        // OEE = 可用性 × 性能 × 质量
        $oee = $availability * $performance * $quality;
        
        return [
            'availability' => round($availability * 100, 2),
            'performance' => round($performance * 100, 2),
            'quality' => round($quality * 100, 2),
            'oee' => round($oee * 100, 2)
        ];
    }
}
