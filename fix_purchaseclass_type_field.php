<?php
// 修复 purchaseclass 表 type 字段默认值问题
// 执行日期: 2024-12-31
// 问题描述: SQLSTATE[HY000]: General error: 1364 Field 'type' doesn't have a default value

$host = '*************';
$username = 'root';
$password = '1234567';
$database = 'ZS_ERP';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h3>修复 purchaseclass 表 type 字段默认值问题</h3>";
    
    // 1. 检查当前 type 字段的定义
    echo "<h4>1. 检查当前字段定义</h4>";
    $sql_check = "SHOW COLUMNS FROM `is_purchaseclass` LIKE 'type'";
    $stmt = $pdo->query($sql_check);
    $column_info = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($column_info) {
        echo "当前 type 字段定义：<br>";
        echo "Field: " . $column_info['Field'] . "<br>";
        echo "Type: " . $column_info['Type'] . "<br>";
        echo "Null: " . $column_info['Null'] . "<br>";
        echo "Default: " . ($column_info['Default'] ?? 'NULL') . "<br>";
        echo "Extra: " . $column_info['Extra'] . "<br><br>";
    } else {
        echo "type 字段不存在！<br><br>";
    }
    
    // 2. 修改 type 字段，添加默认值
    echo "<h4>2. 修改字段定义，添加默认值</h4>";
    $sql_modify = "ALTER TABLE `is_purchaseclass` MODIFY COLUMN `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '审核状态[0:未审核|1:已审核]'";
    
    try {
        $pdo->exec($sql_modify);
        echo "✓ 成功修改 type 字段，添加默认值 0<br>";
        echo "执行的SQL: " . $sql_modify . "<br><br>";
    } catch(PDOException $e) {
        echo "✗ 修改字段失败: " . $e->getMessage() . "<br><br>";
    }
    
    // 3. 更新现有记录中 type 为 NULL 的数据
    echo "<h4>3. 更新现有 NULL 数据</h4>";
    $sql_update = "UPDATE `is_purchaseclass` SET `type` = 0 WHERE `type` IS NULL";
    
    try {
        $affected_rows = $pdo->exec($sql_update);
        echo "✓ 成功更新 {$affected_rows} 条记录的 type 字段为 0<br>";
        echo "执行的SQL: " . $sql_update . "<br><br>";
    } catch(PDOException $e) {
        echo "✗ 更新数据失败: " . $e->getMessage() . "<br><br>";
    }
    
    // 4. 验证修复结果
    echo "<h4>4. 验证修复结果</h4>";
    $sql_verify = "SHOW COLUMNS FROM `is_purchaseclass` LIKE 'type'";
    $stmt = $pdo->query($sql_verify);
    $new_column_info = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($new_column_info) {
        echo "修复后 type 字段定义：<br>";
        echo "Field: " . $new_column_info['Field'] . "<br>";
        echo "Type: " . $new_column_info['Type'] . "<br>";
        echo "Null: " . $new_column_info['Null'] . "<br>";
        echo "Default: " . ($new_column_info['Default'] ?? 'NULL') . "<br>";
        echo "Extra: " . $new_column_info['Extra'] . "<br><br>";
        
        if ($new_column_info['Default'] === '0') {
            echo "✓ 字段修复成功！type 字段现在有默认值 0<br>";
        } else {
            echo "✗ 字段修复可能未完全成功，请检查<br>";
        }
    }
    
    // 5. 检查是否还有 NULL 值
    echo "<h4>5. 检查数据完整性</h4>";
    $sql_count_null = "SELECT COUNT(*) as null_count FROM `is_purchaseclass` WHERE `type` IS NULL";
    $stmt = $pdo->query($sql_count_null);
    $null_count = $stmt->fetch(PDO::FETCH_ASSOC)['null_count'];
    
    if ($null_count == 0) {
        echo "✓ 所有记录的 type 字段都有值，数据完整性良好<br>";
    } else {
        echo "✗ 还有 {$null_count} 条记录的 type 字段为 NULL，需要进一步处理<br>";
    }
    
    echo "<br><h3>修复完成！</h3>";
    echo "<p>现在可以正常使用购货单功能了。</p>";
    
} catch(PDOException $e) {
    echo "<h3>错误</h3>";
    echo "数据库连接或操作失败: " . $e->getMessage();
}
?>
