<?php
namespace app\index\validate;
use think\Validate;

class Mold extends Validate
{
    protected $rule = [
        'name'              => 'require|max:64',
        'code'              => 'require|max:32|unique:mold',
        'goods_id'          => 'integer|gt:0',
        'cavity_count'      => 'require|integer|gt:0',
        'cycle_time'        => 'float|egt:0',
        'weight_per_shot'   => 'float|egt:0',
        'status'            => 'require|in:0,1,2,3',
        'data'              => 'max:256'
    ];

    protected $message = [
        'name.require'              => '模具名称不能为空',
        'name.max'                  => '模具名称不能超过64个字符',
        'code.require'              => '模具编号不能为空',
        'code.max'                  => '模具编号不能超过32个字符',
        'code.unique'               => '模具编号已存在',
        'goods_id.integer'          => '商品ID必须是整数',
        'goods_id.gt'               => '请选择对应商品',
        'cavity_count.require'      => '穴数不能为空',
        'cavity_count.integer'      => '穴数必须是整数',
        'cavity_count.gt'           => '穴数必须大于0',
        'cycle_time.float'          => '周期时间必须是数字',
        'cycle_time.egt'            => '周期时间不能小于0',
        'weight_per_shot.float'     => '单次射胶重量必须是数字',
        'weight_per_shot.egt'       => '单次射胶重量不能小于0',
        'status.require'            => '模具状态不能为空',
        'status.in'                 => '模具状态值不正确',
        'data.max'                  => '备注信息不能超过256个字符'
    ];

    protected $scene = [
        'add'   => ['name', 'code', 'goods_id', 'cavity_count', 'cycle_time', 'weight_per_shot', 'status', 'data'],
        'edit'  => ['name', 'goods_id', 'cavity_count', 'cycle_time', 'weight_per_shot', 'status', 'data']
    ];
}
