<?php
namespace app\index\controller;
use app\index\controller\Acl;
use app\index\model\WorkShift;

class Shift extends Acl{
    
    //主页面
    public function main(){
        return $this->fetch();
    }
    
    //表单页面
    public function form(){
        $id = input('id', 0);
        $shift = [];
        if($id > 0){
            $shift = \app\index\model\WorkShift::get($id);
            if(!$shift){
                $this->error('班次不存在!');
            }
            $shift = $shift->toArray();

            // 调试信息
            error_log('班次数据: ' . json_encode($shift));
        }
        $this->assign('shift', $shift);
        return $this->fetch();
    }
    
    //详情页面
    public function info(){
        $id = input('id', 0);
        if($id <= 0){
            $this->error('参数错误!');
        }

        $shift = WorkShift::get($id);
        if(!$shift){
            $this->error('班次不存在!');
        }

        $shift = $shift->toArray();
        $this->assign('shift', $shift);
        return $this->fetch();
    }
    
    //获取班次列表
    public function get_list(){
        $input = input('post.');
        $where = auth('work_shift', []);
        
        //搜索条件
        if(isset($input['name']) && !empty($input['name'])){
            $where[] = ['name', 'like', '%'.$input['name'].'%'];
        }
        
        if(isset($input['code']) && !empty($input['code'])){
            $where[] = ['code', 'like', '%'.$input['code'].'%'];
        }
        
        if(isset($input['status']) && $input['status'] !== ''){
            $where[] = ['status', '=', $input['status']];
        }
        
        $list = \app\index\model\WorkShift::where($where)
            ->order('sort asc, id asc')
            ->paginate(input('limit', 20), false, ['query' => request()->param()]);
            
        return json(['code' => 0, 'msg' => '', 'count' => $list->total(), 'data' => $list->items()]);
    }
    
    //新增|更新班次
    public function set(){
        $input = input('post.');
        
        if(isset($input['id']) && $input['id'] > 0){
            //更新
            // 验证时排除不需要的字段
            $validate_data = $input;
            $vali = $this->validate($validate_data, 'WorkShift');
            if($vali === true){
                //开启事务
                db()->startTrans();
                try {
                    $shift = WorkShift::update($input);
                    
                    db()->commit();
                    push_log('更新班次[ '.$shift['name'].' ]');
                    $result = ['state' => 'success'];
                } catch (\Exception $e) {
                    db()->rollback();
                    $result = ['state' => 'error', 'info' => '更新失败：' . $e->getMessage()];
                }
            }else{
                $result = ['state' => 'error', 'info' => $vali];
            }
        }else{
            //新增
            $input['merchant'] = Session('is_merchant_id');
            
            $vali = $this->validate($input, 'WorkShift');
            if($vali === true){
                //开启事务
                db()->startTrans();
                try {
                    $shift = WorkShift::create($input);
                    
                    db()->commit();
                    push_log('新增班次[ '.$shift['name'].' ]');
                    $result = ['state' => 'success'];
                } catch (\Exception $e) {
                    db()->rollback();
                    $result = ['state' => 'error', 'info' => '保存失败：' . $e->getMessage()];
                }
            }else{
                $result = ['state' => 'error', 'info' => $vali];
            }
        }
        
        return json($result);
    }
    
    //删除班次
    public function del(){
        $input = input('post.');
        if(isset($input['id']) && $input['id'] > 0){
            $shift = WorkShift::find($input['id']);
            if($shift){
                // 检查是否有生产订单使用此班次
                $order_count = db('production_schedule')->where(['shift_id' => $input['id']])->count();
                if($order_count > 0){
                    $result = ['state' => 'error', 'info' => '该班次已被生产计划使用，无法删除！'];
                } else {
                    $shift->delete();
                    push_log('删除班次[ '.$shift['name'].' ]');
                    $result = ['state' => 'success'];
                }
            }else{
                $result = ['state' => 'error', 'info' => '班次不存在!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //启用/停用班次
    public function toggle_status(){
        $input = input('post.');
        if(isset($input['id']) && isset($input['status']) && $input['id'] > 0 && in_array($input['status'], ['0', '1', 0, 1])){
            $shift = WorkShift::get($input['id']);
            if($shift){
                $shift->status = $input['status'];
                $shift->save();
                
                $status_text = $input['status'] == 1 ? '启用' : '停用';
                push_log('班次[ '.$shift['name'].' ]'.$status_text);
                $result = ['state' => 'success'];
            }else{
                $result = ['state' => 'error', 'info' => '班次不存在!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //获取可用班次列表(用于下拉选择)
    public function get_available(){
        $where = auth('work_shift', []);
        $where[] = ['status', '=', 1]; //只获取启用的班次
        
        $list = WorkShift::where($where)
            ->field('id,name,code,start_time,end_time,duration')
            ->order('sort asc, id asc')
            ->select();
            
        return json(['code' => 0, 'msg' => '', 'data' => $list]);
    }
    
    //计算工作时长
    public function calculate_duration(){
        $start_time = input('start_time');
        $end_time = input('end_time');
        
        if($start_time && $end_time){
            $start = strtotime($start_time);
            $end = strtotime($end_time);
            
            // 处理跨天的情况
            if($end <= $start){
                $end += 24 * 3600; // 加一天
            }
            
            $duration = ($end - $start) / 3600; // 转换为小时
            
            return json(['code' => 0, 'duration' => $duration]);
        }
        
        return json(['code' => 1, 'msg' => '参数错误']);
    }

    //批量删除班次
    public function batch_del(){
        $input = input('post.');
        if(isset($input['ids']) && is_array($input['ids']) && !empty($input['ids'])){
            $success_count = 0;
            $error_messages = [];

            foreach($input['ids'] as $id){
                $shift = WorkShift::get($id);
                if($shift){
                    // 检查是否有生产计划使用此班次
                    $order_count = db('production_schedule')->where(['shift_id' => $id])->count();
                    if($order_count > 0){
                        $error_messages[] = '班次【' . $shift->name . '】已被生产计划使用，无法删除';
                    } else {
                        $shift->delete();
                        $success_count++;
                        push_log('删除班次[ '.$shift['name'].' ]');
                    }
                }
            }

            if($success_count > 0){
                $message = "成功删除 {$success_count} 个班次";
                if(!empty($error_messages)){
                    $message .= "，" . implode('；', $error_messages);
                }
                $result = ['state' => 'success', 'info' => $message];
            } else {
                $result = ['state' => 'error', 'info' => implode('；', $error_messages)];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }

        return json($result);
    }

    //获取班次统计数据
    public function get_stats(){
        $id = input('id', 0);
        if($id <= 0){
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        $shift = WorkShift::get($id);
        if(!$shift){
            return json(['code' => 1, 'msg' => '班次不存在']);
        }

        $stats = $shift->getStats();

        return json(['code' => 0, 'data' => $stats]);
    }

    //获取班次工作负荷
    public function get_workload(){
        $id = input('id', 0);
        $date = input('date', date('Y-m-d'));

        if($id <= 0){
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        $shift = WorkShift::get($id);
        if(!$shift){
            return json(['code' => 1, 'msg' => '班次不存在']);
        }

        $workload = $shift->getWorkload($date);

        return json(['code' => 0, 'data' => $workload]);
    }
}
