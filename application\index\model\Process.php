<?php
namespace app\index\model;
use think\Model;

class Process extends Model
{
    protected $table = 'is_process';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 状态读取器
    protected function getStatusAttr($val, $data){
        $status = [0 => '停用', 1 => '启用'];
        return isset($status[$val]) ? $status[$val] : '未知';
    }
    
    // 状态原始值读取器
    protected function getStatusValueAttr($val, $data){
        return $data['status'];
    }
    
    // 工艺类型读取器
    protected function getTypeAttr($val, $data){
        $types = [
            'mixing' => '配料混炼',
            'molding' => '模压成型',
            'injection' => '注射成型',
            'extrusion' => '挤出成型',
            'vulcanization' => '硫化',
            'trimming' => '修边去毛刺',
            'secondary_vulcanization' => '二次硫化',
            'surface_treatment' => '表面处理',
            'printing' => '丝印/移印',
            'assembly' => '装配',
            'quality_check' => '质量检验',
            'packaging' => '包装',
            'other' => '其他'
        ];
        return isset($types[$val]) ? $types[$val] : $val;
    }
    
    // 工艺类型原始值读取器
    protected function getTypeValueAttr($val, $data){
        return $data['type'];
    }
    
    // 难度等级读取器
    protected function getDifficultyAttr($val, $data){
        $levels = [1 => '简单', 2 => '一般', 3 => '困难', 4 => '复杂', 5 => '极难'];
        return isset($levels[$val]) ? $levels[$val] : '未知';
    }
    
    // 难度等级原始值读取器
    protected function getDifficultyValueAttr($val, $data){
        return $data['difficulty'];
    }
    
    // 创建时间读取器
    protected function getCreateTimeAttr($val, $data){
        return $val ? date('Y-m-d H:i:s', $val) : '';
    }
    
    // 更新时间读取器
    protected function getUpdateTimeAttr($val, $data){
        return $val ? date('Y-m-d H:i:s', $val) : '';
    }
    
    // 工艺步骤读取器（JSON格式）
    protected function getStepsAttr($val, $data){
        return $val ? json_decode($val, true) : [];
    }
    
    // 工艺步骤修改器
    protected function setStepsAttr($val, $data){
        return is_array($val) ? json_encode($val) : $val;
    }
    
    // 质量要求读取器（JSON格式）
    protected function getQualityRequirementsAttr($val, $data){
        return $val ? json_decode($val, true) : [];
    }
    
    // 质量要求修改器
    protected function setQualityRequirementsAttr($val, $data){
        return is_array($val) ? json_encode($val) : $val;
    }
    
    // 所需设备读取器（JSON格式）
    protected function getRequiredEquipmentAttr($val, $data){
        return $val ? json_decode($val, true) : [];
    }
    
    // 所需设备修改器
    protected function setRequiredEquipmentAttr($val, $data){
        return is_array($val) ? json_encode($val) : $val;
    }
    
    // 工艺参数读取器（JSON格式）
    protected function getParametersAttr($val, $data){
        return $val ? json_decode($val, true) : [];
    }
    
    // 工艺参数修改器
    protected function setParametersAttr($val, $data){
        return is_array($val) ? json_encode($val) : $val;
    }
    
    // 安全注意事项读取器（JSON格式）
    protected function getSafetyNotesAttr($val, $data){
        return $val ? json_decode($val, true) : [];
    }
    
    // 安全注意事项修改器
    protected function setSafetyNotesAttr($val, $data){
        return is_array($val) ? json_encode($val) : $val;
    }
    
    // 工艺文档读取器（JSON格式）
    protected function getDocumentsAttr($val, $data){
        return $val ? json_decode($val, true) : [];
    }
    
    // 工艺文档修改器
    protected function setDocumentsAttr($val, $data){
        return is_array($val) ? json_encode($val) : $val;
    }
    
    // 获取工艺统计信息
    public static function getStatistics(){
        $total = self::count();
        $active = self::where('status', 1)->count();
        $inactive = self::where('status', 0)->count();
        
        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $inactive
        ];
    }
    
    // 根据类型获取工艺列表
    public static function getByType($type){
        return self::where('type', $type)
            ->where('status', 1)
            ->order('sort asc, id desc')
            ->select();
    }
    
    // 根据难度等级获取工艺列表
    public static function getByDifficulty($difficulty){
        return self::where('difficulty', $difficulty)
            ->where('status', 1)
            ->order('sort asc, id desc')
            ->select();
    }
    
    // 搜索工艺
    public static function search($keyword){
        return self::where('name|code|description', 'like', '%'.$keyword.'%')
            ->where('status', 1)
            ->order('sort asc, id desc')
            ->select();
    }
}
