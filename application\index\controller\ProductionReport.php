<?php
namespace app\index\controller;
use app\index\controller\Acl;
use app\index\model\ProductionReport as ProductionReportModel;
use app\index\model\ProductionSchedule;
use app\index\model\ProductionOrder;

class ProductionReport extends Acl {
    //生产报工管理模块
    
    //主视图
    public function main(){
        return $this->fetch();
    }

    //手机端视图
    public function mobile(){
        return $this->fetch();
    }

    //查看报工记录
    public function view(){
        
        $schedule_id = input('schedule_id');
        $order_id = input('order_id');

        if(!$schedule_id && !$order_id){
            $this->error('参数错误：需要提供排产ID或生产订单ID');
        }

        if($order_id){
            // 通过生产订单ID查看所有相关的报工记录
            $this->viewByOrderId($order_id);
        } else {
            // 通过排产ID查看单个排产的报工记录
            $this->viewByScheduleId($schedule_id);
        }
    }

    //通过排产ID查看报工记录
    private function viewByScheduleId($schedule_id){
        // 获取排产信息
        $schedule = db('production_schedule')
            ->alias('ps')
            ->join('production_order po', 'ps.production_order_id = po.id', 'left')
            ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
            ->join('work_shift ws', 'ps.shift_id = ws.id', 'left')
            ->join('goods g', 'po.goods_id = g.id', 'left')
            ->where('ps.id', $schedule_id)
            ->where('ps.merchant', Session('is_merchant_id'))
            ->field('ps.*, po.order_no, po.priority, im.name as machine_name,
                    ws.name as shift_name, g.name as goods_name')
            ->find();

        if(!$schedule){
            $this->error('排产记录不存在');
        }

        // 获取该排产的所有报工记录
        $reports = ProductionReportModel::where([
            'schedule_id' => $schedule_id,
            'merchant' => Session('is_merchant_id')
        ])->with(['operatorinfo'])
          ->order('report_time desc')
          ->select();

        $this->assign('schedule', $schedule);
        $this->assign('reports', $reports);
        $this->assign('view_type', 'single');
        return $this->fetch();
    }

    //通过生产订单ID查看所有报工记录
    private function viewByOrderId($order_id){
        // 获取生产订单信息
        $order = db('production_order')
            ->alias('po')
            ->join('goods g', 'po.goods_id = g.id', 'left')
            ->where('po.id', $order_id)
            ->where('po.merchant', Session('is_merchant_id'))
            ->field('po.id, po.order_no, po.plan_qty, po.status, po.priority, po.createtime, po.goods_id, g.name as goods_name')
            ->find();

        if(!$order){
            $this->error('生产订单不存在');
        }

        // 获取该订单的所有排产计划
        $schedules = db('production_schedule')
            ->alias('ps')
            ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
            ->join('work_shift ws', 'ps.shift_id = ws.id', 'left')
            ->where('ps.production_order_id', $order_id)
            ->where('ps.merchant', Session('is_merchant_id'))
            ->field('ps.*, im.name as machine_name, ws.name as shift_name')
            ->order('ps.plan_start_time asc')
            ->select();

        // 获取所有相关的报工记录
        $all_reports = [];
        foreach($schedules as $schedule){
            $reports = ProductionReportModel::where([
                'schedule_id' => $schedule['id'],
                'merchant' => Session('is_merchant_id')
            ])->with(['operatorinfo'])
              ->order('report_time desc')
              ->select();

            foreach($reports as $report){
                $report['schedule_info'] = $schedule;
                $all_reports[] = $report;
            }
        }

        // 按报工时间排序
        usort($all_reports, function($a, $b){
            return strtotime($b['report_time']) - strtotime($a['report_time']);
        });

        $this->assign('order', $order);
        $this->assign('schedules', $schedules);
        $this->assign('reports', $all_reports);
        $this->assign('view_type', 'order');
        return $this->fetch();
    }
    
    //获取待报工的排产列表
    public function get_pending_schedules(){
        $input = input('post.');
        $where = auth('production_schedule', []);
        $where[] = ['status', 'in', [0, 1, 3]]; //待生产、生产中、暂停
        $where[] = ['schedule_date', '=', date('Y-m-d')]; //今日排产
        
        //操作员筛选
        if(isset_full($input, 'operator_id')){
            $where[] = ['operator_id', '=', $input['operator_id']];
        }
        
        //设备筛选
        if(isset_full($input, 'machine_id')){
            $where[] = ['machine_id', '=', $input['machine_id']];
        }
        
        $list = ProductionSchedule::with(['orderinfo', 'machineinfo', 'shiftinfo', 'operatorinfo'])
            ->where($where)
            ->order('plan_start_time asc')
            ->paginate(input('limit', 15))
            ->toArray();
            
        return json(['code' => 0, 'msg' => '', 'count' => $list['total'], 'data' => $list['data']]);
    }
    
    //开工报告
    public function start_work(){
        $input = input('post.');
        if(isset_full($input, 'schedule_id')){
            $schedule = ProductionSchedule::find($input['schedule_id']);
            if($schedule && $schedule->status == 0){
                //开启事务
                db()->startTrans();
                try {
                    //更新排产状态
                    $schedule->status = 1; //生产中
                    $schedule->actual_start_time = date('Y-m-d H:i:s');
                    $schedule->save();
                    
                    //记录报工
                    $report_data = [
                        'merchant' => Session('is_merchant_id'),
                        'schedule_id' => $input['schedule_id'],
                        'report_type' => 1, //开工
                        'report_time' => date('Y-m-d H:i:s'),
                        'operator_id' => Session('is_user_id'),
                        'machine_status' => isset($input['machine_status']) ? $input['machine_status'] : 1,
                        'data' => isset($input['data']) ? $input['data'] : '',
                        'createtime' => time()
                    ];
                    
                    ProductionReportModel::create($report_data);
                    
                    //更新生产订单状态
                    $order = ProductionOrder::get($schedule->production_order_id);
                    if($order){
                        $order->updateStatus();
                    }
                    
                    db()->commit();
                    push_log('开工报告 - 排产ID: '.$input['schedule_id']);
                    $result = ['state' => 'success'];
                } catch (\Exception $e) {
                    db()->rollback();
                    $result = ['state' => 'error', 'info' => '开工报告失败：' . $e->getMessage()];
                }
            }else{
                $result = ['state' => 'error', 'info' => '排产状态不正确!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //完工报告（支持多次报工）
    public function finish_work(){
        $input = input('post.');
        if(isset_full($input, 'schedule_id') && isset_full($input, 'actual_qty')){
            $schedule = ProductionSchedule::get($input['schedule_id']);
            if($schedule && ($schedule->status == 0 || $schedule->status == 1)){
                //验证数据
                if($input['actual_qty'] <= 0){
                    return json(['state' => 'error', 'info' => '实际产量必须大于0!']);
                }

                $good_qty = isset($input['good_qty']) ? $input['good_qty'] : $input['actual_qty'];
                $defect_qty = $input['actual_qty'] - $good_qty;

                //检查是否是完全完工（本次报工后是否达到计划产量）
                $current_actual_qty = $schedule->actual_qty ? $schedule->actual_qty : 0;
                $new_total_actual_qty = $current_actual_qty + $input['actual_qty'];
                $is_complete = $new_total_actual_qty >= $schedule->plan_qty;
                
                //开启事务
                db()->startTrans();
                try {
                    // 如果是从待生产状态直接完工，需要先创建开工记录
                    if($schedule->status == 0){
                        // 自动创建开工记录
                        $start_time = isset($input['actual_start_time']) ? $input['actual_start_time'] : $schedule->plan_start_time;
                        $start_report_data = [
                            'schedule_id' => $input['schedule_id'],
                            'report_type' => 1, // 开工
                            'report_time' => $start_time,
                            'operator_id' => Session('is_user_id'),
                            'data' => '自动开工记录（完工时创建）',
                            'merchant' => Session('is_merchant_id'),
                            'createtime' => time()
                        ];
                        ProductionReportModel::create($start_report_data);

                        // 更新排产开始时间
                        $schedule->actual_start_time = $start_time;
                    }

                    //更新排产状态和累计数据
                    if($is_complete){
                        $schedule->status = 2; //已完成
                        $schedule->actual_end_time = isset($input['actual_end_time']) ? $input['actual_end_time'] : date('Y-m-d H:i:s');
                    } else {
                        $schedule->status = 1; //继续生产中
                    }

                    //累计产量数据
                    $schedule->actual_qty = $new_total_actual_qty;
                    $schedule->good_qty = ($schedule->good_qty ? $schedule->good_qty : 0) + $good_qty;
                    $schedule->defect_qty = ($schedule->defect_qty ? $schedule->defect_qty : 0) + $defect_qty;
                    $schedule->save();
                    
                    //记录报工
                    $report_type = $is_complete ? 2 : 7; // 2=完工, 7=阶段报工
                    $report_desc = $is_complete ? '完工报告' : '阶段报工';

                    $report_data = [
                        'merchant' => Session('is_merchant_id'),
                        'schedule_id' => $input['schedule_id'],
                        'report_type' => $report_type,
                        'report_time' => date('Y-m-d H:i:s'),
                        'operator_id' => Session('is_user_id'),
                        'qty' => $input['actual_qty'],
                        'good_qty' => $good_qty,
                        'defect_qty' => $defect_qty,
                        'defect_reason' => isset($input['defect_reason']) ? $input['defect_reason'] : '',
                        'machine_status' => isset($input['machine_status']) ? $input['machine_status'] : 1,
                        'process_temp_1' => isset($input['process_temp_1']) ? $input['process_temp_1'] : null,
                        'process_temp_2' => isset($input['process_temp_2']) ? $input['process_temp_2'] : null,
                        'process_pressure' => isset($input['process_pressure']) ? $input['process_pressure'] : null,
                        'cycle_time_actual' => isset($input['cycle_time_actual']) ? $input['cycle_time_actual'] : null,
                        'data' => isset($input['data']) ? $input['data'] : ($report_desc . ' - 累计产量: ' . $new_total_actual_qty),
                        'createtime' => time()
                    ];
                    
                    ProductionReportModel::create($report_data);
                    
                    //更新生产订单状态
                    $order = ProductionOrder::get($schedule->production_order_id);
                    if($order){
                        $order->updateStatus();
                    }
                    
                    //更新模具射胶次数
                    if($order && $order->mold_id){
                        $mold = db('mold')->where(['id' => $order->mold_id])->find();
                        if($mold){
                            $shots = ceil($input['actual_qty'] / $mold['cavity_count']);
                            db('mold')->where(['id' => $order->mold_id])
                                ->setInc('total_shots', $shots);
                        }
                    }
                    
                    //生成生产统计数据
                    $this->generateProductionSummary($schedule);
                    
                    db()->commit();
                    $log_msg = $is_complete ? '完工报告' : '阶段报工';
                    push_log($log_msg . ' - 排产ID: '.$input['schedule_id'].' 本次产量: '.$input['actual_qty'].' 累计产量: '.$new_total_actual_qty);
                    $result = ['state' => 'success', 'info' => $log_msg . '提交成功', 'is_complete' => $is_complete, 'total_qty' => $new_total_actual_qty];
                } catch (\Exception $e) {
                    db()->rollback();
                    $result = ['state' => 'error', 'info' => '完工报告失败：' . $e->getMessage()];
                }
            }else{
                $result = ['state' => 'error', 'info' => '排产状态不正确!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }

    //阶段报工（专门用于多次报工）
    public function stage_report(){
        $input = input('post.');
        if(isset_full($input, 'schedule_id') && isset_full($input, 'actual_qty')){
            $schedule = ProductionSchedule::get($input['schedule_id']);
            if($schedule && ($schedule->status == 0 || $schedule->status == 1)){ // 待生产或生产中都可以阶段报工
                //验证数据
                if($input['actual_qty'] <= 0){
                    return json(['state' => 'error', 'info' => '实际产量必须大于0!']);
                }

                $good_qty = isset($input['good_qty']) ? $input['good_qty'] : $input['actual_qty'];
                $defect_qty = $input['actual_qty'] - $good_qty;

                //开启事务
                db()->startTrans();
                try {
                    // 如果是从待生产状态开始阶段报工，需要先创建开工记录
                    if($schedule->status == 0){
                        // 自动创建开工记录
                        $start_time = isset($input['actual_start_time']) ? $input['actual_start_time'] : $schedule->plan_start_time;
                        $start_report_data = [
                            'schedule_id' => $input['schedule_id'],
                            'report_type' => 1, // 开工
                            'report_time' => $start_time,
                            'operator_id' => Session('is_user_id'),
                            'data' => '自动开工记录（阶段报工时创建）',
                            'merchant' => Session('is_merchant_id'),
                            'createtime' => time()
                        ];
                        ProductionReportModel::create($start_report_data);

                        // 更新排产开始时间和状态
                        $schedule->actual_start_time = $start_time;
                        $schedule->status = 1; // 设为生产中
                    }

                    //累计产量数据
                    $current_actual_qty = $schedule->actual_qty ? $schedule->actual_qty : 0;
                    $new_total_actual_qty = $current_actual_qty + $input['actual_qty'];

                    $schedule->actual_qty = $new_total_actual_qty;
                    $schedule->good_qty = ($schedule->good_qty ? $schedule->good_qty : 0) + $good_qty;
                    $schedule->defect_qty = ($schedule->defect_qty ? $schedule->defect_qty : 0) + $defect_qty;
                    $schedule->save();

                    //记录阶段报工
                    $report_data = [
                        'merchant' => Session('is_merchant_id'),
                        'schedule_id' => $input['schedule_id'],
                        'report_type' => 7, //阶段报工
                        'report_time' => date('Y-m-d H:i:s'),
                        'operator_id' => Session('is_user_id'),
                        'qty' => $input['actual_qty'],
                        'good_qty' => $good_qty,
                        'defect_qty' => $defect_qty,
                        'data' => '阶段报工 - 本次产量: '.$input['actual_qty'].' 累计产量: '.$new_total_actual_qty,
                        'createtime' => time()
                    ];

                    ProductionReportModel::create($report_data);

                    db()->commit();
                    push_log('阶段报工 - 排产ID: '.$input['schedule_id'].' 本次产量: '.$input['actual_qty'].' 累计产量: '.$new_total_actual_qty);
                    $result = ['state' => 'success', 'info' => '阶段报工提交成功', 'total_qty' => $new_total_actual_qty];
                } catch (\Exception $e) {
                    db()->rollback();
                    $result = ['state' => 'error', 'info' => '阶段报工失败：' . $e->getMessage()];
                }
            } else {
                $status_text = ['待生产', '生产中', '已完成', '暂停', '取消'];
                $current_status = isset($status_text[$schedule->status]) ? $status_text[$schedule->status] : '未知';
                $result = ['state' => 'error', 'info' => '排产状态不正确! 当前状态: ' . $current_status . '，只能对待生产或生产中的排产进行阶段报工'];
            }
        } else {
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }

        return json($result);
    }
    
    //暂停生产
    public function pause_work(){
        $input = input('post.');
        if(isset_full($input, 'schedule_id')){
            $schedule = ProductionSchedule::find($input['schedule_id']);
            if($schedule && $schedule->status == 1){
                //更新排产状态
                $schedule->status = 3; //暂停
                $schedule->save();
                
                //记录报工
                $report_data = [
                    'merchant' => Session('is_merchant_id'),
                    'schedule_id' => $input['schedule_id'],
                    'report_type' => 3, //暂停
                    'report_time' => date('Y-m-d H:i:s'),
                    'operator_id' => Session('is_user_id'),
                    'data' => isset($input['reason']) ? $input['reason'] : '',
                    'createtime' => time()
                ];
                
                ProductionReportModel::create($report_data);

                push_log('暂停生产 - 排产ID: '.$input['schedule_id']);
                $result = ['state' => 'success'];
            }else{
                $result = ['state' => 'error', 'info' => '排产状态不正确!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //恢复生产
    public function resume_work(){
        $input = input('post.');
        if(isset_full($input, 'schedule_id')){
            $schedule = ProductionSchedule::get($input['schedule_id']);
            if($schedule && $schedule->status == 3){
                //更新排产状态
                $schedule->status = 1; //生产中
                $schedule->save();
                
                //记录报工
                $report_data = [
                    'merchant' => Session('is_merchant_id'),
                    'schedule_id' => $input['schedule_id'],
                    'report_type' => 4, //恢复
                    'report_time' => date('Y-m-d H:i:s'),
                    'operator_id' => Session('is_user_id'),
                    'data' => isset($input['data']) ? $input['data'] : '',
                    'createtime' => time()
                ];
                
                ProductionReportModel::create($report_data);

                push_log('恢复生产 - 排产ID: '.$input['schedule_id']);
                $result = ['state' => 'success'];
            }else{
                $result = ['state' => 'error', 'info' => '排产状态不正确!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //异常报告
    public function report_exception(){
        $input = input('post.');
        if(isset_full($input, 'schedule_id') && isset_full($input, 'exception_desc')){
            //开启事务
            db()->startTrans();
            try {
                //记录异常报工
                $report_data = [
                    'merchant' => Session('is_merchant_id'),
                    'schedule_id' => $input['schedule_id'],
                    'report_type' => 5, //异常
                    'report_time' => date('Y-m-d H:i:s'),
                    'operator_id' => Session('is_user_id'),
                    'machine_status' => isset($input['machine_status']) ? $input['machine_status'] : 2,
                    'data' => $input['exception_desc'],
                    'createtime' => time()
                ];

                ProductionReportModel::create($report_data);

                //记录生产异常
                $exception_data = [
                    'merchant' => Session('is_merchant_id'),
                    'schedule_id' => $input['schedule_id'],
                    'exception_type' => isset($input['exception_type']) ? $input['exception_type'] : 6,
                    'exception_desc' => $input['exception_desc'],
                    'occurrence_time' => date('Y-m-d H:i:s'),
                    'reporter_id' => Session('is_user_id'),
                    'severity' => isset($input['severity']) ? $input['severity'] : 2,
                    'impact_desc' => isset($input['impact_desc']) ? $input['impact_desc'] : '',
                    'temp_solution' => isset($input['temp_solution']) ? $input['temp_solution'] : '',
                    'status' => 0,
                    'createtime' => time()
                ];

                // 暂时注释异常表插入，等待创建异常表
                // db('production_exception')->insert($exception_data);

                db()->commit();
                push_log('异常报告 - 排产ID: '.$input['schedule_id']);
                $result = ['state' => 'success'];
            } catch (\Exception $e) {
                db()->rollback();
                $result = ['state' => 'error', 'info' => '异常报告失败：' . $e->getMessage()];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }

        return json($result);
    }

    //质量检验报工
    public function quality_inspection(){
        $input = input('post.');
        if(isset_full($input, 'schedule_id') && isset_full($input, 'inspection_type')){
            //开启事务
            db()->startTrans();
            try {
                //记录质量检验
                $inspection_data = [
                    'merchant' => Session('is_merchant_id'),
                    'schedule_id' => $input['schedule_id'],
                    'inspection_type' => $input['inspection_type'],
                    'inspection_time' => date('Y-m-d H:i:s'),
                    'inspector_id' => Session('is_user_id'),
                    'sample_qty' => isset($input['sample_qty']) ? $input['sample_qty'] : 0,
                    'qualified_qty' => isset($input['qualified_qty']) ? $input['qualified_qty'] : 0,
                    'defect_qty' => isset($input['defect_qty']) ? $input['defect_qty'] : 0,
                    'qualified_rate' => 0,
                    // 'defect_rate' => 0, // 字段可能不存在，暂时注释
                    'result' => isset($input['result']) ? $input['result'] : 1,
                    'data' => isset($input['data']) ? $input['data'] : '',
                    'createtime' => time()
                ];

                //计算合格率和不良率
                if($inspection_data['sample_qty'] > 0){
                    $inspection_data['qualified_rate'] = ($inspection_data['qualified_qty'] / $inspection_data['sample_qty']) * 100;
                    // $inspection_data['defect_rate'] = ($inspection_data['defect_qty'] / $inspection_data['sample_qty']) * 100; // 字段可能不存在，暂时注释
                }

                $inspection_id = db('quality_inspection')->insertGetId($inspection_data);

                //记录检验明细
                if(isset($input['inspection_details']) && is_array($input['inspection_details'])){
                    foreach($input['inspection_details'] as $detail){
                        $detail['inspection_id'] = $inspection_id;
                        db('quality_inspection_detail')->insert($detail);
                    }
                }

                //记录质检报工
                $report_data = [
                    'merchant' => Session('is_merchant_id'),
                    'schedule_id' => $input['schedule_id'],
                    'report_type' => 6, //质检
                    'report_time' => date('Y-m-d H:i:s'),
                    'operator_id' => Session('is_user_id'),
                    'sample_qty' => $inspection_data['sample_qty'],
                    'qualified_qty' => $inspection_data['qualified_qty'],
                    'defect_qty' => $inspection_data['defect_qty'],
                    'inspector_id' => Session('is_user_id'),
                    'inspection_time' => date('Y-m-d H:i:s'),
                    'data' => $inspection_data['data'],
                    'createtime' => time()
                ];

                ProductionReportModel::create($report_data);

                //如果有不良品，记录不良品信息
                if($inspection_data['defect_qty'] > 0){
                    $defect_data = [
                        'merchant' => Session('is_merchant_id'),
                        'schedule_id' => $input['schedule_id'],
                        'defect_type' => isset($input['defect_type']) ? $input['defect_type'] : '质检不良',
                        'defect_reason' => isset($input['defect_reason']) ? $input['defect_reason'] : '',
                        'defect_qty' => $inspection_data['defect_qty'],
                        // 'defect_rate' => $inspection_data['defect_rate'], // 字段可能不存在，暂时注释
                        'responsibility' => isset($input['responsibility']) ? $input['responsibility'] : '',
                        'status' => 0,
                        'createtime' => time()
                    ];

                    db('defect_record')->insert($defect_data);
                }

                db()->commit();
                push_log('质量检验 - 排产ID: '.$input['schedule_id'].' 类型: '.$input['inspection_type']);
                $result = ['state' => 'success', 'data' => ['inspection_id' => $inspection_id]];
            } catch (\Exception $e) {
                db()->rollback();
                $result = ['state' => 'error', 'info' => '质量检验失败：' . $e->getMessage()];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }

        return json($result);
    }
    
    //获取报工记录
    public function get_reports(){
        $input = input('post.');
        $where = auth('production_report', []);
        
        //筛选条件
        if(isset_full($input, 'schedule_id')){
            $where[] = ['schedule_id', '=', $input['schedule_id']];
        }
        if(isset_full($input, 'operator_id')){
            $where[] = ['operator_id', '=', $input['operator_id']];
        }
        if(isset_full($input, 'report_type')){
            $where[] = ['report_type', '=', $input['report_type']];
        }
        if(isset_full($input, 'start_date')){
            $where[] = ['report_time', '>=', $input['start_date'].' 00:00:00'];
        }
        if(isset_full($input, 'end_date')){
            $where[] = ['report_time', '<=', $input['end_date'].' 23:59:59'];
        }
        
        $list = ProductionReportModel::with(['scheduleinfo', 'operatorinfo'])
            ->where($where)
            ->order('report_time desc')
            ->paginate(input('limit', 15))
            ->toArray();
            
        return json(['code' => 0, 'msg' => '', 'count' => $list['total'], 'data' => $list['data']]);
    }
    
    //获取质量标准
    public function get_quality_standard(){
        $input = input('post.');
        if(isset_full($input, 'goods_id')){
            $standard = db('quality_standard')
                ->where([
                    'merchant' => Session('is_merchant_id'),
                    'goods_id' => $input['goods_id'],
                    'status' => 1
                ])
                ->find();

            if($standard){
                $details = db('quality_standard_detail')
                    ->where(['standard_id' => $standard['id']])
                    ->order('sort asc')
                    ->select();

                $standard['details'] = $details;
                return json(['state' => 'success', 'data' => $standard]);
            }else{
                return json(['state' => 'error', 'info' => '未找到质量标准']);
            }
        }else{
            return json(['state' => 'error', 'info' => '传入参数不完整']);
        }
    }

    //获取异常列表
    public function get_exceptions(){
        $input = input('post.');
        $where = auth('production_exception', []);

        //筛选条件
        if(isset_full($input, 'schedule_id')){
            $where[] = ['schedule_id', '=', $input['schedule_id']];
        }
        if(isset_full($input, 'exception_type')){
            $where[] = ['exception_type', '=', $input['exception_type']];
        }
        if(isset_full($input, 'status')){
            $where[] = ['status', '=', $input['status']];
        }
        if(isset_full($input, 'start_date')){
            $where[] = ['occurrence_time', '>=', $input['start_date'].' 00:00:00'];
        }
        if(isset_full($input, 'end_date')){
            $where[] = ['occurrence_time', '<=', $input['end_date'].' 23:59:59'];
        }

        // 暂时返回空数据，等待创建异常表
        /*
        $list = db('production_exception')
            ->alias('pe')
            ->join('production_schedule ps', 'pe.schedule_id = ps.id', 'left')
            ->join('production_order po', 'ps.production_order_id = po.id', 'left')
            ->join('user u1', 'pe.reporter_id = u1.id', 'left')
            ->join('user u2', 'pe.handler_id = u2.id', 'left')
            ->where($where)
            ->field('pe.*,po.order_no,u1.name as reporter_name,u2.name as handler_name')
            ->order('pe.occurrence_time desc')
            ->paginate(input('limit', 15))
            ->toArray();

        return json(['code' => 0, 'msg' => '', 'count' => $list['total'], 'data' => $list['data']]);
        */

        return json(['code' => 0, 'msg' => '', 'count' => 0, 'data' => []]);
    }

    //处理异常
    public function handle_exception(){
        $input = input('post.');
        if(isset_full($input, 'exception_id')){
            // 暂时注释，等待创建异常表
            // $exception = db('production_exception')->where(['id' => $input['exception_id']])->find();
            $exception = null;
            if($exception){
                $update_data = [
                    'handler_id' => Session('is_user_id'),
                    'status' => isset($input['status']) ? $input['status'] : 1
                ];

                if($update_data['status'] == 1 && !$exception['handle_start_time']){
                    $update_data['handle_start_time'] = date('Y-m-d H:i:s');
                }

                if($update_data['status'] == 2){
                    $update_data['handle_end_time'] = date('Y-m-d H:i:s');
                }

                if(isset($input['root_cause'])){
                    $update_data['root_cause'] = $input['root_cause'];
                }

                if(isset($input['permanent_solution'])){
                    $update_data['permanent_solution'] = $input['permanent_solution'];
                }

                // 暂时注释，等待创建异常表
                // db('production_exception')->where(['id' => $input['exception_id']])->update($update_data);

                push_log('处理生产异常[ ID:'.$input['exception_id'].' ]');
                return json(['state' => 'success']);
            }else{
                return json(['state' => 'error', 'info' => '异常记录不存在']);
            }
        }else{
            return json(['state' => 'error', 'info' => '传入参数不完整']);
        }
    }

    //获取不良品记录
    public function get_defects(){
        $input = input('post.');
        $where = auth('defect_record', []);

        //筛选条件
        if(isset_full($input, 'schedule_id')){
            $where[] = ['schedule_id', '=', $input['schedule_id']];
        }
        if(isset_full($input, 'defect_type')){
            $where[] = ['defect_type', 'like', '%'.$input['defect_type'].'%'];
        }
        if(isset_full($input, 'status')){
            $where[] = ['status', '=', $input['status']];
        }

        $list = db('defect_record')
            ->alias('dr')
            ->join('production_schedule ps', 'dr.schedule_id = ps.id', 'left')
            ->join('production_order po', 'ps.production_order_id = po.id', 'left')
            ->join('user u', 'dr.handler_id = u.id', 'left')
            ->where($where)
            ->field('dr.*,po.order_no,u.name as handler_name')
            ->order('dr.createtime desc')
            ->paginate(input('limit', 15))
            ->toArray();

        return json(['code' => 0, 'msg' => '', 'count' => $list['total'], 'data' => $list['data']]);
    }

    //移动端报工接口
    public function mobile_report(){
        $input = input('post.');
        $action = isset($input['action']) ? $input['action'] : '';

        switch($action){
            case 'start':
                return $this->start_work();
            case 'finish':
                return $this->finish_work();
            case 'stage':
                return $this->stage_report();
            case 'pause':
                return $this->pause_work();
            case 'resume':
                return $this->resume_work();
            case 'exception':
                return $this->report_exception();
            case 'quality':
                return $this->quality_inspection();
            default:
                return json(['state' => 'error', 'info' => '无效的操作类型']);
        }
    }

    //生成生产统计数据
    private function generateProductionSummary($schedule){
        $summary_data = [
            'merchant' => $schedule->merchant,
            'summary_date' => $schedule->schedule_date,
            'machine_id' => $schedule->machine_id,
            'shift_id' => $schedule->shift_id,
            'goods_id' => $schedule->orderinfo->goods_id,
            'operator_id' => $schedule->operator_id,
            'plan_qty' => $schedule->plan_qty,
            'actual_qty' => $schedule->actual_qty,
            'good_qty' => $schedule->good_qty,
            'defect_qty' => $schedule->defect_qty,
            'completion_rate' => $schedule->plan_qty > 0 ? ($schedule->actual_qty / $schedule->plan_qty * 100) : 0,
            'qualified_rate' => $schedule->actual_qty > 0 ? ($schedule->good_qty / $schedule->actual_qty * 100) : 0,
            // 'defect_rate' => $schedule->actual_qty > 0 ? ($schedule->defect_qty / $schedule->actual_qty * 100) : 0, // 字段不存在，暂时注释
            'createtime' => time()
        ];

        //计算工作时长
        if($schedule->actual_start_time && $schedule->actual_end_time){
            $working_hours = (strtotime($schedule->actual_end_time) - strtotime($schedule->actual_start_time)) / 3600;
            // $summary_data['working_hours'] = $working_hours; // 如果字段不存在，注释这行

            //计算效率 - 如果字段不存在则注释
            $plan_hours = (strtotime($schedule->plan_end_time) - strtotime($schedule->plan_start_time)) / 3600;
            if($plan_hours > 0){
                // $summary_data['efficiency'] = ($plan_hours / $working_hours) * 100; // 如果字段不存在，取消注释这行
            }

            //计算OEE (设备综合效率) - 字段不存在，暂时注释
            /*
            $availability = $working_hours / 8; //可用率 (假设标准8小时)
            $performance = $summary_data['completion_rate'] / 100; //性能率
            $quality = $summary_data['qualified_rate'] / 100; //质量率
            $summary_data['oee'] = $availability * $performance * $quality * 100;
            */
        }

        //计算停机时长
        $downtime = $this->calculateDowntime($schedule);
        // $summary_data['downtime_hours'] = $downtime; // 如果字段不存在，注释这行

        //计算异常次数（字段不存在，暂时注释）
        // $exception_count = db('production_exception')
        //     ->where(['schedule_id' => $schedule->id])
        //     ->count();
        // $summary_data['exception_count'] = 0; // 字段不存在，暂时注释

        //检查是否已存在统计记录
        $existing = db('production_summary')
            ->where([
                'summary_date' => $summary_data['summary_date'],
                'machine_id' => $summary_data['machine_id'],
                'shift_id' => $summary_data['shift_id'],
                'goods_id' => $summary_data['goods_id']
            ])
            ->find();

        if($existing){
            //更新现有记录
            $new_plan_qty = $existing['plan_qty'] + $summary_data['plan_qty'];
            $new_actual_qty = $existing['actual_qty'] + $summary_data['actual_qty'];
            $new_good_qty = $existing['good_qty'] + $summary_data['good_qty'];
            $new_defect_qty = $existing['defect_qty'] + $summary_data['defect_qty'];
            // $new_working_hours = $existing['working_hours'] + $summary_data['working_hours']; // 如果字段不存在，注释这行
            $new_working_hours = 0; // 临时设为0

            db('production_summary')
                ->where(['id' => $existing['id']])
                ->update([
                    'plan_qty' => $new_plan_qty,
                    'actual_qty' => $new_actual_qty,
                    'good_qty' => $new_good_qty,
                    'defect_qty' => $new_defect_qty,
                    'completion_rate' => $new_plan_qty > 0 ? ($new_actual_qty / $new_plan_qty * 100) : 0,
                    'qualified_rate' => $new_actual_qty > 0 ? ($new_good_qty / $new_actual_qty * 100) : 0,
                    // 'defect_rate' => $new_actual_qty > 0 ? ($new_defect_qty / $new_actual_qty * 100) : 0, // 字段不存在，暂时注释
                    // 'working_hours' => $new_working_hours, // 如果字段不存在，注释这行
                    // 'downtime_hours' => $existing['downtime_hours'] + $summary_data['downtime_hours'] // 如果字段不存在，注释这行
                    // 'exception_count' => $existing['exception_count'] + $summary_data['exception_count'] // 字段不存在，暂时注释
                ]);
        }else{
            //创建新记录
            db('production_summary')->insert($summary_data);
        }
    }

    //计算停机时长
    private function calculateDowntime($schedule){
        //获取暂停和异常报工记录
        $reports = db('production_report')
            ->where([
                'schedule_id' => $schedule->id,
                'report_type' => ['in', [3, 4, 5]] //暂停、恢复、异常
            ])
            ->order('report_time asc')
            ->select();

        $downtime = 0;
        $pause_start = null;

        foreach($reports as $report){
            if($report['report_type'] == 3){ //暂停
                $pause_start = strtotime($report['report_time']);
            } elseif($report['report_type'] == 4 && $pause_start){ //恢复
                $downtime += strtotime($report['report_time']) - $pause_start;
                $pause_start = null;
            }
        }

        //如果最后是暂停状态，计算到当前时间
        if($pause_start){
            $end_time = $schedule->actual_end_time ? strtotime($schedule->actual_end_time) : time();
            $downtime += $end_time - $pause_start;
        }

        return round($downtime / 3600, 2); //转换为小时
    }
}
