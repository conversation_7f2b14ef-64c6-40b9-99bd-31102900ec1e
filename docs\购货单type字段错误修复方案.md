# 购货单 type 字段错误修复方案

## 问题描述

**错误信息：**
```
SQLSTATE[HY000]: General error: 1364 Field 'type' doesn't have a default value
```

**错误位置：**
- URL: `http://tc.xinqiyu.cn:8842/index/purchase/set`
- 文件: `application/index/controller/Purchase.php` 第 44 行
- 代码: `$create_info=Purchaseclass::create(syn_sql($input,'purchaseclass'));`

## 问题分析

### 1. 根本原因
- MySQL 服务器运行在严格模式下（`sql_mode` 包含 `NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION`）
- `is_purchaseclass` 表的 `type` 字段定义为 `NOT NULL` 但没有默认值
- 应用程序在创建购货单时没有为 `type` 字段提供值

### 2. 字段作用
根据模型代码分析，`type` 字段表示审核状态：
- `0` = 未审核
- `1` = 已审核

### 3. 业务逻辑
- 新创建的购货单应该默认为未审核状态（`type = 0`）
- 只有经过审核流程后才变为已审核状态（`type = 1`）

## 解决方案

### 方案一：数据库层面修复（推荐）

**1. 修改数据库字段定义**
```sql
ALTER TABLE `is_purchaseclass` 
MODIFY COLUMN `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '审核状态[0:未审核|1:已审核]';
```

**2. 更新现有 NULL 数据**
```sql
UPDATE `is_purchaseclass` SET `type` = 0 WHERE `type` IS NULL;
```

### 方案二：应用程序层面修复

**修改控制器代码**
在 `application/index/controller/Purchase.php` 第 41 行添加：
```php
$input['type']=0;//设置默认审核状态为未审核
```

## 修复步骤

### 1. 执行数据库修复脚本
```bash
mysql -h192.168.1.165 -uroot -p1234567 ZS_ERP < fix_purchaseclass_type_field.sql
```

### 2. 修改应用程序代码
已修改 `application/index/controller/Purchase.php`：
- 在新增购货单时自动设置 `type = 0`
- 确保所有新创建的购货单都有正确的审核状态

### 3. 验证修复结果
1. 访问购货单新增页面
2. 填写必要信息并保存
3. 确认不再出现 `Field 'type' doesn't have a default value` 错误

## 预防措施

### 1. 数据库设计规范
- 所有 `NOT NULL` 字段都应该有合理的默认值
- 状态字段应该明确定义各状态的含义
- 使用注释说明字段用途和取值范围

### 2. 代码开发规范
- 在模型中明确定义字段的默认值
- 在控制器中为关键字段设置默认值
- 添加字段验证规则

### 3. 测试验证
- 在开发环境中启用 MySQL 严格模式
- 测试所有 CRUD 操作
- 验证字段约束和默认值

## 相关文件

### 修复文件
- `fix_purchaseclass_type_field.php` - PHP 修复脚本
- `fix_purchaseclass_type_field.sql` - SQL 修复脚本
- `application/index/controller/Purchase.php` - 已修改的控制器

### 相关模型和验证
- `application/index/model/Purchaseclass.php` - 购货单模型
- `application/index/validate/Purchaseclass.php` - 购货单验证规则

## 测试验证

### 1. 功能测试
- [ ] 新增购货单正常保存
- [ ] 审核状态正确显示为"未审核"
- [ ] 审核功能正常工作
- [ ] 反审核功能正常工作

### 2. 数据完整性测试
- [ ] 所有现有记录的 type 字段都有值
- [ ] 新记录自动设置 type = 0
- [ ] 数据库约束正常工作

## 总结

此问题是典型的数据库字段约束与应用程序逻辑不匹配导致的错误。通过同时修复数据库层面和应用程序层面，确保了：

1. **数据库层面**：字段有合理的默认值，符合业务逻辑
2. **应用程序层面**：明确设置字段值，避免依赖数据库默认值
3. **业务逻辑**：新购货单默认为未审核状态，符合业务流程

这种双重保障的方式可以有效防止类似问题再次发生。
