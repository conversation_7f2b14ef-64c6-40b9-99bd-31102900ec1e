<?php
namespace app\index\controller;
use app\index\controller\Acl;
use app\index\model\InjectionMachine;

class Machine extends Acl {
    //注塑机管理模块
    
    //主视图
    public function main(){
        return $this->fetch();
    }

    //表单页面
    public function form(){
        $id = input('id', 0);
        $machine = [];
        if($id > 0){
            $machine = InjectionMachine::get($id);
            if(!$machine){
                $this->error('设备不存在!');
            }
            $machine = $machine->toArray();
        }
        $this->assign('machine', $machine);
        return $this->fetch();
    }

    //详情页面
    public function info(){
        $id = input('id', 0);
        if($id <= 0){
            $this->error('参数错误!');
        }

        $machine = InjectionMachine::get($id);
        if(!$machine){
            $this->error('设备不存在!');
        }

        $this->assign('machine', $machine);
        return $this->fetch();
    }
    
    //获取注塑机列表
    public function get_list(){
        $input = input('post.');
        $where = auth('injection_machine', []);
        
        //搜索条件
        if(isset_full($input, 'name')){
            $where[] = ['name', 'like', '%'.$input['name'].'%'];
        }
        if(isset_full($input, 'code')){
            $where[] = ['code', 'like', '%'.$input['code'].'%'];
        }
        if(isset_full($input, 'status')){
            $where[] = ['status', '=', $input['status']];
        }
        if(isset_full($input, 'workshop')){
            $where[] = ['workshop', 'like', '%'.$input['workshop'].'%'];
        }
        
        $list = InjectionMachine::where($where)
            ->order('id desc')
            ->paginate(input('limit', 15))
            ->toArray();
            
        return json(['code' => 0, 'msg' => '', 'count' => $list['total'], 'data' => $list['data']]);
    }
    
    //新增|更新注塑机
    public function set(){
        $input = input('post.');
        
        if(isset($input['id'])){
            if(empty($input['id'])){
                //新增
                $input['merchant'] = Session('is_merchant_id');
                $input['createtime'] = time();
                $vali = $this->validate($input, 'InjectionMachine');
                if($vali === true){
                    $create_info = InjectionMachine::create($input);
                    push_log('新增注塑机[ '.$create_info['name'].' ]');
                    $result = ['state' => 'success'];
                }else{
                    $result = ['state' => 'error', 'info' => $vali];
                }
            }else{
                //更新
                $vali = $this->validate($input, 'InjectionMachine');
                if($vali === true){
                    $update_info = InjectionMachine::update($input);
                    push_log('更新注塑机[ '.$update_info['name'].' ]');
                    $result = ['state' => 'success'];
                }else{
                    $result = ['state' => 'error', 'info' => $vali];
                }
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //删除注塑机
    public function del(){
        $input = input('post.');

        // 支持批量删除和单个删除
        $ids = [];
        if(isset_full($input, 'arr') && is_array($input['arr'])){
            // 批量删除
            $ids = $input['arr'];
        } elseif(isset_full($input, 'id')){
            // 单个删除
            $ids = [$input['id']];
        } else {
            return json(['state' => 'error', 'info' => '传入参数不完整!']);
        }

        $success_count = 0;
        $error_messages = [];

        foreach($ids as $id){
            $machine = InjectionMachine::find($id);
            if($machine){
                //检查是否有关联的生产排产
                $schedule_count = db('production_schedule')->where(['machine_id' => $id])->count();
                if($schedule_count > 0){
                    $error_messages[] = '设备['.$machine['name'].']已有生产排产记录，无法删除!';
                    continue;
                }

                $machine->delete();
                push_log('删除注塑机[ '.$machine['name'].' ]');
                $success_count++;
            } else {
                $error_messages[] = 'ID为'.$id.'的设备不存在!';
            }
        }

        if($success_count > 0){
            $message = '成功删除'.$success_count.'条记录';
            if(!empty($error_messages)){
                $message .= '，但有'.count($error_messages).'条记录删除失败：'.implode('；', $error_messages);
            }
            return json(['state' => 'success', 'info' => $message]);
        } else {
            return json(['state' => 'error', 'info' => '删除失败：'.implode('；', $error_messages)]);
        }
    }
    
    //获取设备详情
    public function get_info(){
        $input = input('post.');
        if(isset_full($input, 'id')){
            $machine = InjectionMachine::find($input['id']);
            if($machine){
                $result = ['state' => 'success', 'data' => $machine];
            }else{
                $result = ['state' => 'error', 'info' => '设备不存在!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //更新设备状态
    public function update_status(){
        $input = input('post.');
        if(isset_full($input, 'id') && isset_full($input, 'status')){
            $machine = InjectionMachine::get($input['id']);
            if($machine){
                $machine->status = $input['status'];
                $machine->save();
                
                $status_text = ['0' => '停用', '1' => '正常', '2' => '维修'];
                push_log('更新注塑机[ '.$machine['name'].' ]状态为: '.$status_text[$input['status']]);
                $result = ['state' => 'success'];
            }else{
                $result = ['state' => 'error', 'info' => '设备不存在!'];
            }
        }else{
            $result = ['state' => 'error', 'info' => '传入参数不完整!'];
        }
        
        return json($result);
    }
    
    //获取可用设备列表(用于下拉选择)
    public function get_available(){
        $where = auth('injection_machine', []);
        $where[] = ['status', '=', 1]; //只获取正常状态的设备
        
        $list = InjectionMachine::where($where)
            ->field('id,name,code,tonnage,workshop')
            ->order('workshop asc,name asc')
            ->select()
            ->toArray();
            
        return json(['state' => 'success', 'data' => $list]);
    }
    

    
    //设备效率统计
    public function efficiency(){
        $input = input('post.');
        $start_date = isset($input['start_date']) ? $input['start_date'] : date('Y-m-01');
        $end_date = isset($input['end_date']) ? $input['end_date'] : date('Y-m-d');

        $where = auth('production_summary', []);

        $list = db('production_summary')
            ->alias('ps')
            ->join('injection_machine im', 'ps.machine_id = im.id', 'left')
            ->join('goods g', 'ps.goods_id = g.id', 'left')
            ->where($where)
            ->where('summary_date', 'between', [$start_date, $end_date]);

        if(isset_full($input, 'machine_id')){
            $list = $list->where('machine_id', '=', $input['machine_id']);
        }

        $list = $list->field('
                ps.summary_date,
                g.name as goods_name,
                ps.plan_qty as planned_quantity,
                ps.actual_qty as actual_quantity,
                ps.working_hours,
                ps.downtime_hours,
                ps.efficiency,
                ps.completion_rate,
                ps.qualified_rate,
                im.name as machine_name
            ')
            ->order('ps.summary_date desc')
            ->paginate(input('limit', 15))
            ->toArray();

        return json(['code' => 0, 'msg' => '', 'count' => $list['total'], 'data' => $list['data']]);
    }

    //设备维护页面
    public function maintenance(){
        $id = input('id', 0);
        if($id <= 0){
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        $machine = InjectionMachine::get($id);
        if(!$machine){
            return json(['code' => 1, 'msg' => '设备不存在']);
        }

        $this->assign('machine', $machine);
        return $this->fetch();
    }

    //获取维护记录列表
    public function get_maintenance_list(){
        $input = input('post.');
        $machine_id = isset($input['machine_id']) ? $input['machine_id'] : 0;

        if($machine_id <= 0){
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        $where = auth('machine_maintenance', []);

        $list = db('machine_maintenance')
            ->alias('mm')
            ->join('user u', 'mm.maintainer_id = u.id', 'left')
            ->where($where)
            ->where('mm.machine_id', '=', $machine_id)
            ->field('
                mm.*,
                u.name as maintainer_name
            ')
            ->order('mm.maintenance_date desc')
            ->paginate(input('limit', 15))
            ->toArray();

        return json(['code' => 0, 'msg' => '', 'count' => $list['total'], 'data' => $list['data']]);
    }

    //保存维护记录
    public function save_maintenance(){
        $input = input('post.');

        if(!isset_full($input, ['machine_id', 'maintenance_type', 'maintenance_date', 'maintenance_content'])){
            return json(['code' => 1, 'msg' => '参数不完整']);
        }

        $data = [
            'merchant' => session('admin_user.merchant'),
            'machine_id' => $input['machine_id'],
            'maintenance_type' => $input['maintenance_type'],
            'maintenance_date' => $input['maintenance_date'],
            'start_time' => $input['maintenance_date'] . ' ' . date('H:i:s'),
            'end_time' => isset($input['status']) && $input['status'] == 2 ? date('Y-m-d H:i:s') : null,
            'maintainer_id' => session('admin_user.id'),
            'maintenance_content' => $input['maintenance_content'],
            'fault_description' => isset($input['fault_description']) ? $input['fault_description'] : '',
            'cost' => isset($input['cost']) ? $input['cost'] : 0,
            'status' => isset($input['status']) ? $input['status'] : 1,
            'data' => isset($input['data']) ? $input['data'] : '',
            'createtime' => time()
        ];

        if(isset($input['id']) && $input['id'] > 0){
            // 更新
            unset($data['createtime']); // 更新时不修改创建时间
            $result = db('machine_maintenance')->where('id', $input['id'])->update($data);
            $action = '更新';
        } else {
            // 新增
            $result = db('machine_maintenance')->insert($data);
            $action = '新增';
        }

        if($result){
            push_log($action . '设备维护记录');
            return json(['code' => 0, 'msg' => $action . '成功']);
        } else {
            return json(['code' => 1, 'msg' => $action . '失败']);
        }
    }
}
